class AudioEngine {
    constructor() {
        this.audioContext = null;
        this.currentSource = null;
        this.currentBuffer = null;
        this.isPlaying = false;
        this.startTime = 0;
        this.pauseTime = 0;
        this.currentPosition = 0;
        this.duration = 0;
        this.volume = 0.8;
        this.gainNode = null;
        this.onTimeUpdate = null;
        this.onEnded = null;
        this.onError = null;
        this.onLoadStart = null;
        this.onLoadEnd = null;
        this.audioCache = new Map(); // Cache for preloaded audio buffers

        this.initializeAudioContext();
    }

    async initializeAudioContext() {
        try {
            this.audioContext = new(window.AudioContext || window.webkitAudioContext)();

            // Create gain node for volume control
            this.gainNode = this.audioContext.createGain();
            this.gainNode.connect(this.audioContext.destination);
            this.gainNode.gain.value = this.volume;

            // Resume context if suspended (required by some browsers)
            if (this.audioContext.state === 'suspended') {
                await this.audioContext.resume();
            }

            console.log('AudioContext initialized successfully');
        } catch (error) {
            console.error('Failed to initialize AudioContext:', error);
        }
    }

    async preloadAudioFile(file) {
        try {
            if (!this.audioContext) {
                await this.initializeAudioContext();
            }

            // Create a cache key
            const cacheKey = file instanceof File ? file.name + file.size : file;

            // Check if already cached
            if (this.audioCache.has(cacheKey)) {
                return this.audioCache.get(cacheKey);
            }

            let arrayBuffer;

            if (file instanceof File) {
                // Browser File object
                arrayBuffer = await file.arrayBuffer();
            } else if (typeof file === 'string') {
                // File path (for Electron)
                if (window.electronAPI) {
                    // Use Electron's file reading capabilities via IPC
                    try {
                        const fileData = await window.electronAPI.readAudioFile(file);
                        arrayBuffer = new Uint8Array(fileData).buffer;
                    } catch (error) {
                        console.error('Error reading file via IPC:', error);
                        // Fallback to file:// protocol
                        const fileUrl = file.startsWith('file://') ? file : `file://${file.replace(/\\/g, '/')}`;
                        const response = await fetch(fileUrl);
                        arrayBuffer = await response.arrayBuffer();
                    }
                } else {
                    // Fallback for web - use file:// protocol
                    const fileUrl = file.startsWith('file://') ? file : `file://${file.replace(/\\/g, '/')}`;
                    const response = await fetch(fileUrl);
                    arrayBuffer = await response.arrayBuffer();
                }
            } else {
                throw new Error('Invalid file type');
            }

            // Decode audio data
            const audioBuffer = await this.audioContext.decodeAudioData(arrayBuffer);

            // Cache the buffer
            this.audioCache.set(cacheKey, audioBuffer);

            console.log(`Audio preloaded: ${audioBuffer.duration.toFixed(2)}s, ${audioBuffer.sampleRate}Hz`);

            return audioBuffer;

        } catch (error) {
            console.error('Error preloading audio file:', error);
            throw error;
        }
    }

    async loadAudioFile(file) {
        try {
            if (!this.audioContext) {
                await this.initializeAudioContext();
            }

            // Notify load start
            if (this.onLoadStart) {
                this.onLoadStart();
            }

            // Stop current playback
            this.stop();

            // Try to get from cache first, otherwise preload
            const cacheKey = file instanceof File ? file.name + file.size : file;
            let audioBuffer = this.audioCache.get(cacheKey);

            if (!audioBuffer) {
                audioBuffer = await this.preloadAudioFile(file);
            }

            this.currentBuffer = audioBuffer;
            this.duration = this.currentBuffer.duration;
            this.currentPosition = 0;

            console.log(`Audio loaded from cache: ${this.duration.toFixed(2)}s`);

            // Notify load end
            if (this.onLoadEnd) {
                this.onLoadEnd();
            }

            return {
                duration: this.duration,
                sampleRate: this.currentBuffer.sampleRate,
                numberOfChannels: this.currentBuffer.numberOfChannels,
                buffer: this.currentBuffer
            };

        } catch (error) {
            console.error('Error loading audio file:', error);

            // Notify error
            if (this.onError) {
                this.onError(error);
            }

            throw error;
        }
    }

    play(startTime = 0) {
        if (!this.currentBuffer || !this.audioContext) {
            console.warn('No audio buffer loaded');
            return;
        }

        // Stop current playback
        this.stop();

        try {
            // Create new source
            this.currentSource = this.audioContext.createBufferSource();
            this.currentSource.buffer = this.currentBuffer;
            this.currentSource.connect(this.gainNode);

            // Set up ended callback
            this.currentSource.onended = () => {
                this.isPlaying = false;
                if (this.onEnded) {
                    this.onEnded();
                }
            };

            // Start playback
            const offset = startTime || this.currentPosition;
            this.currentSource.start(0, offset);
            this.startTime = this.audioContext.currentTime - offset;
            this.isPlaying = true;

            // Start time update loop
            this.startTimeUpdateLoop();

            console.log(`Playback started at ${offset.toFixed(2)}s`);

        } catch (error) {
            console.error('Error starting playback:', error);
            this.isPlaying = false;

            // Notify error
            if (this.onError) {
                this.onError(error);
            }
        }
    }

    pause() {
        console.log(`Pause called - isPlaying: ${this.isPlaying}, hasSource: ${!!this.currentSource}`);
        if (this.currentSource && this.isPlaying) {
            this.currentPosition = this.audioContext.currentTime - this.startTime;
            this.stop();
            console.log(`Playback paused at ${this.currentPosition.toFixed(2)}s`);
        } else {
            console.log('Pause ignored - not playing or no source');
        }
    }

    stop() {
        if (this.currentSource) {
            try {
                this.currentSource.stop();
            } catch (error) {
                // Source might already be stopped
            }
            this.currentSource = null;
        }
        this.isPlaying = false;
        this.stopTimeUpdateLoop();
    }

    seek(position) {
        const wasPlaying = this.isPlaying;
        this.currentPosition = Math.max(0, Math.min(position, this.duration));

        if (wasPlaying) {
            this.play(this.currentPosition);
        }

        console.log(`Seeked to ${this.currentPosition.toFixed(2)}s`);
    }

    setVolume(volume) {
        this.volume = Math.max(0, Math.min(1, volume));
        if (this.gainNode) {
            this.gainNode.gain.value = this.volume;
        }
    }

    getCurrentTime() {
        if (this.isPlaying && this.audioContext) {
            return this.audioContext.currentTime - this.startTime;
        }
        return this.currentPosition;
    }

    getDuration() {
        return this.duration;
    }

    getIsPlaying() {
        return this.isPlaying;
    }

    // Time update loop for progress tracking
    startTimeUpdateLoop() {
        this.stopTimeUpdateLoop();
        this.timeUpdateInterval = setInterval(() => {
            if (this.isPlaying && this.onTimeUpdate) {
                const currentTime = this.getCurrentTime();
                this.onTimeUpdate(currentTime, this.duration);
            }
        }, 100); // Update every 100ms
    }

    stopTimeUpdateLoop() {
        if (this.timeUpdateInterval) {
            clearInterval(this.timeUpdateInterval);
            this.timeUpdateInterval = null;
        }
    }

    // Generate waveform data from audio buffer
    generateWaveformData(targetSamples = 1000) {
        if (!this.currentBuffer) {
            console.warn('No current buffer for waveform generation');
            return null;
        }

        console.log(`Generating waveform from buffer: ${this.currentBuffer.duration}s, ${this.currentBuffer.numberOfChannels} channels`);

        const channelData = this.currentBuffer.getChannelData(0); // Use left channel
        const samplesPerPixel = Math.floor(channelData.length / targetSamples);
        const waveformData = [];

        console.log(`Channel data length: ${channelData.length}, samples per pixel: ${samplesPerPixel}`);

        for (let i = 0; i < targetSamples; i++) {
            const start = i * samplesPerPixel;
            const end = Math.min(start + samplesPerPixel, channelData.length);

            let min = 0;
            let max = 0;

            for (let j = start; j < end; j++) {
                const sample = channelData[j];
                if (sample > max) max = sample;
                if (sample < min) min = sample;
            }

            // Store the peak amplitude for this segment
            waveformData.push(Math.max(Math.abs(min), Math.abs(max)));
        }

        console.log(`Generated waveform data: ${waveformData.length} points, max value: ${Math.max(...waveformData)}`);
        return waveformData;
    }

    // Calculate basic audio metrics
    calculateAudioMetrics() {
        if (!this.currentBuffer) return null;

        const channelData = this.currentBuffer.getChannelData(0);
        let sum = 0;
        let peak = 0;

        for (let i = 0; i < channelData.length; i++) {
            const sample = Math.abs(channelData[i]);
            sum += sample * sample;
            if (sample > peak) peak = sample;
        }

        const rms = Math.sqrt(sum / channelData.length);
        const peakDb = 20 * Math.log10(peak);
        const rmsDb = 20 * Math.log10(rms);

        return {
            peak: peak,
            peakDb: peakDb,
            rms: rms,
            rmsDb: rmsDb,
            duration: this.duration,
            sampleRate: this.currentBuffer.sampleRate,
            channels: this.currentBuffer.numberOfChannels
        };
    }

    // Clean up resources
    dispose() {
        this.stop();
        this.stopTimeUpdateLoop();

        if (this.audioContext && this.audioContext.state !== 'closed') {
            this.audioContext.close();
        }

        this.currentBuffer = null;
        this.gainNode = null;
        this.audioContext = null;
    }
}

export default AudioEngine;