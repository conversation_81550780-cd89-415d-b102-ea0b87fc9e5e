{"name": "album-player", "version": "1.0.0", "description": "Spotify Normalization Analyzer - Professional audio player for loudness analysis", "main": "src/main/main.js", "homepage": "./", "scripts": {"start": "concurrently \"npm run electron-dev\" \"npm run react-start\"", "react-start": "react-scripts start", "react-build": "react-scripts build", "electron-dev": "wait-on http://localhost:3000 && electron .", "electron-build": "npm run react-build && electron-builder", "build": "npm run react-build && electron-builder", "dist": "npm run react-build && electron-builder --publish=never", "test": "react-scripts test", "eject": "react-scripts eject"}, "keywords": ["audio", "player", "lufs", "true-peak", "normalization", "spotify", "mastering", "loudness"], "author": "Album Player", "license": "MIT", "devDependencies": {"@types/node": "^20.0.0", "concurrently": "^8.2.0", "electron": "^25.0.0", "electron-builder": "^24.0.0", "electron-is-dev": "^2.0.0", "wait-on": "^7.0.0"}, "dependencies": {"@domchristie/needles": "^0.0.2-1", "@emotion/react": "^11.11.0", "@emotion/styled": "^11.11.0", "@ffmpeg-installer/ffmpeg": "^1.1.0", "fluent-ffmpeg": "^2.1.3", "react": "^18.2.0", "react-beautiful-dnd": "^13.1.1", "react-dom": "^18.2.0", "react-scripts": "5.0.1", "styled-components": "^6.0.0", "wavesurfer.js": "^7.0.0"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "build": {"appId": "com.albumplayer.app", "productName": "Album Player", "directories": {"output": "dist"}, "files": ["build/**/*", "src/main/**/*", "node_modules/**/*"], "mac": {"category": "public.app-category.music"}, "win": {"target": "nsis"}, "linux": {"target": "AppImage"}}}