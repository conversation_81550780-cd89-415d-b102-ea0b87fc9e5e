{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\VSC Folder\\\\AlbumPlayer\\\\src\\\\App.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useCallback, useRef, useEffect } from 'react';\nimport styled from 'styled-components';\nimport WaveformPlayer from './components/WaveformPlayer';\nimport ControlPanel from './components/ControlPanel';\nimport FileDropZone from './components/FileDropZone';\nimport AudioAnalyzer from './audio/AudioAnalyzer';\nimport AudioEngine from './audio/AudioEngine';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AppContainer = styled.div`\n  display: flex;\n  flex-direction: column;\n  height: 100vh;\n  background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);\n  color: #ffffff;\n  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;\n`;\n_c = AppContainer;\nconst Header = styled.div`\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 20px 30px;\n  background: rgba(0, 0, 0, 0.3);\n  border-bottom: 1px solid rgba(255, 255, 255, 0.1);\n`;\n_c2 = Header;\nconst Title = styled.h1`\n  margin: 0;\n  font-size: 24px;\n  font-weight: 300;\n  color: #4CAF50;\n`;\n_c3 = Title;\nconst MainContent = styled.div`\n  display: flex;\n  flex: 1;\n  overflow: hidden;\n`;\n_c4 = MainContent;\nconst WaveformContainer = styled.div`\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  padding: 20px;\n  overflow-y: auto;\n  position: relative;\n`;\n_c5 = WaveformContainer;\nconst SidePanel = styled.div`\n  width: 350px;\n  background: rgba(0, 0, 0, 0.4);\n  border-left: 1px solid rgba(255, 255, 255, 0.1);\n  display: flex;\n  flex-direction: column;\n`;\n_c6 = SidePanel;\nconst EmptyState = styled.div`\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  height: 100%;\n  color: rgba(255, 255, 255, 0.6);\n  text-align: center;\n`;\n_c7 = EmptyState;\nconst EmptyStateIcon = styled.div`\n  font-size: 64px;\n  margin-bottom: 20px;\n  opacity: 0.3;\n`;\n_c8 = EmptyStateIcon;\nconst TracksContainer = styled.div`\n  position: relative;\n`;\n_c9 = TracksContainer;\nconst GlobalDropZone = styled.div`\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  z-index: 1000;\n  pointer-events: ${props => props.isDragActive ? 'auto' : 'none'};\n  background: ${props => props.isDragActive ? 'rgba(76, 175, 80, 0.1)' : 'transparent'};\n  border: 2px dashed ${props => props.isDragActive ? '#4CAF50' : 'transparent'};\n  border-radius: 12px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  transition: all 0.3s ease;\n  \n  h2 {\n    color: #4CAF50;\n    font-size: 24px;\n    text-shadow: 0 2px 4px rgba(0,0,0,0.3);\n    margin: 0;\n  }\n`;\n_c0 = GlobalDropZone;\nfunction App() {\n  _s();\n  const [audioFiles, setAudioFiles] = useState([]);\n  const [currentTrack, setCurrentTrack] = useState(null);\n  const [isPlaying, setIsPlaying] = useState(false);\n  const [masterVolume, setMasterVolume] = useState(0.8);\n  const [analysisData, setAnalysisData] = useState({});\n  const [currentTime, setCurrentTime] = useState(0);\n  const [duration, setDuration] = useState(0);\n  const [isDragActive, setIsDragActive] = useState(false);\n  const [isLoading, setIsLoading] = useState(false);\n  const [error, setError] = useState(null);\n  const [loadingMessage, setLoadingMessage] = useState('');\n  const audioAnalyzer = useRef(new AudioAnalyzer());\n  const audioEngine = useRef(new AudioEngine());\n  const lastClickTime = useRef(0);\n  const lastPlayPauseTime = useRef(0);\n\n  // REMOVED: validateAndRecoverState function - was causing performance issues\n  // Audio engine event handlers below provide proper state synchronization\n\n  // REMOVED: Performance-killing validation loop\n  // The audio engine event handlers below provide proper state sync\n\n  // Set up audio engine event handlers\n  useEffect(() => {\n    const engine = audioEngine.current;\n    engine.onTimeUpdate = (time, dur) => {\n      // Validate the time values before setting state\n      if (typeof time === 'number' && !isNaN(time) && time >= 0) {\n        setCurrentTime(time);\n      }\n      if (typeof dur === 'number' && !isNaN(dur) && dur > 0) {\n        setDuration(dur);\n      }\n    };\n    engine.onEnded = () => {\n      console.log('Audio ended');\n      setIsPlaying(false);\n      setCurrentTime(0);\n    };\n    engine.onError = error => {\n      console.error('Audio engine error:', error);\n      setIsPlaying(false);\n      setIsLoading(false);\n      setError(`Audio error: ${error.message || 'Unknown error'}`);\n      setLoadingMessage('');\n    };\n    engine.onLoadStart = () => {\n      console.log('Audio load started');\n      setIsLoading(true);\n      setLoadingMessage('Loading audio file...');\n      setError(null);\n    };\n    engine.onLoadEnd = () => {\n      console.log('Audio load ended');\n      setIsLoading(false);\n      setLoadingMessage('');\n    };\n\n    // Set initial volume\n    engine.setVolume(masterVolume);\n    return () => {\n      engine.dispose();\n    };\n  }, []);\n\n  // Update volume when masterVolume changes\n  useEffect(() => {\n    audioEngine.current.setVolume(masterVolume);\n  }, [masterVolume]);\n\n  // Add spacebar play/pause functionality\n  useEffect(() => {\n    const handleKeyDown = e => {\n      if (e.code === 'Space' && e.target.tagName !== 'INPUT' && e.target.tagName !== 'TEXTAREA') {\n        e.preventDefault();\n        if (currentTrack) {\n          if (isPlaying) {\n            audioEngine.current.pause();\n            setIsPlaying(false);\n          } else {\n            audioEngine.current.play();\n            setIsPlaying(true);\n          }\n        }\n      }\n    };\n    document.addEventListener('keydown', handleKeyDown);\n    return () => {\n      document.removeEventListener('keydown', handleKeyDown);\n    };\n  }, [currentTrack, isPlaying]);\n\n  // Global drag and drop handlers\n  useEffect(() => {\n    const handleDragEnter = e => {\n      e.preventDefault();\n      e.stopPropagation();\n      if (e.dataTransfer.items && e.dataTransfer.items.length > 0) {\n        setIsDragActive(true);\n      }\n    };\n    const handleDragLeave = e => {\n      e.preventDefault();\n      e.stopPropagation();\n      if (!e.currentTarget.contains(e.relatedTarget)) {\n        setIsDragActive(false);\n      }\n    };\n    const handleDragOver = e => {\n      e.preventDefault();\n      e.stopPropagation();\n    };\n    const handleDrop = async e => {\n      e.preventDefault();\n      e.stopPropagation();\n      setIsDragActive(false);\n      const files = [];\n      if (e.dataTransfer.files && e.dataTransfer.files.length > 0) {\n        for (let i = 0; i < e.dataTransfer.files.length; i++) {\n          const file = e.dataTransfer.files[i];\n          if (isAudioFile(file.name)) {\n            files.push(file);\n          }\n        }\n      }\n      if (files.length > 0) {\n        handleFilesAdded(files);\n      }\n    };\n    document.addEventListener('dragenter', handleDragEnter);\n    document.addEventListener('dragleave', handleDragLeave);\n    document.addEventListener('dragover', handleDragOver);\n    document.addEventListener('drop', handleDrop);\n    return () => {\n      document.removeEventListener('dragenter', handleDragEnter);\n      document.removeEventListener('dragleave', handleDragLeave);\n      document.removeEventListener('dragover', handleDragOver);\n      document.removeEventListener('drop', handleDrop);\n    };\n  }, []);\n  const isAudioFile = filename => {\n    const audioExtensions = ['.wav', '.flac', '.mp3', '.aac', '.ogg', '.m4a', '.wma'];\n    const ext = filename.toLowerCase().substring(filename.lastIndexOf('.'));\n    return audioExtensions.includes(ext);\n  };\n\n  // Handle file loading\n  const handleFilesAdded = useCallback(async files => {\n    const newFiles = [];\n    for (const file of files) {\n      try {\n        // Determine file name and path based on file type\n        let fileName, filePath;\n        if (file instanceof File) {\n          // Browser File object from drag & drop\n          fileName = file.name;\n          filePath = file; // Keep the File object for browser processing\n        } else if (typeof file === 'string') {\n          // File path string from Electron\n          fileName = file.split('/').pop().split('\\\\').pop(); // Handle both / and \\ separators\n          filePath = file;\n        } else {\n          // Fallback\n          fileName = 'Unknown File';\n          filePath = file;\n        }\n\n        // Analyze the audio file\n        const analysis = await audioAnalyzer.current.analyzeFile(filePath);\n\n        // Preload audio for seamless playback\n        try {\n          await audioEngine.current.preloadAudioFile(filePath);\n          console.log(`Preloaded: ${fileName}`);\n        } catch (error) {\n          console.warn(`Failed to preload ${fileName}:`, error);\n        }\n        const audioFile = {\n          id: Date.now() + Math.random(),\n          name: fileName,\n          path: filePath,\n          analysis,\n          color: generateRandomColor(),\n          waveformData: analysis.waveformData || null\n        };\n        newFiles.push(audioFile);\n\n        // Store analysis data\n        setAnalysisData(prev => ({\n          ...prev,\n          [audioFile.id]: analysis\n        }));\n      } catch (error) {\n        console.error('Error analyzing file:', error);\n\n        // Create a basic file entry even if analysis fails\n        let fileName;\n        if (file instanceof File) {\n          fileName = file.name;\n        } else if (typeof file === 'string') {\n          fileName = file.split('/').pop().split('\\\\').pop();\n        } else {\n          fileName = 'Unknown File';\n        }\n        const audioFile = {\n          id: Date.now() + Math.random(),\n          name: fileName,\n          path: file,\n          analysis: null,\n          color: generateRandomColor(),\n          waveformData: null\n        };\n        newFiles.push(audioFile);\n      }\n    }\n    setAudioFiles(prev => [...prev, ...newFiles]);\n\n    // Auto-select first track if none selected\n    if (!currentTrack && newFiles.length > 0) {\n      setCurrentTrack(newFiles[0]);\n    }\n  }, [currentTrack]);\n\n  // Generate random colors for waveforms\n  const generateRandomColor = () => {\n    const colors = ['#4CAF50', '#2196F3', '#FF9800', '#E91E63', '#9C27B0', '#00BCD4', '#FFEB3B', '#FF5722'];\n    return colors[Math.floor(Math.random() * colors.length)];\n  };\n\n  // Handle track selection\n  const handleTrackSelect = useCallback(async track => {\n    if (track.id === (currentTrack && currentTrack.id)) {\n      console.log('Track already selected');\n      return;\n    }\n    if (isLoading) {\n      console.log('Already loading a track, ignoring click');\n      return;\n    }\n    try {\n      console.log(`Loading track: ${track.name}`);\n      setIsLoading(true);\n\n      // Stop current playback\n      audioEngine.current.stop();\n      setIsPlaying(false);\n\n      // Load new track\n      await audioEngine.current.loadAudioFile(track.path);\n      setCurrentTrack(track);\n      setCurrentTime(0);\n      console.log(`Track loaded successfully: ${track.name}`);\n    } catch (error) {\n      console.error('Error loading track:', error);\n      // Reset state on error\n      setCurrentTrack(null);\n      setIsPlaying(false);\n      setCurrentTime(0);\n    } finally {\n      setIsLoading(false);\n    }\n  }, [currentTrack, isLoading]);\n\n  // Handle playback control\n  const handlePlayPause = useCallback(() => {\n    if (!currentTrack) {\n      console.log('No current track selected');\n      return;\n    }\n    if (isLoading) {\n      console.log('Track is loading, cannot play/pause');\n      return;\n    }\n\n    // Debounce rapid play/pause clicks\n    const now = Date.now();\n    if (now - lastPlayPauseTime.current < 200) {\n      console.log('Play/pause debounced');\n      return;\n    }\n    lastPlayPauseTime.current = now;\n    try {\n      const engine = audioEngine.current;\n      const currentEngineState = engine.getIsPlaying();\n      console.log(`Play/Pause clicked - Engine state: ${currentEngineState}`);\n\n      // Use ONLY the engine state as the source of truth\n      if (currentEngineState) {\n        console.log('Pausing playback');\n        engine.pause();\n        setIsPlaying(false);\n        console.log('Pause completed');\n      } else {\n        console.log('Starting playback');\n        // Ensure the track is loaded before playing\n        if (engine.currentBuffer) {\n          engine.play();\n          setIsPlaying(true);\n          console.log('Play started');\n        } else {\n          console.log('Track not loaded, reloading...');\n          // Reload the track if it's not loaded\n          handleTrackSelect(currentTrack);\n        }\n      }\n    } catch (error) {\n      console.error('Error in play/pause:', error);\n      // Reset state on error\n      setIsPlaying(false);\n    }\n  }, [isPlaying, currentTrack, isLoading, handleTrackSelect]);\n\n  // Handle waveform click (seek)\n  const handleWaveformClick = useCallback(async (track, position) => {\n    if (!track || typeof position !== 'number' || position < 0 || position > 1) {\n      console.error('Invalid waveform click parameters');\n      return;\n    }\n\n    // Debounce rapid clicks more aggressively\n    const now = Date.now();\n    if (now - lastClickTime.current < 300) {\n      console.log('Click debounced');\n      return;\n    }\n    lastClickTime.current = now;\n    console.log(`Waveform clicked: ${track.name} at position ${position.toFixed(3)}`);\n    try {\n      // If clicking on a different track, switch to it\n      if (track.id !== (currentTrack && currentTrack.id)) {\n        await handleTrackSelect(track);\n        // Wait for the track to load before seeking\n        await new Promise(resolve => setTimeout(resolve, 200));\n      }\n\n      // Ensure the audio engine is ready\n      const engine = audioEngine.current;\n      if (!engine.currentBuffer) {\n        console.warn('Audio not loaded, cannot seek');\n        return;\n      }\n\n      // Seek to position\n      const trackAnalysis = analysisData[track.id];\n      const trackDuration = trackAnalysis && trackAnalysis.duration || engine.getDuration() || 0;\n      if (trackDuration > 0) {\n        const seekTime = Math.max(0, Math.min(trackDuration, position * trackDuration));\n        console.log(`Seeking to: ${seekTime.toFixed(2)}s of ${trackDuration.toFixed(2)}s`);\n\n        // Store the current playing state before seeking\n        const wasPlaying = engine.getIsPlaying();\n\n        // Perform the seek\n        engine.seek(seekTime);\n        setCurrentTime(seekTime);\n\n        // Only start playing if we were already playing or if explicitly requested\n        if (wasPlaying) {\n          // Give a small delay to ensure seek is complete\n          setTimeout(() => {\n            if (engine.getIsPlaying()) {\n              setIsPlaying(true);\n            }\n          }, 50);\n        } else {\n          // If we weren't playing, just update the position\n          setIsPlaying(false);\n        }\n\n        // Validate state after a longer delay to avoid race conditions\n        setTimeout(validateAndRecoverState, 500);\n      } else {\n        console.warn('No duration available for seeking');\n      }\n    } catch (error) {\n      console.error('Error in waveform click:', error);\n    }\n  }, [currentTrack, analysisData, handleTrackSelect, isPlaying]);\n\n  // Handle track deletion\n  const handleTrackDelete = useCallback(trackToDelete => {\n    console.log(`Deleting track: ${trackToDelete.name}`);\n\n    // Stop playback if deleting current track\n    if (currentTrack && currentTrack.id === trackToDelete.id) {\n      audioEngine.current.stop();\n      setIsPlaying(false);\n      setCurrentTrack(null);\n      setCurrentTime(0);\n      setDuration(0);\n    }\n\n    // Remove from audio files list\n    setAudioFiles(prev => prev.filter(track => track.id !== trackToDelete.id));\n\n    // Remove from analysis data\n    setAnalysisData(prev => {\n      const newData = {\n        ...prev\n      };\n      delete newData[trackToDelete.id];\n      return newData;\n    });\n  }, [currentTrack]);\n  return /*#__PURE__*/_jsxDEV(AppContainer, {\n    children: [/*#__PURE__*/_jsxDEV(Header, {\n      children: [/*#__PURE__*/_jsxDEV(Title, {\n        children: \" Album Player \"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 544,\n        columnNumber: 13\n      }, this), \" \", /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          fontSize: '14px',\n          opacity: 0.7\n        },\n        children: \"Spotify Normalization Analyzer \"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 545,\n        columnNumber: 44\n      }, this), \" \", error && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          color: '#ff5722',\n          fontSize: '12px',\n          marginTop: '8px'\n        },\n        children: [\" \\u26A0\\uFE0F\", error, \" \"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 551,\n        columnNumber: 26\n      }, this), \" \", isLoading && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          color: '#4CAF50',\n          fontSize: '12px',\n          marginTop: '8px'\n        },\n        children: [\" \\u23F3\", loadingMessage, \" \"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 553,\n        columnNumber: 34\n      }, this), \" \"]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 542,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(MainContent, {\n      children: [/*#__PURE__*/_jsxDEV(WaveformContainer, {\n        children: [\" \", audioFiles.length === 0 ? /*#__PURE__*/_jsxDEV(FileDropZone, {\n          onFilesAdded: handleFilesAdded,\n          hasFiles: false,\n          children: [/*#__PURE__*/_jsxDEV(EmptyState, {\n            children: [/*#__PURE__*/_jsxDEV(EmptyStateIcon, {\n              children: \" \\uD83C\\uDFB5 \"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 567,\n              columnNumber: 33\n            }, this), \" \", /*#__PURE__*/_jsxDEV(\"h2\", {\n              children: \" Drop Audio Files Here \"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 568,\n              columnNumber: 72\n            }, this), \" \", /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \" Drag and drop your audio files to start analyzing \"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 569,\n              columnNumber: 67\n            }, this), \" \", /*#__PURE__*/_jsxDEV(\"p\", {\n              style: {\n                fontSize: '12px',\n                opacity: 0.5\n              },\n              children: \"Supported: WAV, FLAC, MP3, AAC, OGG \"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 570,\n              columnNumber: 93\n            }, this), \" \"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 565,\n            columnNumber: 33\n          }, this), \" \"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 562,\n          columnNumber: 57\n        }, this) : /*#__PURE__*/_jsxDEV(TracksContainer, {\n          children: [\" \", audioFiles.map((track, index) => /*#__PURE__*/_jsxDEV(WaveformPlayer, {\n            track: track,\n            isActive: currentTrack && currentTrack.id === track.id,\n            isPlaying: isPlaying && currentTrack && currentTrack.id === track.id,\n            onTrackSelect: handleTrackSelect,\n            onWaveformClick: handleWaveformClick,\n            onTrackDelete: handleTrackDelete,\n            onPlayPause: handlePlayPause,\n            analysisData: analysisData[track.id],\n            currentTime: currentTrack && currentTrack.id === track.id ? currentTime : 0,\n            duration: currentTrack && currentTrack.id === track.id ? duration : analysisData[track.id] && analysisData[track.id].duration || 0\n          }, track.id, false, {\n            fileName: _jsxFileName,\n            lineNumber: 580,\n            columnNumber: 76\n          }, this)), \" \", /*#__PURE__*/_jsxDEV(GlobalDropZone, {\n            isDragActive: isDragActive,\n            children: [\" \", isDragActive && /*#__PURE__*/_jsxDEV(\"h2\", {\n              children: \" Drop to Add More Files \"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 596,\n              columnNumber: 57\n            }, this), \" \"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 594,\n            columnNumber: 39\n          }, this), \" \"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 578,\n          columnNumber: 35\n        }, this), \" \"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 560,\n        columnNumber: 25\n      }, this), /*#__PURE__*/_jsxDEV(SidePanel, {\n        children: [/*#__PURE__*/_jsxDEV(ControlPanel, {\n          currentTrack: currentTrack,\n          isPlaying: isPlaying,\n          masterVolume: masterVolume,\n          onPlayPause: handlePlayPause,\n          onVolumeChange: setMasterVolume,\n          onFilesAdded: handleFilesAdded,\n          analysisData: currentTrack ? analysisData[currentTrack.id] : null,\n          currentTime: currentTime,\n          duration: duration\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 605,\n          columnNumber: 33\n        }, this), \" \"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 603,\n        columnNumber: 33\n      }, this), \" \"]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 558,\n      columnNumber: 25\n    }, this), \" \"]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 540,\n    columnNumber: 14\n  }, this);\n}\n_s(App, \"E8nlJykG9SLyv5S1s4R/0eH+XgQ=\");\n_c1 = App;\nexport default App;\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9, _c0, _c1;\n$RefreshReg$(_c, \"AppContainer\");\n$RefreshReg$(_c2, \"Header\");\n$RefreshReg$(_c3, \"Title\");\n$RefreshReg$(_c4, \"MainContent\");\n$RefreshReg$(_c5, \"WaveformContainer\");\n$RefreshReg$(_c6, \"SidePanel\");\n$RefreshReg$(_c7, \"EmptyState\");\n$RefreshReg$(_c8, \"EmptyStateIcon\");\n$RefreshReg$(_c9, \"TracksContainer\");\n$RefreshReg$(_c0, \"GlobalDropZone\");\n$RefreshReg$(_c1, \"App\");", "map": {"version": 3, "names": ["React", "useState", "useCallback", "useRef", "useEffect", "styled", "WaveformPlayer", "ControlPanel", "FileDropZone", "AudioAnalyzer", "AudioEngine", "jsxDEV", "_jsxDEV", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "div", "_c", "Header", "_c2", "Title", "h1", "_c3", "MainContent", "_c4", "WaveformContainer", "_c5", "SidePanel", "_c6", "EmptyState", "_c7", "EmptyStateIcon", "_c8", "TracksContainer", "_c9", "GlobalDropZone", "props", "isDragActive", "_c0", "App", "_s", "audioFiles", "setAudioFiles", "currentTrack", "setCurrentTrack", "isPlaying", "setIsPlaying", "masterVolume", "setMasterVolume", "analysisData", "setAnalysisData", "currentTime", "setCurrentTime", "duration", "setDuration", "setIsDragActive", "isLoading", "setIsLoading", "error", "setError", "loadingMessage", "setLoadingMessage", "audioAnalyzer", "audioEngine", "lastClickTime", "lastPlayPauseTime", "engine", "current", "onTimeUpdate", "time", "dur", "isNaN", "onEnded", "console", "log", "onError", "message", "onLoadStart", "onLoadEnd", "setVolume", "dispose", "handleKeyDown", "e", "code", "target", "tagName", "preventDefault", "pause", "play", "document", "addEventListener", "removeEventListener", "handleDragEnter", "stopPropagation", "dataTransfer", "items", "length", "handleDragLeave", "currentTarget", "contains", "relatedTarget", "handleDragOver", "handleDrop", "files", "i", "file", "isAudioFile", "name", "push", "handleFilesAdded", "filename", "audioExtensions", "ext", "toLowerCase", "substring", "lastIndexOf", "includes", "newFiles", "fileName", "filePath", "File", "split", "pop", "analysis", "analyzeFile", "preloadAudioFile", "warn", "audioFile", "id", "Date", "now", "Math", "random", "path", "color", "generateRandomColor", "waveformData", "prev", "colors", "floor", "handleTrackSelect", "track", "stop", "loadAudioFile", "handlePlayPause", "currentEngineState", "getIsPlaying", "current<PERSON><PERSON><PERSON>", "handleWaveformClick", "position", "toFixed", "Promise", "resolve", "setTimeout", "trackAnalysis", "trackDuration", "getDuration", "seekTime", "max", "min", "wasPlaying", "seek", "validateAndRecoverState", "handleTrackDelete", "trackToDelete", "filter", "newData", "children", "_jsxFileName", "lineNumber", "columnNumber", "style", "fontSize", "opacity", "marginTop", "onFilesAdded", "hasFiles", "map", "index", "isActive", "onTrackSelect", "onWaveformClick", "onTrackDelete", "onPlayPause", "onVolumeChange", "_c1", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/VSC Folder/AlbumPlayer/src/App.js"], "sourcesContent": ["import React, { useState, useCallback, useRef, useEffect } from 'react';\nimport styled from 'styled-components';\nimport WaveformPlayer from './components/WaveformPlayer';\nimport ControlPanel from './components/ControlPanel';\nimport FileDropZone from './components/FileDropZone';\nimport AudioAnalyzer from './audio/AudioAnalyzer';\nimport AudioEngine from './audio/AudioEngine';\n\nconst AppContainer = styled.div `\n  display: flex;\n  flex-direction: column;\n  height: 100vh;\n  background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);\n  color: #ffffff;\n  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;\n`;\n\nconst Header = styled.div `\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 20px 30px;\n  background: rgba(0, 0, 0, 0.3);\n  border-bottom: 1px solid rgba(255, 255, 255, 0.1);\n`;\n\nconst Title = styled.h1 `\n  margin: 0;\n  font-size: 24px;\n  font-weight: 300;\n  color: #4CAF50;\n`;\n\nconst MainContent = styled.div `\n  display: flex;\n  flex: 1;\n  overflow: hidden;\n`;\n\nconst WaveformContainer = styled.div `\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  padding: 20px;\n  overflow-y: auto;\n  position: relative;\n`;\n\nconst SidePanel = styled.div `\n  width: 350px;\n  background: rgba(0, 0, 0, 0.4);\n  border-left: 1px solid rgba(255, 255, 255, 0.1);\n  display: flex;\n  flex-direction: column;\n`;\n\nconst EmptyState = styled.div `\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  height: 100%;\n  color: rgba(255, 255, 255, 0.6);\n  text-align: center;\n`;\n\nconst EmptyStateIcon = styled.div `\n  font-size: 64px;\n  margin-bottom: 20px;\n  opacity: 0.3;\n`;\n\nconst TracksContainer = styled.div `\n  position: relative;\n`;\n\nconst GlobalDropZone = styled.div `\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  z-index: 1000;\n  pointer-events: ${props => props.isDragActive ? 'auto' : 'none'};\n  background: ${props => props.isDragActive ? 'rgba(76, 175, 80, 0.1)' : 'transparent'};\n  border: 2px dashed ${props => props.isDragActive ? '#4CAF50' : 'transparent'};\n  border-radius: 12px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  transition: all 0.3s ease;\n  \n  h2 {\n    color: #4CAF50;\n    font-size: 24px;\n    text-shadow: 0 2px 4px rgba(0,0,0,0.3);\n    margin: 0;\n  }\n`;\n\nfunction App() {\n    const [audioFiles, setAudioFiles] = useState([]);\n    const [currentTrack, setCurrentTrack] = useState(null);\n    const [isPlaying, setIsPlaying] = useState(false);\n    const [masterVolume, setMasterVolume] = useState(0.8);\n    const [analysisData, setAnalysisData] = useState({});\n    const [currentTime, setCurrentTime] = useState(0);\n    const [duration, setDuration] = useState(0);\n    const [isDragActive, setIsDragActive] = useState(false);\n    const [isLoading, setIsLoading] = useState(false);\n    const [error, setError] = useState(null);\n    const [loadingMessage, setLoadingMessage] = useState('');\n\n    const audioAnalyzer = useRef(new AudioAnalyzer());\n    const audioEngine = useRef(new AudioEngine());\n    const lastClickTime = useRef(0);\n    const lastPlayPauseTime = useRef(0);\n\n    // REMOVED: validateAndRecoverState function - was causing performance issues\n    // Audio engine event handlers below provide proper state synchronization\n\n    // REMOVED: Performance-killing validation loop\n    // The audio engine event handlers below provide proper state sync\n\n    // Set up audio engine event handlers\n    useEffect(() => {\n        const engine = audioEngine.current;\n\n        engine.onTimeUpdate = (time, dur) => {\n            // Validate the time values before setting state\n            if (typeof time === 'number' && !isNaN(time) && time >= 0) {\n                setCurrentTime(time);\n            }\n            if (typeof dur === 'number' && !isNaN(dur) && dur > 0) {\n                setDuration(dur);\n            }\n        };\n\n        engine.onEnded = () => {\n            console.log('Audio ended');\n            setIsPlaying(false);\n            setCurrentTime(0);\n        };\n\n        engine.onError = (error) => {\n            console.error('Audio engine error:', error);\n            setIsPlaying(false);\n            setIsLoading(false);\n            setError(`Audio error: ${error.message || 'Unknown error'}`);\n            setLoadingMessage('');\n        };\n\n        engine.onLoadStart = () => {\n            console.log('Audio load started');\n            setIsLoading(true);\n            setLoadingMessage('Loading audio file...');\n            setError(null);\n        };\n\n        engine.onLoadEnd = () => {\n            console.log('Audio load ended');\n            setIsLoading(false);\n            setLoadingMessage('');\n        };\n\n        // Set initial volume\n        engine.setVolume(masterVolume);\n\n        return () => {\n            engine.dispose();\n        };\n    }, []);\n\n    // Update volume when masterVolume changes\n    useEffect(() => {\n        audioEngine.current.setVolume(masterVolume);\n    }, [masterVolume]);\n\n    // Add spacebar play/pause functionality\n    useEffect(() => {\n        const handleKeyDown = (e) => {\n            if (e.code === 'Space' && e.target.tagName !== 'INPUT' && e.target.tagName !== 'TEXTAREA') {\n                e.preventDefault();\n                if (currentTrack) {\n                    if (isPlaying) {\n                        audioEngine.current.pause();\n                        setIsPlaying(false);\n                    } else {\n                        audioEngine.current.play();\n                        setIsPlaying(true);\n                    }\n                }\n            }\n        };\n\n        document.addEventListener('keydown', handleKeyDown);\n        return () => {\n            document.removeEventListener('keydown', handleKeyDown);\n        };\n    }, [currentTrack, isPlaying]);\n\n    // Global drag and drop handlers\n    useEffect(() => {\n        const handleDragEnter = (e) => {\n            e.preventDefault();\n            e.stopPropagation();\n            if (e.dataTransfer.items && e.dataTransfer.items.length > 0) {\n                setIsDragActive(true);\n            }\n        };\n\n        const handleDragLeave = (e) => {\n            e.preventDefault();\n            e.stopPropagation();\n            if (!e.currentTarget.contains(e.relatedTarget)) {\n                setIsDragActive(false);\n            }\n        };\n\n        const handleDragOver = (e) => {\n            e.preventDefault();\n            e.stopPropagation();\n        };\n\n        const handleDrop = async(e) => {\n            e.preventDefault();\n            e.stopPropagation();\n            setIsDragActive(false);\n\n            const files = [];\n            if (e.dataTransfer.files && e.dataTransfer.files.length > 0) {\n                for (let i = 0; i < e.dataTransfer.files.length; i++) {\n                    const file = e.dataTransfer.files[i];\n                    if (isAudioFile(file.name)) {\n                        files.push(file);\n                    }\n                }\n            }\n\n            if (files.length > 0) {\n                handleFilesAdded(files);\n            }\n        };\n\n        document.addEventListener('dragenter', handleDragEnter);\n        document.addEventListener('dragleave', handleDragLeave);\n        document.addEventListener('dragover', handleDragOver);\n        document.addEventListener('drop', handleDrop);\n\n        return () => {\n            document.removeEventListener('dragenter', handleDragEnter);\n            document.removeEventListener('dragleave', handleDragLeave);\n            document.removeEventListener('dragover', handleDragOver);\n            document.removeEventListener('drop', handleDrop);\n        };\n    }, []);\n\n    const isAudioFile = (filename) => {\n        const audioExtensions = ['.wav', '.flac', '.mp3', '.aac', '.ogg', '.m4a', '.wma'];\n        const ext = filename.toLowerCase().substring(filename.lastIndexOf('.'));\n        return audioExtensions.includes(ext);\n    };\n\n    // Handle file loading\n    const handleFilesAdded = useCallback(async(files) => {\n        const newFiles = [];\n\n        for (const file of files) {\n            try {\n                // Determine file name and path based on file type\n                let fileName, filePath;\n\n                if (file instanceof File) {\n                    // Browser File object from drag & drop\n                    fileName = file.name;\n                    filePath = file; // Keep the File object for browser processing\n                } else if (typeof file === 'string') {\n                    // File path string from Electron\n                    fileName = file.split('/').pop().split('\\\\').pop(); // Handle both / and \\ separators\n                    filePath = file;\n                } else {\n                    // Fallback\n                    fileName = 'Unknown File';\n                    filePath = file;\n                }\n\n                // Analyze the audio file\n                const analysis = await audioAnalyzer.current.analyzeFile(filePath);\n\n                // Preload audio for seamless playback\n                try {\n                    await audioEngine.current.preloadAudioFile(filePath);\n                    console.log(`Preloaded: ${fileName}`);\n                } catch (error) {\n                    console.warn(`Failed to preload ${fileName}:`, error);\n                }\n\n                const audioFile = {\n                    id: Date.now() + Math.random(),\n                    name: fileName,\n                    path: filePath,\n                    analysis,\n                    color: generateRandomColor(),\n                    waveformData: analysis.waveformData || null\n                };\n\n                newFiles.push(audioFile);\n\n                // Store analysis data\n                setAnalysisData(prev => ({\n                    ...prev,\n                    [audioFile.id]: analysis\n                }));\n\n            } catch (error) {\n                console.error('Error analyzing file:', error);\n\n                // Create a basic file entry even if analysis fails\n                let fileName;\n                if (file instanceof File) {\n                    fileName = file.name;\n                } else if (typeof file === 'string') {\n                    fileName = file.split('/').pop().split('\\\\').pop();\n                } else {\n                    fileName = 'Unknown File';\n                }\n\n                const audioFile = {\n                    id: Date.now() + Math.random(),\n                    name: fileName,\n                    path: file,\n                    analysis: null,\n                    color: generateRandomColor(),\n                    waveformData: null\n                };\n                newFiles.push(audioFile);\n            }\n        }\n\n        setAudioFiles(prev => [...prev, ...newFiles]);\n\n        // Auto-select first track if none selected\n        if (!currentTrack && newFiles.length > 0) {\n            setCurrentTrack(newFiles[0]);\n        }\n    }, [currentTrack]);\n\n    // Generate random colors for waveforms\n    const generateRandomColor = () => {\n        const colors = [\n            '#4CAF50', '#2196F3', '#FF9800', '#E91E63',\n            '#9C27B0', '#00BCD4', '#FFEB3B', '#FF5722'\n        ];\n        return colors[Math.floor(Math.random() * colors.length)];\n    };\n\n    // Handle track selection\n    const handleTrackSelect = useCallback(async(track) => {\n        if (track.id === (currentTrack && currentTrack.id)) {\n            console.log('Track already selected');\n            return;\n        }\n\n        if (isLoading) {\n            console.log('Already loading a track, ignoring click');\n            return;\n        }\n\n        try {\n            console.log(`Loading track: ${track.name}`);\n            setIsLoading(true);\n\n            // Stop current playback\n            audioEngine.current.stop();\n            setIsPlaying(false);\n\n            // Load new track\n            await audioEngine.current.loadAudioFile(track.path);\n            setCurrentTrack(track);\n            setCurrentTime(0);\n\n            console.log(`Track loaded successfully: ${track.name}`);\n        } catch (error) {\n            console.error('Error loading track:', error);\n            // Reset state on error\n            setCurrentTrack(null);\n            setIsPlaying(false);\n            setCurrentTime(0);\n        } finally {\n            setIsLoading(false);\n        }\n    }, [currentTrack, isLoading]);\n\n    // Handle playback control\n    const handlePlayPause = useCallback(() => {\n        if (!currentTrack) {\n            console.log('No current track selected');\n            return;\n        }\n\n        if (isLoading) {\n            console.log('Track is loading, cannot play/pause');\n            return;\n        }\n\n        // Debounce rapid play/pause clicks\n        const now = Date.now();\n        if (now - lastPlayPauseTime.current < 200) {\n            console.log('Play/pause debounced');\n            return;\n        }\n        lastPlayPauseTime.current = now;\n\n        try {\n            const engine = audioEngine.current;\n            const currentEngineState = engine.getIsPlaying();\n\n            console.log(`Play/Pause clicked - Engine state: ${currentEngineState}`);\n\n            // Use ONLY the engine state as the source of truth\n            if (currentEngineState) {\n                console.log('Pausing playback');\n                engine.pause();\n                setIsPlaying(false);\n                console.log('Pause completed');\n            } else {\n                console.log('Starting playback');\n                // Ensure the track is loaded before playing\n                if (engine.currentBuffer) {\n                    engine.play();\n                    setIsPlaying(true);\n                    console.log('Play started');\n                } else {\n                    console.log('Track not loaded, reloading...');\n                    // Reload the track if it's not loaded\n                    handleTrackSelect(currentTrack);\n                }\n            }\n        } catch (error) {\n            console.error('Error in play/pause:', error);\n            // Reset state on error\n            setIsPlaying(false);\n        }\n    }, [isPlaying, currentTrack, isLoading, handleTrackSelect]);\n\n    // Handle waveform click (seek)\n    const handleWaveformClick = useCallback(async(track, position) => {\n        if (!track || typeof position !== 'number' || position < 0 || position > 1) {\n            console.error('Invalid waveform click parameters');\n            return;\n        }\n\n        // Debounce rapid clicks more aggressively\n        const now = Date.now();\n        if (now - lastClickTime.current < 300) {\n            console.log('Click debounced');\n            return;\n        }\n        lastClickTime.current = now;\n\n        console.log(`Waveform clicked: ${track.name} at position ${position.toFixed(3)}`);\n\n        try {\n            // If clicking on a different track, switch to it\n            if (track.id !== (currentTrack && currentTrack.id)) {\n                await handleTrackSelect(track);\n                // Wait for the track to load before seeking\n                await new Promise(resolve => setTimeout(resolve, 200));\n            }\n\n            // Ensure the audio engine is ready\n            const engine = audioEngine.current;\n            if (!engine.currentBuffer) {\n                console.warn('Audio not loaded, cannot seek');\n                return;\n            }\n\n            // Seek to position\n            const trackAnalysis = analysisData[track.id];\n            const trackDuration = trackAnalysis && trackAnalysis.duration || engine.getDuration() || 0;\n\n            if (trackDuration > 0) {\n                const seekTime = Math.max(0, Math.min(trackDuration, position * trackDuration));\n                console.log(`Seeking to: ${seekTime.toFixed(2)}s of ${trackDuration.toFixed(2)}s`);\n\n                // Store the current playing state before seeking\n                const wasPlaying = engine.getIsPlaying();\n\n                // Perform the seek\n                engine.seek(seekTime);\n                setCurrentTime(seekTime);\n\n                // Only start playing if we were already playing or if explicitly requested\n                if (wasPlaying) {\n                    // Give a small delay to ensure seek is complete\n                    setTimeout(() => {\n                        if (engine.getIsPlaying()) {\n                            setIsPlaying(true);\n                        }\n                    }, 50);\n                } else {\n                    // If we weren't playing, just update the position\n                    setIsPlaying(false);\n                }\n\n                // Validate state after a longer delay to avoid race conditions\n                setTimeout(validateAndRecoverState, 500);\n            } else {\n                console.warn('No duration available for seeking');\n            }\n        } catch (error) {\n            console.error('Error in waveform click:', error);\n        }\n    }, [currentTrack, analysisData, handleTrackSelect, isPlaying]);\n\n    // Handle track deletion\n    const handleTrackDelete = useCallback((trackToDelete) => {\n        console.log(`Deleting track: ${trackToDelete.name}`);\n\n        // Stop playback if deleting current track\n        if (currentTrack && currentTrack.id === trackToDelete.id) {\n            audioEngine.current.stop();\n            setIsPlaying(false);\n            setCurrentTrack(null);\n            setCurrentTime(0);\n            setDuration(0);\n        }\n\n        // Remove from audio files list\n        setAudioFiles(prev => prev.filter(track => track.id !== trackToDelete.id));\n\n        // Remove from analysis data\n        setAnalysisData(prev => {\n            const newData = {...prev };\n            delete newData[trackToDelete.id];\n            return newData;\n        });\n    }, [currentTrack]);\n\n    return ( <\n            AppContainer >\n            <\n            Header >\n            <\n            Title > Album Player < /Title> <\n            div style = {\n                { fontSize: '14px', opacity: 0.7 }\n            } >\n            Spotify Normalization Analyzer <\n            /div> {\n                error && < div style = {\n                    { color: '#ff5722', fontSize: '12px', marginTop: '8px' } } > ⚠️{ error } < /div>} {\n                    isLoading && < div style = {\n                            { color: '#4CAF50', fontSize: '12px', marginTop: '8px' } } > ⏳{ loadingMessage } < /div>} <\n                        /\n                    Header >\n\n                        <\n                        MainContent >\n                        <\n                        WaveformContainer > {\n                            audioFiles.length === 0 ? ( <\n                                FileDropZone onFilesAdded = { handleFilesAdded }\n                                hasFiles = { false } >\n                                <\n                                EmptyState >\n                                <\n                                EmptyStateIcon > 🎵 < /EmptyStateIcon> <\n                                h2 > Drop Audio Files Here < /h2> <\n                                p > Drag and drop your audio files to start analyzing < /p> <\n                                p style = {\n                                    { fontSize: '12px', opacity: 0.5 }\n                                } >\n                                Supported: WAV, FLAC, MP3, AAC, OGG <\n                                /p> < /\n                                EmptyState > <\n                                /FileDropZone>\n                            ) : ( <\n                                    TracksContainer > {\n                                        audioFiles.map((track, index) => ( <\n                                            WaveformPlayer key = { track.id }\n                                            track = { track }\n                                            isActive = { currentTrack && currentTrack.id === track.id }\n                                            isPlaying = { isPlaying && currentTrack && currentTrack.id === track.id }\n                                            onTrackSelect = { handleTrackSelect }\n                                            onWaveformClick = { handleWaveformClick }\n                                            onTrackDelete = { handleTrackDelete }\n                                            onPlayPause = { handlePlayPause }\n                                            analysisData = { analysisData[track.id] }\n                                            currentTime = { currentTrack && currentTrack.id === track.id ? currentTime : 0 }\n                                            duration = { currentTrack && currentTrack.id === track.id ? duration : (analysisData[track.id] && analysisData[track.id].duration || 0) }\n                                            />\n                                        ))\n                                    } <\n                                    GlobalDropZone isDragActive = { isDragActive } > {\n                                        isDragActive && < h2 > Drop to Add More Files < /h2>} < /\n                                        GlobalDropZone > <\n                                        /TracksContainer>\n                                    )\n                                } <\n                                /WaveformContainer>\n\n                                <\n                                SidePanel >\n                                <\n                                ControlPanel\n                            currentTrack = { currentTrack }\n                            isPlaying = { isPlaying }\n                            masterVolume = { masterVolume }\n                            onPlayPause = { handlePlayPause }\n                            onVolumeChange = { setMasterVolume }\n                            onFilesAdded = { handleFilesAdded }\n                            analysisData = { currentTrack ? analysisData[currentTrack.id] : null }\n                            currentTime = { currentTime }\n                            duration = { duration }\n                            /> < /\n                            SidePanel > <\n                            /MainContent> < /\n                            AppContainer >\n                        );\n                }\n\n                export default App;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,WAAW,EAAEC,MAAM,EAAEC,SAAS,QAAQ,OAAO;AACvE,OAAOC,MAAM,MAAM,mBAAmB;AACtC,OAAOC,cAAc,MAAM,6BAA6B;AACxD,OAAOC,YAAY,MAAM,2BAA2B;AACpD,OAAOC,YAAY,MAAM,2BAA2B;AACpD,OAAOC,aAAa,MAAM,uBAAuB;AACjD,OAAOC,WAAW,MAAM,qBAAqB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE9C,MAAMC,YAAY,GAAGR,MAAM,CAACS,GAAI;AAChC;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,EAAA,GAPIF,YAAY;AASlB,MAAMG,MAAM,GAAGX,MAAM,CAACS,GAAI;AAC1B;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACG,GAAA,GAPID,MAAM;AASZ,MAAME,KAAK,GAAGb,MAAM,CAACc,EAAG;AACxB;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GALIF,KAAK;AAOX,MAAMG,WAAW,GAAGhB,MAAM,CAACS,GAAI;AAC/B;AACA;AACA;AACA,CAAC;AAACQ,GAAA,GAJID,WAAW;AAMjB,MAAME,iBAAiB,GAAGlB,MAAM,CAACS,GAAI;AACrC;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACU,GAAA,GAPID,iBAAiB;AASvB,MAAME,SAAS,GAAGpB,MAAM,CAACS,GAAI;AAC7B;AACA;AACA;AACA;AACA;AACA,CAAC;AAACY,GAAA,GANID,SAAS;AAQf,MAAME,UAAU,GAAGtB,MAAM,CAACS,GAAI;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACc,GAAA,GARID,UAAU;AAUhB,MAAME,cAAc,GAAGxB,MAAM,CAACS,GAAI;AAClC;AACA;AACA;AACA,CAAC;AAACgB,GAAA,GAJID,cAAc;AAMpB,MAAME,eAAe,GAAG1B,MAAM,CAACS,GAAI;AACnC;AACA,CAAC;AAACkB,GAAA,GAFID,eAAe;AAIrB,MAAME,cAAc,GAAG5B,MAAM,CAACS,GAAI;AAClC;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoBoB,KAAK,IAAIA,KAAK,CAACC,YAAY,GAAG,MAAM,GAAG,MAAM;AACjE,gBAAgBD,KAAK,IAAIA,KAAK,CAACC,YAAY,GAAG,wBAAwB,GAAG,aAAa;AACtF,uBAAuBD,KAAK,IAAIA,KAAK,CAACC,YAAY,GAAG,SAAS,GAAG,aAAa;AAC9E;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GAtBIH,cAAc;AAwBpB,SAASI,GAAGA,CAAA,EAAG;EAAAC,EAAA;EACX,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGvC,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACwC,YAAY,EAAEC,eAAe,CAAC,GAAGzC,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAAC0C,SAAS,EAAEC,YAAY,CAAC,GAAG3C,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAAC4C,YAAY,EAAEC,eAAe,CAAC,GAAG7C,QAAQ,CAAC,GAAG,CAAC;EACrD,MAAM,CAAC8C,YAAY,EAAEC,eAAe,CAAC,GAAG/C,QAAQ,CAAC,CAAC,CAAC,CAAC;EACpD,MAAM,CAACgD,WAAW,EAAEC,cAAc,CAAC,GAAGjD,QAAQ,CAAC,CAAC,CAAC;EACjD,MAAM,CAACkD,QAAQ,EAAEC,WAAW,CAAC,GAAGnD,QAAQ,CAAC,CAAC,CAAC;EAC3C,MAAM,CAACkC,YAAY,EAAEkB,eAAe,CAAC,GAAGpD,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACqD,SAAS,EAAEC,YAAY,CAAC,GAAGtD,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACuD,KAAK,EAAEC,QAAQ,CAAC,GAAGxD,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAACyD,cAAc,EAAEC,iBAAiB,CAAC,GAAG1D,QAAQ,CAAC,EAAE,CAAC;EAExD,MAAM2D,aAAa,GAAGzD,MAAM,CAAC,IAAIM,aAAa,CAAC,CAAC,CAAC;EACjD,MAAMoD,WAAW,GAAG1D,MAAM,CAAC,IAAIO,WAAW,CAAC,CAAC,CAAC;EAC7C,MAAMoD,aAAa,GAAG3D,MAAM,CAAC,CAAC,CAAC;EAC/B,MAAM4D,iBAAiB,GAAG5D,MAAM,CAAC,CAAC,CAAC;;EAEnC;EACA;;EAEA;EACA;;EAEA;EACAC,SAAS,CAAC,MAAM;IACZ,MAAM4D,MAAM,GAAGH,WAAW,CAACI,OAAO;IAElCD,MAAM,CAACE,YAAY,GAAG,CAACC,IAAI,EAAEC,GAAG,KAAK;MACjC;MACA,IAAI,OAAOD,IAAI,KAAK,QAAQ,IAAI,CAACE,KAAK,CAACF,IAAI,CAAC,IAAIA,IAAI,IAAI,CAAC,EAAE;QACvDjB,cAAc,CAACiB,IAAI,CAAC;MACxB;MACA,IAAI,OAAOC,GAAG,KAAK,QAAQ,IAAI,CAACC,KAAK,CAACD,GAAG,CAAC,IAAIA,GAAG,GAAG,CAAC,EAAE;QACnDhB,WAAW,CAACgB,GAAG,CAAC;MACpB;IACJ,CAAC;IAEDJ,MAAM,CAACM,OAAO,GAAG,MAAM;MACnBC,OAAO,CAACC,GAAG,CAAC,aAAa,CAAC;MAC1B5B,YAAY,CAAC,KAAK,CAAC;MACnBM,cAAc,CAAC,CAAC,CAAC;IACrB,CAAC;IAEDc,MAAM,CAACS,OAAO,GAAIjB,KAAK,IAAK;MACxBe,OAAO,CAACf,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAAC;MAC3CZ,YAAY,CAAC,KAAK,CAAC;MACnBW,YAAY,CAAC,KAAK,CAAC;MACnBE,QAAQ,CAAC,gBAAgBD,KAAK,CAACkB,OAAO,IAAI,eAAe,EAAE,CAAC;MAC5Df,iBAAiB,CAAC,EAAE,CAAC;IACzB,CAAC;IAEDK,MAAM,CAACW,WAAW,GAAG,MAAM;MACvBJ,OAAO,CAACC,GAAG,CAAC,oBAAoB,CAAC;MACjCjB,YAAY,CAAC,IAAI,CAAC;MAClBI,iBAAiB,CAAC,uBAAuB,CAAC;MAC1CF,QAAQ,CAAC,IAAI,CAAC;IAClB,CAAC;IAEDO,MAAM,CAACY,SAAS,GAAG,MAAM;MACrBL,OAAO,CAACC,GAAG,CAAC,kBAAkB,CAAC;MAC/BjB,YAAY,CAAC,KAAK,CAAC;MACnBI,iBAAiB,CAAC,EAAE,CAAC;IACzB,CAAC;;IAED;IACAK,MAAM,CAACa,SAAS,CAAChC,YAAY,CAAC;IAE9B,OAAO,MAAM;MACTmB,MAAM,CAACc,OAAO,CAAC,CAAC;IACpB,CAAC;EACL,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA1E,SAAS,CAAC,MAAM;IACZyD,WAAW,CAACI,OAAO,CAACY,SAAS,CAAChC,YAAY,CAAC;EAC/C,CAAC,EAAE,CAACA,YAAY,CAAC,CAAC;;EAElB;EACAzC,SAAS,CAAC,MAAM;IACZ,MAAM2E,aAAa,GAAIC,CAAC,IAAK;MACzB,IAAIA,CAAC,CAACC,IAAI,KAAK,OAAO,IAAID,CAAC,CAACE,MAAM,CAACC,OAAO,KAAK,OAAO,IAAIH,CAAC,CAACE,MAAM,CAACC,OAAO,KAAK,UAAU,EAAE;QACvFH,CAAC,CAACI,cAAc,CAAC,CAAC;QAClB,IAAI3C,YAAY,EAAE;UACd,IAAIE,SAAS,EAAE;YACXkB,WAAW,CAACI,OAAO,CAACoB,KAAK,CAAC,CAAC;YAC3BzC,YAAY,CAAC,KAAK,CAAC;UACvB,CAAC,MAAM;YACHiB,WAAW,CAACI,OAAO,CAACqB,IAAI,CAAC,CAAC;YAC1B1C,YAAY,CAAC,IAAI,CAAC;UACtB;QACJ;MACJ;IACJ,CAAC;IAED2C,QAAQ,CAACC,gBAAgB,CAAC,SAAS,EAAET,aAAa,CAAC;IACnD,OAAO,MAAM;MACTQ,QAAQ,CAACE,mBAAmB,CAAC,SAAS,EAAEV,aAAa,CAAC;IAC1D,CAAC;EACL,CAAC,EAAE,CAACtC,YAAY,EAAEE,SAAS,CAAC,CAAC;;EAE7B;EACAvC,SAAS,CAAC,MAAM;IACZ,MAAMsF,eAAe,GAAIV,CAAC,IAAK;MAC3BA,CAAC,CAACI,cAAc,CAAC,CAAC;MAClBJ,CAAC,CAACW,eAAe,CAAC,CAAC;MACnB,IAAIX,CAAC,CAACY,YAAY,CAACC,KAAK,IAAIb,CAAC,CAACY,YAAY,CAACC,KAAK,CAACC,MAAM,GAAG,CAAC,EAAE;QACzDzC,eAAe,CAAC,IAAI,CAAC;MACzB;IACJ,CAAC;IAED,MAAM0C,eAAe,GAAIf,CAAC,IAAK;MAC3BA,CAAC,CAACI,cAAc,CAAC,CAAC;MAClBJ,CAAC,CAACW,eAAe,CAAC,CAAC;MACnB,IAAI,CAACX,CAAC,CAACgB,aAAa,CAACC,QAAQ,CAACjB,CAAC,CAACkB,aAAa,CAAC,EAAE;QAC5C7C,eAAe,CAAC,KAAK,CAAC;MAC1B;IACJ,CAAC;IAED,MAAM8C,cAAc,GAAInB,CAAC,IAAK;MAC1BA,CAAC,CAACI,cAAc,CAAC,CAAC;MAClBJ,CAAC,CAACW,eAAe,CAAC,CAAC;IACvB,CAAC;IAED,MAAMS,UAAU,GAAG,MAAMpB,CAAC,IAAK;MAC3BA,CAAC,CAACI,cAAc,CAAC,CAAC;MAClBJ,CAAC,CAACW,eAAe,CAAC,CAAC;MACnBtC,eAAe,CAAC,KAAK,CAAC;MAEtB,MAAMgD,KAAK,GAAG,EAAE;MAChB,IAAIrB,CAAC,CAACY,YAAY,CAACS,KAAK,IAAIrB,CAAC,CAACY,YAAY,CAACS,KAAK,CAACP,MAAM,GAAG,CAAC,EAAE;QACzD,KAAK,IAAIQ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGtB,CAAC,CAACY,YAAY,CAACS,KAAK,CAACP,MAAM,EAAEQ,CAAC,EAAE,EAAE;UAClD,MAAMC,IAAI,GAAGvB,CAAC,CAACY,YAAY,CAACS,KAAK,CAACC,CAAC,CAAC;UACpC,IAAIE,WAAW,CAACD,IAAI,CAACE,IAAI,CAAC,EAAE;YACxBJ,KAAK,CAACK,IAAI,CAACH,IAAI,CAAC;UACpB;QACJ;MACJ;MAEA,IAAIF,KAAK,CAACP,MAAM,GAAG,CAAC,EAAE;QAClBa,gBAAgB,CAACN,KAAK,CAAC;MAC3B;IACJ,CAAC;IAEDd,QAAQ,CAACC,gBAAgB,CAAC,WAAW,EAAEE,eAAe,CAAC;IACvDH,QAAQ,CAACC,gBAAgB,CAAC,WAAW,EAAEO,eAAe,CAAC;IACvDR,QAAQ,CAACC,gBAAgB,CAAC,UAAU,EAAEW,cAAc,CAAC;IACrDZ,QAAQ,CAACC,gBAAgB,CAAC,MAAM,EAAEY,UAAU,CAAC;IAE7C,OAAO,MAAM;MACTb,QAAQ,CAACE,mBAAmB,CAAC,WAAW,EAAEC,eAAe,CAAC;MAC1DH,QAAQ,CAACE,mBAAmB,CAAC,WAAW,EAAEM,eAAe,CAAC;MAC1DR,QAAQ,CAACE,mBAAmB,CAAC,UAAU,EAAEU,cAAc,CAAC;MACxDZ,QAAQ,CAACE,mBAAmB,CAAC,MAAM,EAAEW,UAAU,CAAC;IACpD,CAAC;EACL,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMI,WAAW,GAAII,QAAQ,IAAK;IAC9B,MAAMC,eAAe,GAAG,CAAC,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC;IACjF,MAAMC,GAAG,GAAGF,QAAQ,CAACG,WAAW,CAAC,CAAC,CAACC,SAAS,CAACJ,QAAQ,CAACK,WAAW,CAAC,GAAG,CAAC,CAAC;IACvE,OAAOJ,eAAe,CAACK,QAAQ,CAACJ,GAAG,CAAC;EACxC,CAAC;;EAED;EACA,MAAMH,gBAAgB,GAAGzG,WAAW,CAAC,MAAMmG,KAAK,IAAK;IACjD,MAAMc,QAAQ,GAAG,EAAE;IAEnB,KAAK,MAAMZ,IAAI,IAAIF,KAAK,EAAE;MACtB,IAAI;QACA;QACA,IAAIe,QAAQ,EAAEC,QAAQ;QAEtB,IAAId,IAAI,YAAYe,IAAI,EAAE;UACtB;UACAF,QAAQ,GAAGb,IAAI,CAACE,IAAI;UACpBY,QAAQ,GAAGd,IAAI,CAAC,CAAC;QACrB,CAAC,MAAM,IAAI,OAAOA,IAAI,KAAK,QAAQ,EAAE;UACjC;UACAa,QAAQ,GAAGb,IAAI,CAACgB,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAAC,CAAC,CAACD,KAAK,CAAC,IAAI,CAAC,CAACC,GAAG,CAAC,CAAC,CAAC,CAAC;UACpDH,QAAQ,GAAGd,IAAI;QACnB,CAAC,MAAM;UACH;UACAa,QAAQ,GAAG,cAAc;UACzBC,QAAQ,GAAGd,IAAI;QACnB;;QAEA;QACA,MAAMkB,QAAQ,GAAG,MAAM7D,aAAa,CAACK,OAAO,CAACyD,WAAW,CAACL,QAAQ,CAAC;;QAElE;QACA,IAAI;UACA,MAAMxD,WAAW,CAACI,OAAO,CAAC0D,gBAAgB,CAACN,QAAQ,CAAC;UACpD9C,OAAO,CAACC,GAAG,CAAC,cAAc4C,QAAQ,EAAE,CAAC;QACzC,CAAC,CAAC,OAAO5D,KAAK,EAAE;UACZe,OAAO,CAACqD,IAAI,CAAC,qBAAqBR,QAAQ,GAAG,EAAE5D,KAAK,CAAC;QACzD;QAEA,MAAMqE,SAAS,GAAG;UACdC,EAAE,EAAEC,IAAI,CAACC,GAAG,CAAC,CAAC,GAAGC,IAAI,CAACC,MAAM,CAAC,CAAC;UAC9BzB,IAAI,EAAEW,QAAQ;UACde,IAAI,EAAEd,QAAQ;UACdI,QAAQ;UACRW,KAAK,EAAEC,mBAAmB,CAAC,CAAC;UAC5BC,YAAY,EAAEb,QAAQ,CAACa,YAAY,IAAI;QAC3C,CAAC;QAEDnB,QAAQ,CAACT,IAAI,CAACmB,SAAS,CAAC;;QAExB;QACA7E,eAAe,CAACuF,IAAI,KAAK;UACrB,GAAGA,IAAI;UACP,CAACV,SAAS,CAACC,EAAE,GAAGL;QACpB,CAAC,CAAC,CAAC;MAEP,CAAC,CAAC,OAAOjE,KAAK,EAAE;QACZe,OAAO,CAACf,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;;QAE7C;QACA,IAAI4D,QAAQ;QACZ,IAAIb,IAAI,YAAYe,IAAI,EAAE;UACtBF,QAAQ,GAAGb,IAAI,CAACE,IAAI;QACxB,CAAC,MAAM,IAAI,OAAOF,IAAI,KAAK,QAAQ,EAAE;UACjCa,QAAQ,GAAGb,IAAI,CAACgB,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAAC,CAAC,CAACD,KAAK,CAAC,IAAI,CAAC,CAACC,GAAG,CAAC,CAAC;QACtD,CAAC,MAAM;UACHJ,QAAQ,GAAG,cAAc;QAC7B;QAEA,MAAMS,SAAS,GAAG;UACdC,EAAE,EAAEC,IAAI,CAACC,GAAG,CAAC,CAAC,GAAGC,IAAI,CAACC,MAAM,CAAC,CAAC;UAC9BzB,IAAI,EAAEW,QAAQ;UACde,IAAI,EAAE5B,IAAI;UACVkB,QAAQ,EAAE,IAAI;UACdW,KAAK,EAAEC,mBAAmB,CAAC,CAAC;UAC5BC,YAAY,EAAE;QAClB,CAAC;QACDnB,QAAQ,CAACT,IAAI,CAACmB,SAAS,CAAC;MAC5B;IACJ;IAEArF,aAAa,CAAC+F,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAE,GAAGpB,QAAQ,CAAC,CAAC;;IAE7C;IACA,IAAI,CAAC1E,YAAY,IAAI0E,QAAQ,CAACrB,MAAM,GAAG,CAAC,EAAE;MACtCpD,eAAe,CAACyE,QAAQ,CAAC,CAAC,CAAC,CAAC;IAChC;EACJ,CAAC,EAAE,CAAC1E,YAAY,CAAC,CAAC;;EAElB;EACA,MAAM4F,mBAAmB,GAAGA,CAAA,KAAM;IAC9B,MAAMG,MAAM,GAAG,CACX,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAC1C,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,CAC7C;IACD,OAAOA,MAAM,CAACP,IAAI,CAACQ,KAAK,CAACR,IAAI,CAACC,MAAM,CAAC,CAAC,GAAGM,MAAM,CAAC1C,MAAM,CAAC,CAAC;EAC5D,CAAC;;EAED;EACA,MAAM4C,iBAAiB,GAAGxI,WAAW,CAAC,MAAMyI,KAAK,IAAK;IAClD,IAAIA,KAAK,CAACb,EAAE,MAAMrF,YAAY,IAAIA,YAAY,CAACqF,EAAE,CAAC,EAAE;MAChDvD,OAAO,CAACC,GAAG,CAAC,wBAAwB,CAAC;MACrC;IACJ;IAEA,IAAIlB,SAAS,EAAE;MACXiB,OAAO,CAACC,GAAG,CAAC,yCAAyC,CAAC;MACtD;IACJ;IAEA,IAAI;MACAD,OAAO,CAACC,GAAG,CAAC,kBAAkBmE,KAAK,CAAClC,IAAI,EAAE,CAAC;MAC3ClD,YAAY,CAAC,IAAI,CAAC;;MAElB;MACAM,WAAW,CAACI,OAAO,CAAC2E,IAAI,CAAC,CAAC;MAC1BhG,YAAY,CAAC,KAAK,CAAC;;MAEnB;MACA,MAAMiB,WAAW,CAACI,OAAO,CAAC4E,aAAa,CAACF,KAAK,CAACR,IAAI,CAAC;MACnDzF,eAAe,CAACiG,KAAK,CAAC;MACtBzF,cAAc,CAAC,CAAC,CAAC;MAEjBqB,OAAO,CAACC,GAAG,CAAC,8BAA8BmE,KAAK,CAAClC,IAAI,EAAE,CAAC;IAC3D,CAAC,CAAC,OAAOjD,KAAK,EAAE;MACZe,OAAO,CAACf,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC5C;MACAd,eAAe,CAAC,IAAI,CAAC;MACrBE,YAAY,CAAC,KAAK,CAAC;MACnBM,cAAc,CAAC,CAAC,CAAC;IACrB,CAAC,SAAS;MACNK,YAAY,CAAC,KAAK,CAAC;IACvB;EACJ,CAAC,EAAE,CAACd,YAAY,EAAEa,SAAS,CAAC,CAAC;;EAE7B;EACA,MAAMwF,eAAe,GAAG5I,WAAW,CAAC,MAAM;IACtC,IAAI,CAACuC,YAAY,EAAE;MACf8B,OAAO,CAACC,GAAG,CAAC,2BAA2B,CAAC;MACxC;IACJ;IAEA,IAAIlB,SAAS,EAAE;MACXiB,OAAO,CAACC,GAAG,CAAC,qCAAqC,CAAC;MAClD;IACJ;;IAEA;IACA,MAAMwD,GAAG,GAAGD,IAAI,CAACC,GAAG,CAAC,CAAC;IACtB,IAAIA,GAAG,GAAGjE,iBAAiB,CAACE,OAAO,GAAG,GAAG,EAAE;MACvCM,OAAO,CAACC,GAAG,CAAC,sBAAsB,CAAC;MACnC;IACJ;IACAT,iBAAiB,CAACE,OAAO,GAAG+D,GAAG;IAE/B,IAAI;MACA,MAAMhE,MAAM,GAAGH,WAAW,CAACI,OAAO;MAClC,MAAM8E,kBAAkB,GAAG/E,MAAM,CAACgF,YAAY,CAAC,CAAC;MAEhDzE,OAAO,CAACC,GAAG,CAAC,sCAAsCuE,kBAAkB,EAAE,CAAC;;MAEvE;MACA,IAAIA,kBAAkB,EAAE;QACpBxE,OAAO,CAACC,GAAG,CAAC,kBAAkB,CAAC;QAC/BR,MAAM,CAACqB,KAAK,CAAC,CAAC;QACdzC,YAAY,CAAC,KAAK,CAAC;QACnB2B,OAAO,CAACC,GAAG,CAAC,iBAAiB,CAAC;MAClC,CAAC,MAAM;QACHD,OAAO,CAACC,GAAG,CAAC,mBAAmB,CAAC;QAChC;QACA,IAAIR,MAAM,CAACiF,aAAa,EAAE;UACtBjF,MAAM,CAACsB,IAAI,CAAC,CAAC;UACb1C,YAAY,CAAC,IAAI,CAAC;UAClB2B,OAAO,CAACC,GAAG,CAAC,cAAc,CAAC;QAC/B,CAAC,MAAM;UACHD,OAAO,CAACC,GAAG,CAAC,gCAAgC,CAAC;UAC7C;UACAkE,iBAAiB,CAACjG,YAAY,CAAC;QACnC;MACJ;IACJ,CAAC,CAAC,OAAOe,KAAK,EAAE;MACZe,OAAO,CAACf,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC5C;MACAZ,YAAY,CAAC,KAAK,CAAC;IACvB;EACJ,CAAC,EAAE,CAACD,SAAS,EAAEF,YAAY,EAAEa,SAAS,EAAEoF,iBAAiB,CAAC,CAAC;;EAE3D;EACA,MAAMQ,mBAAmB,GAAGhJ,WAAW,CAAC,OAAMyI,KAAK,EAAEQ,QAAQ,KAAK;IAC9D,IAAI,CAACR,KAAK,IAAI,OAAOQ,QAAQ,KAAK,QAAQ,IAAIA,QAAQ,GAAG,CAAC,IAAIA,QAAQ,GAAG,CAAC,EAAE;MACxE5E,OAAO,CAACf,KAAK,CAAC,mCAAmC,CAAC;MAClD;IACJ;;IAEA;IACA,MAAMwE,GAAG,GAAGD,IAAI,CAACC,GAAG,CAAC,CAAC;IACtB,IAAIA,GAAG,GAAGlE,aAAa,CAACG,OAAO,GAAG,GAAG,EAAE;MACnCM,OAAO,CAACC,GAAG,CAAC,iBAAiB,CAAC;MAC9B;IACJ;IACAV,aAAa,CAACG,OAAO,GAAG+D,GAAG;IAE3BzD,OAAO,CAACC,GAAG,CAAC,qBAAqBmE,KAAK,CAAClC,IAAI,gBAAgB0C,QAAQ,CAACC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC;IAEjF,IAAI;MACA;MACA,IAAIT,KAAK,CAACb,EAAE,MAAMrF,YAAY,IAAIA,YAAY,CAACqF,EAAE,CAAC,EAAE;QAChD,MAAMY,iBAAiB,CAACC,KAAK,CAAC;QAC9B;QACA,MAAM,IAAIU,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,GAAG,CAAC,CAAC;MAC1D;;MAEA;MACA,MAAMtF,MAAM,GAAGH,WAAW,CAACI,OAAO;MAClC,IAAI,CAACD,MAAM,CAACiF,aAAa,EAAE;QACvB1E,OAAO,CAACqD,IAAI,CAAC,+BAA+B,CAAC;QAC7C;MACJ;;MAEA;MACA,MAAM4B,aAAa,GAAGzG,YAAY,CAAC4F,KAAK,CAACb,EAAE,CAAC;MAC5C,MAAM2B,aAAa,GAAGD,aAAa,IAAIA,aAAa,CAACrG,QAAQ,IAAIa,MAAM,CAAC0F,WAAW,CAAC,CAAC,IAAI,CAAC;MAE1F,IAAID,aAAa,GAAG,CAAC,EAAE;QACnB,MAAME,QAAQ,GAAG1B,IAAI,CAAC2B,GAAG,CAAC,CAAC,EAAE3B,IAAI,CAAC4B,GAAG,CAACJ,aAAa,EAAEN,QAAQ,GAAGM,aAAa,CAAC,CAAC;QAC/ElF,OAAO,CAACC,GAAG,CAAC,eAAemF,QAAQ,CAACP,OAAO,CAAC,CAAC,CAAC,QAAQK,aAAa,CAACL,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC;;QAElF;QACA,MAAMU,UAAU,GAAG9F,MAAM,CAACgF,YAAY,CAAC,CAAC;;QAExC;QACAhF,MAAM,CAAC+F,IAAI,CAACJ,QAAQ,CAAC;QACrBzG,cAAc,CAACyG,QAAQ,CAAC;;QAExB;QACA,IAAIG,UAAU,EAAE;UACZ;UACAP,UAAU,CAAC,MAAM;YACb,IAAIvF,MAAM,CAACgF,YAAY,CAAC,CAAC,EAAE;cACvBpG,YAAY,CAAC,IAAI,CAAC;YACtB;UACJ,CAAC,EAAE,EAAE,CAAC;QACV,CAAC,MAAM;UACH;UACAA,YAAY,CAAC,KAAK,CAAC;QACvB;;QAEA;QACA2G,UAAU,CAACS,uBAAuB,EAAE,GAAG,CAAC;MAC5C,CAAC,MAAM;QACHzF,OAAO,CAACqD,IAAI,CAAC,mCAAmC,CAAC;MACrD;IACJ,CAAC,CAAC,OAAOpE,KAAK,EAAE;MACZe,OAAO,CAACf,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;IACpD;EACJ,CAAC,EAAE,CAACf,YAAY,EAAEM,YAAY,EAAE2F,iBAAiB,EAAE/F,SAAS,CAAC,CAAC;;EAE9D;EACA,MAAMsH,iBAAiB,GAAG/J,WAAW,CAAEgK,aAAa,IAAK;IACrD3F,OAAO,CAACC,GAAG,CAAC,mBAAmB0F,aAAa,CAACzD,IAAI,EAAE,CAAC;;IAEpD;IACA,IAAIhE,YAAY,IAAIA,YAAY,CAACqF,EAAE,KAAKoC,aAAa,CAACpC,EAAE,EAAE;MACtDjE,WAAW,CAACI,OAAO,CAAC2E,IAAI,CAAC,CAAC;MAC1BhG,YAAY,CAAC,KAAK,CAAC;MACnBF,eAAe,CAAC,IAAI,CAAC;MACrBQ,cAAc,CAAC,CAAC,CAAC;MACjBE,WAAW,CAAC,CAAC,CAAC;IAClB;;IAEA;IACAZ,aAAa,CAAC+F,IAAI,IAAIA,IAAI,CAAC4B,MAAM,CAACxB,KAAK,IAAIA,KAAK,CAACb,EAAE,KAAKoC,aAAa,CAACpC,EAAE,CAAC,CAAC;;IAE1E;IACA9E,eAAe,CAACuF,IAAI,IAAI;MACpB,MAAM6B,OAAO,GAAG;QAAC,GAAG7B;MAAK,CAAC;MAC1B,OAAO6B,OAAO,CAACF,aAAa,CAACpC,EAAE,CAAC;MAChC,OAAOsC,OAAO;IAClB,CAAC,CAAC;EACN,CAAC,EAAE,CAAC3H,YAAY,CAAC,CAAC;EAElB,oBAAS7B,OAAA,CACDC,YAAY;IAAAwJ,QAAA,gBACZzJ,OAAA,CACAI,MAAM;MAAAqJ,QAAA,gBACNzJ,OAAA,CACAM,KAAK;QAAAmJ,QAAA,EAAE;MAAc;QAAAjD,QAAA,EAAAkD,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,KAAC,eAAA5J,OAAA;QAC3B6J,KAAK,EACL;UAAEC,QAAQ,EAAE,MAAM;UAAEC,OAAO,EAAE;QAAI,CACpC;QAAAN,QAAA,EAAE;MAC4B;QAAAjD,QAAA,EAAAkD,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3B,CAAC,KAAC,EACFhH,KAAK,iBAAI5C,OAAA;QAAM6J,KAAK,EAChB;UAAErC,KAAK,EAAE,SAAS;UAAEsC,QAAQ,EAAE,MAAM;UAAEE,SAAS,EAAE;QAAM,CAAG;QAAAP,QAAA,GAAE,eAAG,EAAE7G,KAAK,EAAE,GAAC;MAAA;QAAA4D,QAAA,EAAAkD,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,EAAC,GAAC,EAClFlH,SAAS,iBAAI1C,OAAA;QAAM6J,KAAK,EAChB;UAAErC,KAAK,EAAE,SAAS;UAAEsC,QAAQ,EAAE,MAAM;UAAEE,SAAS,EAAE;QAAM,CAAG;QAAAP,QAAA,GAAE,SAAE,EAAE3G,cAAc,EAAE,GAAC;MAAA;QAAA0D,QAAA,EAAAkD,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,EAAC,GAAC;IAAA;MAAApD,QAAA,EAAAkD,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAE3F,CAAC,eAEJ5J,OAAA,CACAS,WAAW;MAAAgJ,QAAA,gBACXzJ,OAAA,CACAW,iBAAiB;QAAA8I,QAAA,GAAE,GAAC,EAChB9H,UAAU,CAACuD,MAAM,KAAK,CAAC,gBAAKlF,OAAA,CACxBJ,YAAY;UAACqK,YAAY,EAAKlE,gBAAkB;UAChDmE,QAAQ,EAAK,KAAO;UAAAT,QAAA,gBACpBzJ,OAAA,CACAe,UAAU;YAAA0I,QAAA,gBACVzJ,OAAA,CACAiB,cAAc;cAAAwI,QAAA,EAAE;YAAI;cAAAjD,QAAA,EAAAkD,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAiB,CAAC,KAAC,eAAA5J,OAAA;cAAAyJ,QAAA,EACnC;YAAuB;cAAAjD,QAAA,EAAAkD,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,KAAC,eAAA5J,OAAA;cAAAyJ,QAAA,EAC/B;YAAmD;cAAAjD,QAAA,EAAAkD,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,KAAC,eAAA5J,OAAA;cAC1D6J,KAAK,EACH;gBAAEC,QAAQ,EAAE,MAAM;gBAAEC,OAAO,EAAE;cAAI,CACpC;cAAAN,QAAA,EAAE;YACiC;cAAAjD,QAAA,EAAAkD,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClC,CAAC,KAAC;UAAA;YAAApD,QAAA,EAAAkD,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACO,CAAC,KAAC;QAAA;UAAApD,QAAA,EAAAkD,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,gBACZ5J,OAAA,CACEmB,eAAe;UAAAsI,QAAA,GAAE,GAAC,EACd9H,UAAU,CAACwI,GAAG,CAAC,CAACpC,KAAK,EAAEqC,KAAK,kBAAOpK,OAAA,CAC/BN,cAAc;YACdqI,KAAK,EAAKA,KAAO;YACjBsC,QAAQ,EAAKxI,YAAY,IAAIA,YAAY,CAACqF,EAAE,KAAKa,KAAK,CAACb,EAAI;YAC3DnF,SAAS,EAAKA,SAAS,IAAIF,YAAY,IAAIA,YAAY,CAACqF,EAAE,KAAKa,KAAK,CAACb,EAAI;YACzEoD,aAAa,EAAKxC,iBAAmB;YACrCyC,eAAe,EAAKjC,mBAAqB;YACzCkC,aAAa,EAAKnB,iBAAmB;YACrCoB,WAAW,EAAKvC,eAAiB;YACjC/F,YAAY,EAAKA,YAAY,CAAC4F,KAAK,CAACb,EAAE,CAAG;YACzC7E,WAAW,EAAKR,YAAY,IAAIA,YAAY,CAACqF,EAAE,KAAKa,KAAK,CAACb,EAAE,GAAG7E,WAAW,GAAG,CAAG;YAChFE,QAAQ,EAAKV,YAAY,IAAIA,YAAY,CAACqF,EAAE,KAAKa,KAAK,CAACb,EAAE,GAAG3E,QAAQ,GAAIJ,YAAY,CAAC4F,KAAK,CAACb,EAAE,CAAC,IAAI/E,YAAY,CAAC4F,KAAK,CAACb,EAAE,CAAC,CAAC3E,QAAQ,IAAI;UAAI,GAVlHwF,KAAK,CAACb,EAAE;YAAAV,QAAA,EAAAkD,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAW9B,CACJ,CAAC,EACL,GAAC,eAAA5J,OAAA,CACFqB,cAAc;YAACE,YAAY,EAAKA,YAAc;YAAAkI,QAAA,GAAE,GAAC,EAC7ClI,YAAY,iBAAIvB,OAAA;cAAAyJ,QAAA,EAAM;YAAwB;cAAAjD,QAAA,EAAAkD,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,EAAC,GAAC;UAAA;YAAApD,QAAA,EAAAkD,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvC,CAAC,KAAC;QAAA;UAAApD,QAAA,EAAAkD,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CACnB,EACJ,GAAC;MAAA;QAAApD,QAAA,EAAAkD,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACgB,CAAC,eAEnB5J,OAAA,CACAa,SAAS;QAAA4I,QAAA,gBACTzJ,OAAA,CACAL,YAAY;UAChBkC,YAAY,EAAKA,YAAc;UAC/BE,SAAS,EAAKA,SAAW;UACzBE,YAAY,EAAKA,YAAc;UAC/BwI,WAAW,EAAKvC,eAAiB;UACjCwC,cAAc,EAAKxI,eAAiB;UACpC+H,YAAY,EAAKlE,gBAAkB;UACnC5D,YAAY,EAAKN,YAAY,GAAGM,YAAY,CAACN,YAAY,CAACqF,EAAE,CAAC,GAAG,IAAM;UACtE7E,WAAW,EAAKA,WAAa;UAC7BE,QAAQ,EAAKA;QAAU;UAAAiE,QAAA,EAAAkD,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtB,CAAC,KAAC;MAAA;QAAApD,QAAA,EAAAkD,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACO,CAAC,KAAC;IAAA;MAAApD,QAAA,EAAAkD,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,KAAC;EAAA;IAAApD,QAAA,EAAAkD,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACD,CAAC;AAE1B;AAAClI,EAAA,CAxgBRD,GAAG;AAAAkJ,GAAA,GAAHlJ,GAAG;AA0gBI,eAAeA,GAAG;AAAC,IAAAtB,EAAA,EAAAE,GAAA,EAAAG,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAI,GAAA,EAAAmJ,GAAA;AAAAC,YAAA,CAAAzK,EAAA;AAAAyK,YAAA,CAAAvK,GAAA;AAAAuK,YAAA,CAAApK,GAAA;AAAAoK,YAAA,CAAAlK,GAAA;AAAAkK,YAAA,CAAAhK,GAAA;AAAAgK,YAAA,CAAA9J,GAAA;AAAA8J,YAAA,CAAA5J,GAAA;AAAA4J,YAAA,CAAA1J,GAAA;AAAA0J,YAAA,CAAAxJ,GAAA;AAAAwJ,YAAA,CAAApJ,GAAA;AAAAoJ,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}