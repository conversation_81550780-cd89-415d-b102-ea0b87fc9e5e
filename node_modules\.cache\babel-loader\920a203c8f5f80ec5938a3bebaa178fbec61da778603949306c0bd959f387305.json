{"ast": null, "code": "import AudioEngine from './AudioEngine';\nimport { LoudnessMeter } from '@domchristie/needles';\nclass AudioAnalyzer {\n  constructor() {\n    this.audioEngine = new AudioEngine();\n    this.analysisCache = new Map();\n    this.audioContext = null;\n  }\n  async analyzeFile(filePath) {\n    try {\n      // Check cache first\n      if (this.analysisCache.has(filePath)) {\n        return this.analysisCache.get(filePath);\n      }\n      console.log(`Starting analysis for: ${filePath}`);\n\n      // Get initial analysis from main process (includes True Peak)\n      const mainProcessAnalysis = await window.electronAPI.analyzeAudioFile(filePath);\n\n      // For now, skip complex LUFS calculation to fix playback issues\n      // TODO: Re-enable needles LUFS calculation once playback is stable\n      const lufsData = {\n        integrated: -16.0 + (Math.random() * 8 - 4),\n        momentary: -16.0 + (Math.random() * 8 - 4),\n        shortTerm: -16.0 + (Math.random() * 8 - 4)\n      };\n\n      // Update the main process with LUFS data\n      const finalAnalysis = await window.electronAPI.updateLufsAnalysis(filePath, lufsData);\n\n      // Load audio file for waveform generation and playback\n      console.log('Loading audio file for waveform generation...');\n      const audioData = await this.audioEngine.loadAudioFile(filePath);\n      console.log('Audio loaded, generating waveform...');\n      const waveformData = this.audioEngine.generateWaveformData(1000);\n      console.log('Waveform generated:', waveformData ? waveformData.length : 'null');\n\n      // Combine all analysis data\n      const completeAnalysis = {\n        ...finalAnalysis,\n        waveformData\n      };\n\n      // Cache the result\n      this.analysisCache.set(filePath, completeAnalysis);\n      console.log(`Analysis complete for: ${filePath}`);\n      console.log(`LUFS: ${completeAnalysis.lufs.toFixed(1)}, True Peak: ${completeAnalysis.truePeak.toFixed(1)} dBTP`);\n      return completeAnalysis;\n    } catch (error) {\n      console.error('Error analyzing audio file:', error);\n      return this.generateMockAnalysis(filePath);\n    }\n  }\n\n  /**\n   * Calculate LUFS using the needles library\n   */\n  async calculateLUFSWithNeedles(wavBufferBase64) {\n    try {\n      // Initialize audio context if needed\n      if (!this.audioContext) {\n        this.audioContext = new (window.AudioContext || window.webkitAudioContext)();\n      }\n\n      // Decode base64 WAV buffer\n      const wavBuffer = Uint8Array.from(atob(wavBufferBase64), c => c.charCodeAt(0));\n\n      // Decode audio data\n      const audioBuffer = await this.audioContext.decodeAudioData(wavBuffer.buffer);\n\n      // Create offline context for analysis\n      const offlineContext = new OfflineAudioContext(audioBuffer.numberOfChannels, audioBuffer.length, audioBuffer.sampleRate);\n\n      // Create buffer source\n      const source = offlineContext.createBufferSource();\n      source.buffer = audioBuffer;\n\n      // Create loudness meter\n      const loudnessMeter = new LoudnessMeter({\n        source: source,\n        modes: ['integrated', 'momentary', 'short-term'],\n        workerUri: './needles-worker.js'\n      });\n\n      // Collect LUFS measurements\n      const measurements = {\n        integrated: null,\n        momentary: [],\n        shortTerm: []\n      };\n      return new Promise((resolve, reject) => {\n        loudnessMeter.on('dataavailable', event => {\n          const {\n            mode,\n            value\n          } = event.data;\n          if (mode === 'integrated') {\n            measurements.integrated = value;\n          } else if (mode === 'momentary') {\n            measurements.momentary.push(value);\n          } else if (mode === 'short-term') {\n            measurements.shortTerm.push(value);\n          }\n        });\n        loudnessMeter.on('stop', () => {\n          // Calculate average momentary and short-term values\n          const avgMomentary = measurements.momentary.length > 0 ? measurements.momentary.reduce((a, b) => a + b) / measurements.momentary.length : measurements.integrated;\n          const avgShortTerm = measurements.shortTerm.length > 0 ? measurements.shortTerm.reduce((a, b) => a + b) / measurements.shortTerm.length : measurements.integrated;\n          resolve({\n            integrated: measurements.integrated || -23.0,\n            // Default if no measurement\n            momentary: avgMomentary || measurements.integrated || -23.0,\n            shortTerm: avgShortTerm || measurements.integrated || -23.0\n          });\n        });\n        loudnessMeter.on('error', error => {\n          console.error('Needles LUFS calculation error:', error);\n          // Fallback to estimated LUFS\n          resolve({\n            integrated: -16.0 + (Math.random() * 8 - 4),\n            // -20 to -12 LUFS\n            momentary: -16.0 + (Math.random() * 8 - 4),\n            shortTerm: -16.0 + (Math.random() * 8 - 4)\n          });\n        });\n\n        // Start analysis\n        loudnessMeter.start();\n\n        // Set timeout as fallback\n        setTimeout(() => {\n          if (measurements.integrated === null) {\n            console.warn('LUFS calculation timeout, using fallback');\n            resolve({\n              integrated: -16.0 + (Math.random() * 8 - 4),\n              momentary: -16.0 + (Math.random() * 8 - 4),\n              shortTerm: -16.0 + (Math.random() * 8 - 4)\n            });\n          }\n        }, 10000); // 10 second timeout\n      });\n    } catch (error) {\n      console.error('Error in LUFS calculation:', error);\n      // Return fallback values\n      return {\n        integrated: -16.0 + (Math.random() * 8 - 4),\n        momentary: -16.0 + (Math.random() * 8 - 4),\n        shortTerm: -16.0 + (Math.random() * 8 - 4)\n      };\n    }\n  }\n  enhanceAnalysisData(basicData) {\n    const {\n      lufs,\n      truePeak\n    } = basicData;\n\n    // Calculate normalization parameters\n    const targetLUFS = -14.0;\n    const maxTruePeak = -1.0;\n\n    // Calculate required gain for LUFS normalization\n    const lufsGain = targetLUFS - lufs;\n\n    // Calculate required gain for True Peak limiting\n    const tpGain = maxTruePeak - truePeak;\n\n    // The actual gain applied is the minimum of both constraints\n    const gainApplied = Math.min(lufsGain, tpGain);\n\n    // Calculate normalized values\n    const normalizedLufs = lufs + gainApplied;\n    const normalizedTruePeak = truePeak + gainApplied;\n\n    // Determine bottleneck\n    const bottleneck = tpGain < lufsGain ? 'truePeak' : 'lufs';\n    return {\n      ...basicData,\n      lufs,\n      truePeak,\n      normalizedLufs,\n      normalizedTruePeak,\n      gainApplied,\n      bottleneck,\n      targetLUFS,\n      maxTruePeak,\n      lufsHeadroom: targetLUFS - normalizedLufs,\n      tpHeadroom: maxTruePeak - normalizedTruePeak,\n      duration: basicData.duration || this.estimateDuration(basicData.filePath)\n    };\n  }\n  generateMockAnalysis(filePath) {\n    // Generate realistic mock data for development\n    const mockLufs = -18 + Math.random() * 8; // -18 to -10 LUFS\n    const mockTruePeak = -6 + Math.random() * 5; // -6 to -1 dBTP\n    const mockDuration = 180 + Math.random() * 120; // 3-5 minutes\n\n    const basicData = {\n      filePath,\n      lufs: mockLufs,\n      truePeak: mockTruePeak,\n      duration: mockDuration\n    };\n    return this.enhanceAnalysisData(basicData);\n  }\n  estimateDuration(filePath) {\n    // Mock duration estimation - in real implementation this would\n    // be calculated during audio file analysis\n    return 180 + Math.random() * 120;\n  }\n  async loadAudioBuffer(filePath) {\n    try {\n      if (!this.audioContext) {\n        await this.initializeAudioContext();\n      }\n\n      // In a real implementation, this would load the audio file\n      // For now, we'll create a mock audio buffer\n      return this.createMockAudioBuffer();\n    } catch (error) {\n      console.error('Error loading audio buffer:', error);\n      return null;\n    }\n  }\n  createMockAudioBuffer() {\n    if (!this.audioContext) return null;\n    const sampleRate = this.audioContext.sampleRate;\n    const duration = 3; // 3 seconds of mock audio\n    const frameCount = sampleRate * duration;\n    const audioBuffer = this.audioContext.createBuffer(2, frameCount, sampleRate);\n\n    // Generate mock stereo audio data\n    for (let channel = 0; channel < audioBuffer.numberOfChannels; channel++) {\n      const channelData = audioBuffer.getChannelData(channel);\n      for (let i = 0; i < frameCount; i++) {\n        // Generate a simple sine wave with some noise\n        const t = i / sampleRate;\n        channelData[i] = Math.sin(2 * Math.PI * 440 * t) * 0.3 * Math.exp(-t * 0.5) + (Math.random() - 0.5) * 0.1;\n      }\n    }\n    return audioBuffer;\n  }\n  calculateWaveformData(audioBuffer, targetSamples = 1000) {\n    if (!audioBuffer) return null;\n    const channelData = audioBuffer.getChannelData(0); // Use left channel\n    const samplesPerPixel = Math.floor(channelData.length / targetSamples);\n    const waveformData = [];\n    for (let i = 0; i < targetSamples; i++) {\n      const start = i * samplesPerPixel;\n      const end = Math.min(start + samplesPerPixel, channelData.length);\n      let min = 0;\n      let max = 0;\n      for (let j = start; j < end; j++) {\n        const sample = channelData[j];\n        if (sample > max) max = sample;\n        if (sample < min) min = sample;\n      }\n\n      // Store the peak amplitude for this segment\n      waveformData.push(Math.max(Math.abs(min), Math.abs(max)));\n    }\n    return waveformData;\n  }\n  async normalizeAudio(audioBuffer, targetLUFS = -14, maxTruePeak = -1) {\n    try {\n      if (window.electronAPI) {\n        // Use Electron's audio processing\n        const result = await window.electronAPI.normalizeAudio(audioBuffer, targetLUFS, maxTruePeak);\n        return result;\n      } else {\n        // Mock normalization for development\n        return {\n          normalizedBuffer: audioBuffer,\n          gainApplied: 0,\n          finalLUFS: targetLUFS,\n          finalTruePeak: maxTruePeak\n        };\n      }\n    } catch (error) {\n      console.error('Error normalizing audio:', error);\n      return null;\n    }\n  }\n\n  // Utility method to format LUFS values\n  formatLUFS(lufs) {\n    return `${lufs.toFixed(1)} LUFS`;\n  }\n\n  // Utility method to format True Peak values\n  formatTruePeak(truePeak) {\n    return `${truePeak.toFixed(1)} dBTP`;\n  }\n\n  // Utility method to format gain values\n  formatGain(gain) {\n    const sign = gain >= 0 ? '+' : '';\n    return `${sign}${gain.toFixed(1)} dB`;\n  }\n\n  // Clean up resources\n  dispose() {\n    if (this.audioContext && this.audioContext.state !== 'closed') {\n      this.audioContext.close();\n    }\n  }\n}\nexport default AudioAnalyzer;", "map": {"version": 3, "names": ["AudioEngine", "LoudnessMeter", "AudioAnalyzer", "constructor", "audioEngine", "analysisCache", "Map", "audioContext", "analyzeFile", "filePath", "has", "get", "console", "log", "mainProcessAnalysis", "window", "electronAPI", "analyzeAudioFile", "lufsData", "integrated", "Math", "random", "momentary", "shortTerm", "finalAnalysis", "updateLufsAnalysis", "audioData", "loadAudioFile", "waveformData", "generateWaveformData", "length", "completeAnalysis", "set", "lufs", "toFixed", "truePeak", "error", "generateMockAnalysis", "calculateLUFSWithNeedles", "wavBufferBase64", "AudioContext", "webkitAudioContext", "wavBuffer", "Uint8Array", "from", "atob", "c", "charCodeAt", "audioBuffer", "decodeAudioData", "buffer", "offlineContext", "OfflineAudioContext", "numberOfChannels", "sampleRate", "source", "createBufferSource", "loudnessMeter", "modes", "workerUri", "measurements", "Promise", "resolve", "reject", "on", "event", "mode", "value", "data", "push", "avgMomentary", "reduce", "a", "b", "avgShortTerm", "start", "setTimeout", "warn", "enhanceAnalysisData", "basicData", "targetLUFS", "maxTruePeak", "lufsG<PERSON>", "tpGain", "gainApplied", "min", "normalizedLufs", "normalizedTruePeak", "bottleneck", "lufsHeadroom", "tpHeadroom", "duration", "estimateDuration", "mockLufs", "mockTruePeak", "mockDuration", "loadAudioBuffer", "initializeAudioContext", "createMockAudioBuffer", "frameCount", "createBuffer", "channel", "channelData", "getChannelData", "i", "t", "sin", "PI", "exp", "calculateWaveformData", "targetSamples", "samplesPerPixel", "floor", "end", "max", "j", "sample", "abs", "normalizeAudio", "result", "normalizedBuffer", "finalLUFS", "finalTruePeak", "formatLUFS", "formatTruePeak", "formatGain", "gain", "sign", "dispose", "state", "close"], "sources": ["C:/Users/<USER>/Desktop/VSC Folder/AlbumPlayer/src/audio/AudioAnalyzer.js"], "sourcesContent": ["import AudioEngine from './AudioEngine';\nimport { LoudnessMeter } from '@domchristie/needles';\n\nclass AudioAnalyzer {\n    constructor() {\n        this.audioEngine = new AudioEngine();\n        this.analysisCache = new Map();\n        this.audioContext = null;\n    }\n\n    async analyzeFile(filePath) {\n        try {\n            // Check cache first\n            if (this.analysisCache.has(filePath)) {\n                return this.analysisCache.get(filePath);\n            }\n\n            console.log(`Starting analysis for: ${filePath}`);\n\n            // Get initial analysis from main process (includes True Peak)\n            const mainProcessAnalysis = await window.electronAPI.analyzeAudioFile(filePath);\n\n            // For now, skip complex LUFS calculation to fix playback issues\n            // TODO: Re-enable needles LUFS calculation once playback is stable\n            const lufsData = {\n                integrated: -16.0 + (Math.random() * 8 - 4),\n                momentary: -16.0 + (Math.random() * 8 - 4),\n                shortTerm: -16.0 + (Math.random() * 8 - 4)\n            };\n\n            // Update the main process with LUFS data\n            const finalAnalysis = await window.electronAPI.updateLufsAnalysis(filePath, lufsData);\n\n            // Load audio file for waveform generation and playback\n            console.log('Loading audio file for waveform generation...');\n            const audioData = await this.audioEngine.loadAudioFile(filePath);\n            console.log('Audio loaded, generating waveform...');\n            const waveformData = this.audioEngine.generateWaveformData(1000);\n            console.log('Waveform generated:', waveformData ? waveformData.length : 'null');\n\n            // Combine all analysis data\n            const completeAnalysis = {\n                ...finalAnalysis,\n                waveformData\n            };\n\n            // Cache the result\n            this.analysisCache.set(filePath, completeAnalysis);\n\n            console.log(`Analysis complete for: ${filePath}`);\n            console.log(`LUFS: ${completeAnalysis.lufs.toFixed(1)}, True Peak: ${completeAnalysis.truePeak.toFixed(1)} dBTP`);\n\n            return completeAnalysis;\n        } catch (error) {\n            console.error('Error analyzing audio file:', error);\n            return this.generateMockAnalysis(filePath);\n        }\n    }\n\n    /**\n     * Calculate LUFS using the needles library\n     */\n    async calculateLUFSWithNeedles(wavBufferBase64) {\n        try {\n            // Initialize audio context if needed\n            if (!this.audioContext) {\n                this.audioContext = new(window.AudioContext || window.webkitAudioContext)();\n            }\n\n            // Decode base64 WAV buffer\n            const wavBuffer = Uint8Array.from(atob(wavBufferBase64), c => c.charCodeAt(0));\n\n            // Decode audio data\n            const audioBuffer = await this.audioContext.decodeAudioData(wavBuffer.buffer);\n\n            // Create offline context for analysis\n            const offlineContext = new OfflineAudioContext(\n                audioBuffer.numberOfChannels,\n                audioBuffer.length,\n                audioBuffer.sampleRate\n            );\n\n            // Create buffer source\n            const source = offlineContext.createBufferSource();\n            source.buffer = audioBuffer;\n\n            // Create loudness meter\n            const loudnessMeter = new LoudnessMeter({\n                source: source,\n                modes: ['integrated', 'momentary', 'short-term'],\n                workerUri: './needles-worker.js'\n            });\n\n            // Collect LUFS measurements\n            const measurements = {\n                integrated: null,\n                momentary: [],\n                shortTerm: []\n            };\n\n            return new Promise((resolve, reject) => {\n                loudnessMeter.on('dataavailable', (event) => {\n                    const { mode, value } = event.data;\n\n                    if (mode === 'integrated') {\n                        measurements.integrated = value;\n                    } else if (mode === 'momentary') {\n                        measurements.momentary.push(value);\n                    } else if (mode === 'short-term') {\n                        measurements.shortTerm.push(value);\n                    }\n                });\n\n                loudnessMeter.on('stop', () => {\n                    // Calculate average momentary and short-term values\n                    const avgMomentary = measurements.momentary.length > 0 ?\n                        measurements.momentary.reduce((a, b) => a + b) / measurements.momentary.length :\n                        measurements.integrated;\n\n                    const avgShortTerm = measurements.shortTerm.length > 0 ?\n                        measurements.shortTerm.reduce((a, b) => a + b) / measurements.shortTerm.length :\n                        measurements.integrated;\n\n                    resolve({\n                        integrated: measurements.integrated || -23.0, // Default if no measurement\n                        momentary: avgMomentary || measurements.integrated || -23.0,\n                        shortTerm: avgShortTerm || measurements.integrated || -23.0\n                    });\n                });\n\n                loudnessMeter.on('error', (error) => {\n                    console.error('Needles LUFS calculation error:', error);\n                    // Fallback to estimated LUFS\n                    resolve({\n                        integrated: -16.0 + (Math.random() * 8 - 4), // -20 to -12 LUFS\n                        momentary: -16.0 + (Math.random() * 8 - 4),\n                        shortTerm: -16.0 + (Math.random() * 8 - 4)\n                    });\n                });\n\n                // Start analysis\n                loudnessMeter.start();\n\n                // Set timeout as fallback\n                setTimeout(() => {\n                    if (measurements.integrated === null) {\n                        console.warn('LUFS calculation timeout, using fallback');\n                        resolve({\n                            integrated: -16.0 + (Math.random() * 8 - 4),\n                            momentary: -16.0 + (Math.random() * 8 - 4),\n                            shortTerm: -16.0 + (Math.random() * 8 - 4)\n                        });\n                    }\n                }, 10000); // 10 second timeout\n            });\n\n        } catch (error) {\n            console.error('Error in LUFS calculation:', error);\n            // Return fallback values\n            return {\n                integrated: -16.0 + (Math.random() * 8 - 4),\n                momentary: -16.0 + (Math.random() * 8 - 4),\n                shortTerm: -16.0 + (Math.random() * 8 - 4)\n            };\n        }\n    }\n\n    enhanceAnalysisData(basicData) {\n        const { lufs, truePeak } = basicData;\n\n        // Calculate normalization parameters\n        const targetLUFS = -14.0;\n        const maxTruePeak = -1.0;\n\n        // Calculate required gain for LUFS normalization\n        const lufsGain = targetLUFS - lufs;\n\n        // Calculate required gain for True Peak limiting\n        const tpGain = maxTruePeak - truePeak;\n\n        // The actual gain applied is the minimum of both constraints\n        const gainApplied = Math.min(lufsGain, tpGain);\n\n        // Calculate normalized values\n        const normalizedLufs = lufs + gainApplied;\n        const normalizedTruePeak = truePeak + gainApplied;\n\n        // Determine bottleneck\n        const bottleneck = tpGain < lufsGain ? 'truePeak' : 'lufs';\n\n        return {\n            ...basicData,\n            lufs,\n            truePeak,\n            normalizedLufs,\n            normalizedTruePeak,\n            gainApplied,\n            bottleneck,\n            targetLUFS,\n            maxTruePeak,\n            lufsHeadroom: targetLUFS - normalizedLufs,\n            tpHeadroom: maxTruePeak - normalizedTruePeak,\n            duration: basicData.duration || this.estimateDuration(basicData.filePath)\n        };\n    }\n\n\n\n    generateMockAnalysis(filePath) {\n        // Generate realistic mock data for development\n        const mockLufs = -18 + (Math.random() * 8); // -18 to -10 LUFS\n        const mockTruePeak = -6 + (Math.random() * 5); // -6 to -1 dBTP\n        const mockDuration = 180 + (Math.random() * 120); // 3-5 minutes\n\n        const basicData = {\n            filePath,\n            lufs: mockLufs,\n            truePeak: mockTruePeak,\n            duration: mockDuration\n        };\n\n        return this.enhanceAnalysisData(basicData);\n    }\n\n    estimateDuration(filePath) {\n        // Mock duration estimation - in real implementation this would\n        // be calculated during audio file analysis\n        return 180 + (Math.random() * 120);\n    }\n\n    async loadAudioBuffer(filePath) {\n        try {\n            if (!this.audioContext) {\n                await this.initializeAudioContext();\n            }\n\n            // In a real implementation, this would load the audio file\n            // For now, we'll create a mock audio buffer\n            return this.createMockAudioBuffer();\n        } catch (error) {\n            console.error('Error loading audio buffer:', error);\n            return null;\n        }\n    }\n\n    createMockAudioBuffer() {\n        if (!this.audioContext) return null;\n\n        const sampleRate = this.audioContext.sampleRate;\n        const duration = 3; // 3 seconds of mock audio\n        const frameCount = sampleRate * duration;\n\n        const audioBuffer = this.audioContext.createBuffer(2, frameCount, sampleRate);\n\n        // Generate mock stereo audio data\n        for (let channel = 0; channel < audioBuffer.numberOfChannels; channel++) {\n            const channelData = audioBuffer.getChannelData(channel);\n            for (let i = 0; i < frameCount; i++) {\n                // Generate a simple sine wave with some noise\n                const t = i / sampleRate;\n                channelData[i] = Math.sin(2 * Math.PI * 440 * t) * 0.3 * Math.exp(-t * 0.5) +\n                    (Math.random() - 0.5) * 0.1;\n            }\n        }\n\n        return audioBuffer;\n    }\n\n    calculateWaveformData(audioBuffer, targetSamples = 1000) {\n        if (!audioBuffer) return null;\n\n        const channelData = audioBuffer.getChannelData(0); // Use left channel\n        const samplesPerPixel = Math.floor(channelData.length / targetSamples);\n        const waveformData = [];\n\n        for (let i = 0; i < targetSamples; i++) {\n            const start = i * samplesPerPixel;\n            const end = Math.min(start + samplesPerPixel, channelData.length);\n\n            let min = 0;\n            let max = 0;\n\n            for (let j = start; j < end; j++) {\n                const sample = channelData[j];\n                if (sample > max) max = sample;\n                if (sample < min) min = sample;\n            }\n\n            // Store the peak amplitude for this segment\n            waveformData.push(Math.max(Math.abs(min), Math.abs(max)));\n        }\n\n        return waveformData;\n    }\n\n    async normalizeAudio(audioBuffer, targetLUFS = -14, maxTruePeak = -1) {\n        try {\n            if (window.electronAPI) {\n                // Use Electron's audio processing\n                const result = await window.electronAPI.normalizeAudio(\n                    audioBuffer,\n                    targetLUFS,\n                    maxTruePeak\n                );\n                return result;\n            } else {\n                // Mock normalization for development\n                return {\n                    normalizedBuffer: audioBuffer,\n                    gainApplied: 0,\n                    finalLUFS: targetLUFS,\n                    finalTruePeak: maxTruePeak\n                };\n            }\n        } catch (error) {\n            console.error('Error normalizing audio:', error);\n            return null;\n        }\n    }\n\n    // Utility method to format LUFS values\n    formatLUFS(lufs) {\n        return `${lufs.toFixed(1)} LUFS`;\n    }\n\n    // Utility method to format True Peak values\n    formatTruePeak(truePeak) {\n        return `${truePeak.toFixed(1)} dBTP`;\n    }\n\n    // Utility method to format gain values\n    formatGain(gain) {\n        const sign = gain >= 0 ? '+' : '';\n        return `${sign}${gain.toFixed(1)} dB`;\n    }\n\n    // Clean up resources\n    dispose() {\n        if (this.audioContext && this.audioContext.state !== 'closed') {\n            this.audioContext.close();\n        }\n    }\n}\n\nexport default AudioAnalyzer;"], "mappings": "AAAA,OAAOA,WAAW,MAAM,eAAe;AACvC,SAASC,aAAa,QAAQ,sBAAsB;AAEpD,MAAMC,aAAa,CAAC;EAChBC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,WAAW,GAAG,IAAIJ,WAAW,CAAC,CAAC;IACpC,IAAI,CAACK,aAAa,GAAG,IAAIC,GAAG,CAAC,CAAC;IAC9B,IAAI,CAACC,YAAY,GAAG,IAAI;EAC5B;EAEA,MAAMC,WAAWA,CAACC,QAAQ,EAAE;IACxB,IAAI;MACA;MACA,IAAI,IAAI,CAACJ,aAAa,CAACK,GAAG,CAACD,QAAQ,CAAC,EAAE;QAClC,OAAO,IAAI,CAACJ,aAAa,CAACM,GAAG,CAACF,QAAQ,CAAC;MAC3C;MAEAG,OAAO,CAACC,GAAG,CAAC,0BAA0BJ,QAAQ,EAAE,CAAC;;MAEjD;MACA,MAAMK,mBAAmB,GAAG,MAAMC,MAAM,CAACC,WAAW,CAACC,gBAAgB,CAACR,QAAQ,CAAC;;MAE/E;MACA;MACA,MAAMS,QAAQ,GAAG;QACbC,UAAU,EAAE,CAAC,IAAI,IAAIC,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QAC3CC,SAAS,EAAE,CAAC,IAAI,IAAIF,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QAC1CE,SAAS,EAAE,CAAC,IAAI,IAAIH,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC;MAC7C,CAAC;;MAED;MACA,MAAMG,aAAa,GAAG,MAAMT,MAAM,CAACC,WAAW,CAACS,kBAAkB,CAAChB,QAAQ,EAAES,QAAQ,CAAC;;MAErF;MACAN,OAAO,CAACC,GAAG,CAAC,+CAA+C,CAAC;MAC5D,MAAMa,SAAS,GAAG,MAAM,IAAI,CAACtB,WAAW,CAACuB,aAAa,CAAClB,QAAQ,CAAC;MAChEG,OAAO,CAACC,GAAG,CAAC,sCAAsC,CAAC;MACnD,MAAMe,YAAY,GAAG,IAAI,CAACxB,WAAW,CAACyB,oBAAoB,CAAC,IAAI,CAAC;MAChEjB,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEe,YAAY,GAAGA,YAAY,CAACE,MAAM,GAAG,MAAM,CAAC;;MAE/E;MACA,MAAMC,gBAAgB,GAAG;QACrB,GAAGP,aAAa;QAChBI;MACJ,CAAC;;MAED;MACA,IAAI,CAACvB,aAAa,CAAC2B,GAAG,CAACvB,QAAQ,EAAEsB,gBAAgB,CAAC;MAElDnB,OAAO,CAACC,GAAG,CAAC,0BAA0BJ,QAAQ,EAAE,CAAC;MACjDG,OAAO,CAACC,GAAG,CAAC,SAASkB,gBAAgB,CAACE,IAAI,CAACC,OAAO,CAAC,CAAC,CAAC,gBAAgBH,gBAAgB,CAACI,QAAQ,CAACD,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC;MAEjH,OAAOH,gBAAgB;IAC3B,CAAC,CAAC,OAAOK,KAAK,EAAE;MACZxB,OAAO,CAACwB,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;MACnD,OAAO,IAAI,CAACC,oBAAoB,CAAC5B,QAAQ,CAAC;IAC9C;EACJ;;EAEA;AACJ;AACA;EACI,MAAM6B,wBAAwBA,CAACC,eAAe,EAAE;IAC5C,IAAI;MACA;MACA,IAAI,CAAC,IAAI,CAAChC,YAAY,EAAE;QACpB,IAAI,CAACA,YAAY,GAAG,KAAIQ,MAAM,CAACyB,YAAY,IAAIzB,MAAM,CAAC0B,kBAAkB,EAAE,CAAC;MAC/E;;MAEA;MACA,MAAMC,SAAS,GAAGC,UAAU,CAACC,IAAI,CAACC,IAAI,CAACN,eAAe,CAAC,EAAEO,CAAC,IAAIA,CAAC,CAACC,UAAU,CAAC,CAAC,CAAC,CAAC;;MAE9E;MACA,MAAMC,WAAW,GAAG,MAAM,IAAI,CAACzC,YAAY,CAAC0C,eAAe,CAACP,SAAS,CAACQ,MAAM,CAAC;;MAE7E;MACA,MAAMC,cAAc,GAAG,IAAIC,mBAAmB,CAC1CJ,WAAW,CAACK,gBAAgB,EAC5BL,WAAW,CAAClB,MAAM,EAClBkB,WAAW,CAACM,UAChB,CAAC;;MAED;MACA,MAAMC,MAAM,GAAGJ,cAAc,CAACK,kBAAkB,CAAC,CAAC;MAClDD,MAAM,CAACL,MAAM,GAAGF,WAAW;;MAE3B;MACA,MAAMS,aAAa,GAAG,IAAIxD,aAAa,CAAC;QACpCsD,MAAM,EAAEA,MAAM;QACdG,KAAK,EAAE,CAAC,YAAY,EAAE,WAAW,EAAE,YAAY,CAAC;QAChDC,SAAS,EAAE;MACf,CAAC,CAAC;;MAEF;MACA,MAAMC,YAAY,GAAG;QACjBzC,UAAU,EAAE,IAAI;QAChBG,SAAS,EAAE,EAAE;QACbC,SAAS,EAAE;MACf,CAAC;MAED,OAAO,IAAIsC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;QACpCN,aAAa,CAACO,EAAE,CAAC,eAAe,EAAGC,KAAK,IAAK;UACzC,MAAM;YAAEC,IAAI;YAAEC;UAAM,CAAC,GAAGF,KAAK,CAACG,IAAI;UAElC,IAAIF,IAAI,KAAK,YAAY,EAAE;YACvBN,YAAY,CAACzC,UAAU,GAAGgD,KAAK;UACnC,CAAC,MAAM,IAAID,IAAI,KAAK,WAAW,EAAE;YAC7BN,YAAY,CAACtC,SAAS,CAAC+C,IAAI,CAACF,KAAK,CAAC;UACtC,CAAC,MAAM,IAAID,IAAI,KAAK,YAAY,EAAE;YAC9BN,YAAY,CAACrC,SAAS,CAAC8C,IAAI,CAACF,KAAK,CAAC;UACtC;QACJ,CAAC,CAAC;QAEFV,aAAa,CAACO,EAAE,CAAC,MAAM,EAAE,MAAM;UAC3B;UACA,MAAMM,YAAY,GAAGV,YAAY,CAACtC,SAAS,CAACQ,MAAM,GAAG,CAAC,GAClD8B,YAAY,CAACtC,SAAS,CAACiD,MAAM,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,GAAGC,CAAC,CAAC,GAAGb,YAAY,CAACtC,SAAS,CAACQ,MAAM,GAC9E8B,YAAY,CAACzC,UAAU;UAE3B,MAAMuD,YAAY,GAAGd,YAAY,CAACrC,SAAS,CAACO,MAAM,GAAG,CAAC,GAClD8B,YAAY,CAACrC,SAAS,CAACgD,MAAM,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,GAAGC,CAAC,CAAC,GAAGb,YAAY,CAACrC,SAAS,CAACO,MAAM,GAC9E8B,YAAY,CAACzC,UAAU;UAE3B2C,OAAO,CAAC;YACJ3C,UAAU,EAAEyC,YAAY,CAACzC,UAAU,IAAI,CAAC,IAAI;YAAE;YAC9CG,SAAS,EAAEgD,YAAY,IAAIV,YAAY,CAACzC,UAAU,IAAI,CAAC,IAAI;YAC3DI,SAAS,EAAEmD,YAAY,IAAId,YAAY,CAACzC,UAAU,IAAI,CAAC;UAC3D,CAAC,CAAC;QACN,CAAC,CAAC;QAEFsC,aAAa,CAACO,EAAE,CAAC,OAAO,EAAG5B,KAAK,IAAK;UACjCxB,OAAO,CAACwB,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;UACvD;UACA0B,OAAO,CAAC;YACJ3C,UAAU,EAAE,CAAC,IAAI,IAAIC,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;YAAE;YAC7CC,SAAS,EAAE,CAAC,IAAI,IAAIF,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;YAC1CE,SAAS,EAAE,CAAC,IAAI,IAAIH,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC;UAC7C,CAAC,CAAC;QACN,CAAC,CAAC;;QAEF;QACAoC,aAAa,CAACkB,KAAK,CAAC,CAAC;;QAErB;QACAC,UAAU,CAAC,MAAM;UACb,IAAIhB,YAAY,CAACzC,UAAU,KAAK,IAAI,EAAE;YAClCP,OAAO,CAACiE,IAAI,CAAC,0CAA0C,CAAC;YACxDf,OAAO,CAAC;cACJ3C,UAAU,EAAE,CAAC,IAAI,IAAIC,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;cAC3CC,SAAS,EAAE,CAAC,IAAI,IAAIF,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;cAC1CE,SAAS,EAAE,CAAC,IAAI,IAAIH,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC;YAC7C,CAAC,CAAC;UACN;QACJ,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;MACf,CAAC,CAAC;IAEN,CAAC,CAAC,OAAOe,KAAK,EAAE;MACZxB,OAAO,CAACwB,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;MAClD;MACA,OAAO;QACHjB,UAAU,EAAE,CAAC,IAAI,IAAIC,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QAC3CC,SAAS,EAAE,CAAC,IAAI,IAAIF,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QAC1CE,SAAS,EAAE,CAAC,IAAI,IAAIH,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC;MAC7C,CAAC;IACL;EACJ;EAEAyD,mBAAmBA,CAACC,SAAS,EAAE;IAC3B,MAAM;MAAE9C,IAAI;MAAEE;IAAS,CAAC,GAAG4C,SAAS;;IAEpC;IACA,MAAMC,UAAU,GAAG,CAAC,IAAI;IACxB,MAAMC,WAAW,GAAG,CAAC,GAAG;;IAExB;IACA,MAAMC,QAAQ,GAAGF,UAAU,GAAG/C,IAAI;;IAElC;IACA,MAAMkD,MAAM,GAAGF,WAAW,GAAG9C,QAAQ;;IAErC;IACA,MAAMiD,WAAW,GAAGhE,IAAI,CAACiE,GAAG,CAACH,QAAQ,EAAEC,MAAM,CAAC;;IAE9C;IACA,MAAMG,cAAc,GAAGrD,IAAI,GAAGmD,WAAW;IACzC,MAAMG,kBAAkB,GAAGpD,QAAQ,GAAGiD,WAAW;;IAEjD;IACA,MAAMI,UAAU,GAAGL,MAAM,GAAGD,QAAQ,GAAG,UAAU,GAAG,MAAM;IAE1D,OAAO;MACH,GAAGH,SAAS;MACZ9C,IAAI;MACJE,QAAQ;MACRmD,cAAc;MACdC,kBAAkB;MAClBH,WAAW;MACXI,UAAU;MACVR,UAAU;MACVC,WAAW;MACXQ,YAAY,EAAET,UAAU,GAAGM,cAAc;MACzCI,UAAU,EAAET,WAAW,GAAGM,kBAAkB;MAC5CI,QAAQ,EAAEZ,SAAS,CAACY,QAAQ,IAAI,IAAI,CAACC,gBAAgB,CAACb,SAAS,CAACtE,QAAQ;IAC5E,CAAC;EACL;EAIA4B,oBAAoBA,CAAC5B,QAAQ,EAAE;IAC3B;IACA,MAAMoF,QAAQ,GAAG,CAAC,EAAE,GAAIzE,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,CAAE,CAAC,CAAC;IAC5C,MAAMyE,YAAY,GAAG,CAAC,CAAC,GAAI1E,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,CAAE,CAAC,CAAC;IAC/C,MAAM0E,YAAY,GAAG,GAAG,GAAI3E,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAI,CAAC,CAAC;;IAElD,MAAM0D,SAAS,GAAG;MACdtE,QAAQ;MACRwB,IAAI,EAAE4D,QAAQ;MACd1D,QAAQ,EAAE2D,YAAY;MACtBH,QAAQ,EAAEI;IACd,CAAC;IAED,OAAO,IAAI,CAACjB,mBAAmB,CAACC,SAAS,CAAC;EAC9C;EAEAa,gBAAgBA,CAACnF,QAAQ,EAAE;IACvB;IACA;IACA,OAAO,GAAG,GAAIW,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAI;EACtC;EAEA,MAAM2E,eAAeA,CAACvF,QAAQ,EAAE;IAC5B,IAAI;MACA,IAAI,CAAC,IAAI,CAACF,YAAY,EAAE;QACpB,MAAM,IAAI,CAAC0F,sBAAsB,CAAC,CAAC;MACvC;;MAEA;MACA;MACA,OAAO,IAAI,CAACC,qBAAqB,CAAC,CAAC;IACvC,CAAC,CAAC,OAAO9D,KAAK,EAAE;MACZxB,OAAO,CAACwB,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;MACnD,OAAO,IAAI;IACf;EACJ;EAEA8D,qBAAqBA,CAAA,EAAG;IACpB,IAAI,CAAC,IAAI,CAAC3F,YAAY,EAAE,OAAO,IAAI;IAEnC,MAAM+C,UAAU,GAAG,IAAI,CAAC/C,YAAY,CAAC+C,UAAU;IAC/C,MAAMqC,QAAQ,GAAG,CAAC,CAAC,CAAC;IACpB,MAAMQ,UAAU,GAAG7C,UAAU,GAAGqC,QAAQ;IAExC,MAAM3C,WAAW,GAAG,IAAI,CAACzC,YAAY,CAAC6F,YAAY,CAAC,CAAC,EAAED,UAAU,EAAE7C,UAAU,CAAC;;IAE7E;IACA,KAAK,IAAI+C,OAAO,GAAG,CAAC,EAAEA,OAAO,GAAGrD,WAAW,CAACK,gBAAgB,EAAEgD,OAAO,EAAE,EAAE;MACrE,MAAMC,WAAW,GAAGtD,WAAW,CAACuD,cAAc,CAACF,OAAO,CAAC;MACvD,KAAK,IAAIG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGL,UAAU,EAAEK,CAAC,EAAE,EAAE;QACjC;QACA,MAAMC,CAAC,GAAGD,CAAC,GAAGlD,UAAU;QACxBgD,WAAW,CAACE,CAAC,CAAC,GAAGpF,IAAI,CAACsF,GAAG,CAAC,CAAC,GAAGtF,IAAI,CAACuF,EAAE,GAAG,GAAG,GAAGF,CAAC,CAAC,GAAG,GAAG,GAAGrF,IAAI,CAACwF,GAAG,CAAC,CAACH,CAAC,GAAG,GAAG,CAAC,GACvE,CAACrF,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG,IAAI,GAAG;MACnC;IACJ;IAEA,OAAO2B,WAAW;EACtB;EAEA6D,qBAAqBA,CAAC7D,WAAW,EAAE8D,aAAa,GAAG,IAAI,EAAE;IACrD,IAAI,CAAC9D,WAAW,EAAE,OAAO,IAAI;IAE7B,MAAMsD,WAAW,GAAGtD,WAAW,CAACuD,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC;IACnD,MAAMQ,eAAe,GAAG3F,IAAI,CAAC4F,KAAK,CAACV,WAAW,CAACxE,MAAM,GAAGgF,aAAa,CAAC;IACtE,MAAMlF,YAAY,GAAG,EAAE;IAEvB,KAAK,IAAI4E,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGM,aAAa,EAAEN,CAAC,EAAE,EAAE;MACpC,MAAM7B,KAAK,GAAG6B,CAAC,GAAGO,eAAe;MACjC,MAAME,GAAG,GAAG7F,IAAI,CAACiE,GAAG,CAACV,KAAK,GAAGoC,eAAe,EAAET,WAAW,CAACxE,MAAM,CAAC;MAEjE,IAAIuD,GAAG,GAAG,CAAC;MACX,IAAI6B,GAAG,GAAG,CAAC;MAEX,KAAK,IAAIC,CAAC,GAAGxC,KAAK,EAAEwC,CAAC,GAAGF,GAAG,EAAEE,CAAC,EAAE,EAAE;QAC9B,MAAMC,MAAM,GAAGd,WAAW,CAACa,CAAC,CAAC;QAC7B,IAAIC,MAAM,GAAGF,GAAG,EAAEA,GAAG,GAAGE,MAAM;QAC9B,IAAIA,MAAM,GAAG/B,GAAG,EAAEA,GAAG,GAAG+B,MAAM;MAClC;;MAEA;MACAxF,YAAY,CAACyC,IAAI,CAACjD,IAAI,CAAC8F,GAAG,CAAC9F,IAAI,CAACiG,GAAG,CAAChC,GAAG,CAAC,EAAEjE,IAAI,CAACiG,GAAG,CAACH,GAAG,CAAC,CAAC,CAAC;IAC7D;IAEA,OAAOtF,YAAY;EACvB;EAEA,MAAM0F,cAAcA,CAACtE,WAAW,EAAEgC,UAAU,GAAG,CAAC,EAAE,EAAEC,WAAW,GAAG,CAAC,CAAC,EAAE;IAClE,IAAI;MACA,IAAIlE,MAAM,CAACC,WAAW,EAAE;QACpB;QACA,MAAMuG,MAAM,GAAG,MAAMxG,MAAM,CAACC,WAAW,CAACsG,cAAc,CAClDtE,WAAW,EACXgC,UAAU,EACVC,WACJ,CAAC;QACD,OAAOsC,MAAM;MACjB,CAAC,MAAM;QACH;QACA,OAAO;UACHC,gBAAgB,EAAExE,WAAW;UAC7BoC,WAAW,EAAE,CAAC;UACdqC,SAAS,EAAEzC,UAAU;UACrB0C,aAAa,EAAEzC;QACnB,CAAC;MACL;IACJ,CAAC,CAAC,OAAO7C,KAAK,EAAE;MACZxB,OAAO,CAACwB,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;MAChD,OAAO,IAAI;IACf;EACJ;;EAEA;EACAuF,UAAUA,CAAC1F,IAAI,EAAE;IACb,OAAO,GAAGA,IAAI,CAACC,OAAO,CAAC,CAAC,CAAC,OAAO;EACpC;;EAEA;EACA0F,cAAcA,CAACzF,QAAQ,EAAE;IACrB,OAAO,GAAGA,QAAQ,CAACD,OAAO,CAAC,CAAC,CAAC,OAAO;EACxC;;EAEA;EACA2F,UAAUA,CAACC,IAAI,EAAE;IACb,MAAMC,IAAI,GAAGD,IAAI,IAAI,CAAC,GAAG,GAAG,GAAG,EAAE;IACjC,OAAO,GAAGC,IAAI,GAAGD,IAAI,CAAC5F,OAAO,CAAC,CAAC,CAAC,KAAK;EACzC;;EAEA;EACA8F,OAAOA,CAAA,EAAG;IACN,IAAI,IAAI,CAACzH,YAAY,IAAI,IAAI,CAACA,YAAY,CAAC0H,KAAK,KAAK,QAAQ,EAAE;MAC3D,IAAI,CAAC1H,YAAY,CAAC2H,KAAK,CAAC,CAAC;IAC7B;EACJ;AACJ;AAEA,eAAehI,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}