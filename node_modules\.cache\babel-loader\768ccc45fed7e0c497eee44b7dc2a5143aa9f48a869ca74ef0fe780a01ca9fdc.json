{"ast": null, "code": "import AudioEngine from './AudioEngine';\nimport { LoudnessMeter } from '@domchristie/needles';\nclass AudioAnalyzer {\n  constructor() {\n    this.audioEngine = new AudioEngine();\n    this.analysisCache = new Map();\n    this.audioContext = null;\n  }\n  async analyzeFile(filePath) {\n    try {\n      // Check cache first\n      if (this.analysisCache.has(filePath)) {\n        return this.analysisCache.get(filePath);\n      }\n      console.log(`Starting analysis for: ${filePath}`);\n\n      // Load audio file for waveform generation and playback FIRST\n      console.log('Loading audio file for waveform generation...');\n      const audioData = await this.audioEngine.loadAudioFile(filePath);\n      console.log('Audio loaded, generating waveform...');\n      const waveformData = this.audioEngine.generateWaveformData(1000);\n      console.log('Waveform generated:', waveformData ? waveformData.length : 'null');\n\n      // Calculate basic metrics from the loaded audio\n      const metrics = this.audioEngine.calculateAudioMetrics();\n\n      // Calculate REAL LUFS using needles library\n      let lufsValue = -16.0; // fallback\n      try {\n        lufsValue = await this.calculateRealLUFS(audioData.buffer);\n        console.log(`REAL LUFS calculated: ${lufsValue.toFixed(1)}`);\n      } catch (error) {\n        console.error('LUFS calculation failed:', error);\n        // Use RMS-based estimate as fallback\n        lufsValue = metrics ? metrics.rmsDb - 3.5 : -16.0;\n      }\n\n      // Create analysis result with real waveform data\n      const completeAnalysis = {\n        filePath,\n        duration: audioData.duration,\n        sampleRate: audioData.sampleRate,\n        channels: audioData.numberOfChannels,\n        // Real waveform data\n        waveformData,\n        // Audio metrics\n        peak: metrics ? metrics.peak : 0.5,\n        peakDb: metrics ? metrics.peakDb : -6.0,\n        rms: metrics ? metrics.rms : 0.1,\n        rmsDb: metrics ? metrics.rmsDb : -20.0,\n        // REAL LUFS measurement\n        lufs: lufsValue,\n        truePeak: metrics ? metrics.peakDb + 0.3 : -6.0,\n        // Simple True Peak estimate\n\n        // Normalization parameters\n        targetLUFS: -14.0,\n        maxTruePeak: -1.0,\n        timestamp: Date.now()\n      };\n\n      // Calculate normalization parameters\n      const enhancedAnalysis = this.enhanceAnalysisData(completeAnalysis);\n\n      // Cache the result\n      this.analysisCache.set(filePath, enhancedAnalysis);\n      console.log(`Analysis complete for: ${filePath}`);\n      console.log(`LUFS: ${enhancedAnalysis.lufs.toFixed(1)}, True Peak: ${enhancedAnalysis.truePeak.toFixed(1)} dBTP`);\n      return enhancedAnalysis;\n    } catch (error) {\n      console.error('Error analyzing audio file:', error);\n      return this.generateMockAnalysis(filePath);\n    }\n  }\n\n  /**\n   * Calculate REAL LUFS using needles library - SIMPLIFIED VERSION\n   */\n  async calculateRealLUFS(audioBuffer) {\n    try {\n      // Initialize audio context if needed\n      if (!this.audioContext) {\n        this.audioContext = new (window.AudioContext || window.webkitAudioContext)();\n      }\n\n      // Create offline context for analysis\n      const offlineContext = new OfflineAudioContext(audioBuffer.numberOfChannels, audioBuffer.length, audioBuffer.sampleRate);\n\n      // Create buffer source\n      const source = offlineContext.createBufferSource();\n      source.buffer = audioBuffer;\n\n      // Create loudness meter with simplified configuration\n      const loudnessMeter = new LoudnessMeter({\n        source: source,\n        modes: ['integrated'],\n        workerUri: './needles-worker.js'\n      });\n      return new Promise((resolve, reject) => {\n        let lufsResult = null;\n        loudnessMeter.on('dataavailable', event => {\n          if (event.data.mode === 'integrated') {\n            lufsResult = event.data.value;\n            console.log(`Needles LUFS result: ${lufsResult}`);\n          }\n        });\n        loudnessMeter.on('stop', () => {\n          if (lufsResult !== null) {\n            resolve(lufsResult);\n          } else {\n            reject(new Error('No LUFS measurement received'));\n          }\n        });\n        loudnessMeter.on('error', error => {\n          console.error('Needles error:', error);\n          reject(error);\n        });\n\n        // Start analysis\n        try {\n          loudnessMeter.start();\n        } catch (error) {\n          reject(error);\n        }\n\n        // Timeout after 5 seconds\n        setTimeout(() => {\n          if (lufsResult === null) {\n            reject(new Error('LUFS calculation timeout'));\n          }\n        }, 5000);\n      });\n    } catch (error) {\n      console.error('Error setting up LUFS calculation:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Calculate LUFS using the needles library - COMPLEX VERSION (DISABLED)\n   */\n  async calculateLUFSWithNeedles(wavBufferBase64) {\n    try {\n      // Initialize audio context if needed\n      if (!this.audioContext) {\n        this.audioContext = new (window.AudioContext || window.webkitAudioContext)();\n      }\n\n      // Decode base64 WAV buffer\n      const wavBuffer = Uint8Array.from(atob(wavBufferBase64), c => c.charCodeAt(0));\n\n      // Decode audio data\n      const audioBuffer = await this.audioContext.decodeAudioData(wavBuffer.buffer);\n\n      // Create offline context for analysis\n      const offlineContext = new OfflineAudioContext(audioBuffer.numberOfChannels, audioBuffer.length, audioBuffer.sampleRate);\n\n      // Create buffer source\n      const source = offlineContext.createBufferSource();\n      source.buffer = audioBuffer;\n\n      // Create loudness meter\n      const loudnessMeter = new LoudnessMeter({\n        source: source,\n        modes: ['integrated', 'momentary', 'short-term'],\n        workerUri: './needles-worker.js'\n      });\n\n      // Collect LUFS measurements\n      const measurements = {\n        integrated: null,\n        momentary: [],\n        shortTerm: []\n      };\n      return new Promise((resolve, reject) => {\n        loudnessMeter.on('dataavailable', event => {\n          const {\n            mode,\n            value\n          } = event.data;\n          if (mode === 'integrated') {\n            measurements.integrated = value;\n          } else if (mode === 'momentary') {\n            measurements.momentary.push(value);\n          } else if (mode === 'short-term') {\n            measurements.shortTerm.push(value);\n          }\n        });\n        loudnessMeter.on('stop', () => {\n          // Calculate average momentary and short-term values\n          const avgMomentary = measurements.momentary.length > 0 ? measurements.momentary.reduce((a, b) => a + b) / measurements.momentary.length : measurements.integrated;\n          const avgShortTerm = measurements.shortTerm.length > 0 ? measurements.shortTerm.reduce((a, b) => a + b) / measurements.shortTerm.length : measurements.integrated;\n          resolve({\n            integrated: measurements.integrated || -23.0,\n            // Default if no measurement\n            momentary: avgMomentary || measurements.integrated || -23.0,\n            shortTerm: avgShortTerm || measurements.integrated || -23.0\n          });\n        });\n        loudnessMeter.on('error', error => {\n          console.error('Needles LUFS calculation error:', error);\n          // Fallback to estimated LUFS\n          resolve({\n            integrated: -16.0 + (Math.random() * 8 - 4),\n            // -20 to -12 LUFS\n            momentary: -16.0 + (Math.random() * 8 - 4),\n            shortTerm: -16.0 + (Math.random() * 8 - 4)\n          });\n        });\n\n        // Start analysis\n        loudnessMeter.start();\n\n        // Set timeout as fallback\n        setTimeout(() => {\n          if (measurements.integrated === null) {\n            console.warn('LUFS calculation timeout, using fallback');\n            resolve({\n              integrated: -16.0 + (Math.random() * 8 - 4),\n              momentary: -16.0 + (Math.random() * 8 - 4),\n              shortTerm: -16.0 + (Math.random() * 8 - 4)\n            });\n          }\n        }, 10000); // 10 second timeout\n      });\n    } catch (error) {\n      console.error('Error in LUFS calculation:', error);\n      // Return fallback values\n      return {\n        integrated: -16.0 + (Math.random() * 8 - 4),\n        momentary: -16.0 + (Math.random() * 8 - 4),\n        shortTerm: -16.0 + (Math.random() * 8 - 4)\n      };\n    }\n  }\n  enhanceAnalysisData(basicData) {\n    const {\n      lufs,\n      truePeak\n    } = basicData;\n\n    // Calculate normalization parameters\n    const targetLUFS = -14.0;\n    const maxTruePeak = -1.0;\n\n    // Calculate required gain for LUFS normalization\n    const lufsGain = targetLUFS - lufs;\n\n    // Calculate required gain for True Peak limiting\n    const tpGain = maxTruePeak - truePeak;\n\n    // The actual gain applied is the minimum of both constraints\n    const gainApplied = Math.min(lufsGain, tpGain);\n\n    // Calculate normalized values\n    const normalizedLufs = lufs + gainApplied;\n    const normalizedTruePeak = truePeak + gainApplied;\n\n    // Determine bottleneck\n    const bottleneck = tpGain < lufsGain ? 'truePeak' : 'lufs';\n    return {\n      ...basicData,\n      lufs,\n      truePeak,\n      normalizedLufs,\n      normalizedTruePeak,\n      gainApplied,\n      bottleneck,\n      targetLUFS,\n      maxTruePeak,\n      lufsHeadroom: targetLUFS - normalizedLufs,\n      tpHeadroom: maxTruePeak - normalizedTruePeak,\n      duration: basicData.duration || this.estimateDuration(basicData.filePath)\n    };\n  }\n  generateMockAnalysis(filePath) {\n    // Generate realistic mock data for development\n    const mockLufs = -18 + Math.random() * 8; // -18 to -10 LUFS\n    const mockTruePeak = -6 + Math.random() * 5; // -6 to -1 dBTP\n    const mockDuration = 180 + Math.random() * 120; // 3-5 minutes\n\n    const basicData = {\n      filePath,\n      lufs: mockLufs,\n      truePeak: mockTruePeak,\n      duration: mockDuration\n    };\n    return this.enhanceAnalysisData(basicData);\n  }\n  estimateDuration(filePath) {\n    // Mock duration estimation - in real implementation this would\n    // be calculated during audio file analysis\n    return 180 + Math.random() * 120;\n  }\n  async loadAudioBuffer(filePath) {\n    try {\n      if (!this.audioContext) {\n        await this.initializeAudioContext();\n      }\n\n      // In a real implementation, this would load the audio file\n      // For now, we'll create a mock audio buffer\n      return this.createMockAudioBuffer();\n    } catch (error) {\n      console.error('Error loading audio buffer:', error);\n      return null;\n    }\n  }\n  createMockAudioBuffer() {\n    if (!this.audioContext) return null;\n    const sampleRate = this.audioContext.sampleRate;\n    const duration = 3; // 3 seconds of mock audio\n    const frameCount = sampleRate * duration;\n    const audioBuffer = this.audioContext.createBuffer(2, frameCount, sampleRate);\n\n    // Generate mock stereo audio data\n    for (let channel = 0; channel < audioBuffer.numberOfChannels; channel++) {\n      const channelData = audioBuffer.getChannelData(channel);\n      for (let i = 0; i < frameCount; i++) {\n        // Generate a simple sine wave with some noise\n        const t = i / sampleRate;\n        channelData[i] = Math.sin(2 * Math.PI * 440 * t) * 0.3 * Math.exp(-t * 0.5) + (Math.random() - 0.5) * 0.1;\n      }\n    }\n    return audioBuffer;\n  }\n  calculateWaveformData(audioBuffer, targetSamples = 1000) {\n    if (!audioBuffer) return null;\n    const channelData = audioBuffer.getChannelData(0); // Use left channel\n    const samplesPerPixel = Math.floor(channelData.length / targetSamples);\n    const waveformData = [];\n    for (let i = 0; i < targetSamples; i++) {\n      const start = i * samplesPerPixel;\n      const end = Math.min(start + samplesPerPixel, channelData.length);\n      let min = 0;\n      let max = 0;\n      for (let j = start; j < end; j++) {\n        const sample = channelData[j];\n        if (sample > max) max = sample;\n        if (sample < min) min = sample;\n      }\n\n      // Store the peak amplitude for this segment\n      waveformData.push(Math.max(Math.abs(min), Math.abs(max)));\n    }\n    return waveformData;\n  }\n  async normalizeAudio(audioBuffer, targetLUFS = -14, maxTruePeak = -1) {\n    try {\n      if (window.electronAPI) {\n        // Use Electron's audio processing\n        const result = await window.electronAPI.normalizeAudio(audioBuffer, targetLUFS, maxTruePeak);\n        return result;\n      } else {\n        // Mock normalization for development\n        return {\n          normalizedBuffer: audioBuffer,\n          gainApplied: 0,\n          finalLUFS: targetLUFS,\n          finalTruePeak: maxTruePeak\n        };\n      }\n    } catch (error) {\n      console.error('Error normalizing audio:', error);\n      return null;\n    }\n  }\n\n  // Utility method to format LUFS values\n  formatLUFS(lufs) {\n    return `${lufs.toFixed(1)} LUFS`;\n  }\n\n  // Utility method to format True Peak values\n  formatTruePeak(truePeak) {\n    return `${truePeak.toFixed(1)} dBTP`;\n  }\n\n  // Utility method to format gain values\n  formatGain(gain) {\n    const sign = gain >= 0 ? '+' : '';\n    return `${sign}${gain.toFixed(1)} dB`;\n  }\n\n  // Clean up resources\n  dispose() {\n    if (this.audioContext && this.audioContext.state !== 'closed') {\n      this.audioContext.close();\n    }\n  }\n}\nexport default AudioAnalyzer;", "map": {"version": 3, "names": ["AudioEngine", "LoudnessMeter", "AudioAnalyzer", "constructor", "audioEngine", "analysisCache", "Map", "audioContext", "analyzeFile", "filePath", "has", "get", "console", "log", "audioData", "loadAudioFile", "waveformData", "generateWaveformData", "length", "metrics", "calculateAudioMetrics", "lufsValue", "calculateRealLUFS", "buffer", "toFixed", "error", "rmsDb", "completeAnalysis", "duration", "sampleRate", "channels", "numberOfChannels", "peak", "peakDb", "rms", "lufs", "truePeak", "targetLUFS", "maxTruePeak", "timestamp", "Date", "now", "enhancedAnalysis", "enhanceAnalysisData", "set", "generateMockAnalysis", "audioBuffer", "window", "AudioContext", "webkitAudioContext", "offlineContext", "OfflineAudioContext", "source", "createBufferSource", "loudnessMeter", "modes", "workerUri", "Promise", "resolve", "reject", "lufsResult", "on", "event", "data", "mode", "value", "Error", "start", "setTimeout", "calculateLUFSWithNeedles", "wavBufferBase64", "wavBuffer", "Uint8Array", "from", "atob", "c", "charCodeAt", "decodeAudioData", "measurements", "integrated", "momentary", "shortTerm", "push", "avgMomentary", "reduce", "a", "b", "avgShortTerm", "Math", "random", "warn", "basicData", "lufsG<PERSON>", "tpGain", "gainApplied", "min", "normalizedLufs", "normalizedTruePeak", "bottleneck", "lufsHeadroom", "tpHeadroom", "estimateDuration", "mockLufs", "mockTruePeak", "mockDuration", "loadAudioBuffer", "initializeAudioContext", "createMockAudioBuffer", "frameCount", "createBuffer", "channel", "channelData", "getChannelData", "i", "t", "sin", "PI", "exp", "calculateWaveformData", "targetSamples", "samplesPerPixel", "floor", "end", "max", "j", "sample", "abs", "normalizeAudio", "electronAPI", "result", "normalizedBuffer", "finalLUFS", "finalTruePeak", "formatLUFS", "formatTruePeak", "formatGain", "gain", "sign", "dispose", "state", "close"], "sources": ["C:/Users/<USER>/Desktop/VSC Folder/AlbumPlayer/src/audio/AudioAnalyzer.js"], "sourcesContent": ["import AudioEngine from './AudioEngine';\nimport { LoudnessMeter } from '@domchristie/needles';\n\nclass AudioAnalyzer {\n    constructor() {\n        this.audioEngine = new AudioEngine();\n        this.analysisCache = new Map();\n        this.audioContext = null;\n    }\n\n    async analyzeFile(filePath) {\n        try {\n            // Check cache first\n            if (this.analysisCache.has(filePath)) {\n                return this.analysisCache.get(filePath);\n            }\n\n            console.log(`Starting analysis for: ${filePath}`);\n\n            // Load audio file for waveform generation and playback FIRST\n            console.log('Loading audio file for waveform generation...');\n            const audioData = await this.audioEngine.loadAudioFile(filePath);\n            console.log('Audio loaded, generating waveform...');\n            const waveformData = this.audioEngine.generateWaveformData(1000);\n            console.log('Waveform generated:', waveformData ? waveformData.length : 'null');\n\n            // Calculate basic metrics from the loaded audio\n            const metrics = this.audioEngine.calculateAudioMetrics();\n\n            // Calculate REAL LUFS using needles library\n            let lufsValue = -16.0; // fallback\n            try {\n                lufsValue = await this.calculateRealLUFS(audioData.buffer);\n                console.log(`REAL LUFS calculated: ${lufsValue.toFixed(1)}`);\n            } catch (error) {\n                console.error('LUFS calculation failed:', error);\n                // Use RMS-based estimate as fallback\n                lufsValue = metrics ? metrics.rmsDb - 3.5 : -16.0;\n            }\n\n            // Create analysis result with real waveform data\n            const completeAnalysis = {\n                filePath,\n                duration: audioData.duration,\n                sampleRate: audioData.sampleRate,\n                channels: audioData.numberOfChannels,\n\n                // Real waveform data\n                waveformData,\n\n                // Audio metrics\n                peak: metrics ? metrics.peak : 0.5,\n                peakDb: metrics ? metrics.peakDb : -6.0,\n                rms: metrics ? metrics.rms : 0.1,\n                rmsDb: metrics ? metrics.rmsDb : -20.0,\n\n                // REAL LUFS measurement\n                lufs: lufsValue,\n                truePeak: metrics ? metrics.peakDb + 0.3 : -6.0, // Simple True Peak estimate\n\n                // Normalization parameters\n                targetLUFS: -14.0,\n                maxTruePeak: -1.0,\n\n                timestamp: Date.now()\n            };\n\n            // Calculate normalization parameters\n            const enhancedAnalysis = this.enhanceAnalysisData(completeAnalysis);\n\n            // Cache the result\n            this.analysisCache.set(filePath, enhancedAnalysis);\n\n            console.log(`Analysis complete for: ${filePath}`);\n            console.log(`LUFS: ${enhancedAnalysis.lufs.toFixed(1)}, True Peak: ${enhancedAnalysis.truePeak.toFixed(1)} dBTP`);\n\n            return enhancedAnalysis;\n        } catch (error) {\n            console.error('Error analyzing audio file:', error);\n            return this.generateMockAnalysis(filePath);\n        }\n    }\n\n    /**\n     * Calculate REAL LUFS using needles library - SIMPLIFIED VERSION\n     */\n    async calculateRealLUFS(audioBuffer) {\n        try {\n            // Initialize audio context if needed\n            if (!this.audioContext) {\n                this.audioContext = new(window.AudioContext || window.webkitAudioContext)();\n            }\n\n            // Create offline context for analysis\n            const offlineContext = new OfflineAudioContext(\n                audioBuffer.numberOfChannels,\n                audioBuffer.length,\n                audioBuffer.sampleRate\n            );\n\n            // Create buffer source\n            const source = offlineContext.createBufferSource();\n            source.buffer = audioBuffer;\n\n            // Create loudness meter with simplified configuration\n            const loudnessMeter = new LoudnessMeter({\n                source: source,\n                modes: ['integrated'],\n                workerUri: './needles-worker.js'\n            });\n\n            return new Promise((resolve, reject) => {\n                let lufsResult = null;\n\n                loudnessMeter.on('dataavailable', (event) => {\n                    if (event.data.mode === 'integrated') {\n                        lufsResult = event.data.value;\n                        console.log(`Needles LUFS result: ${lufsResult}`);\n                    }\n                });\n\n                loudnessMeter.on('stop', () => {\n                    if (lufsResult !== null) {\n                        resolve(lufsResult);\n                    } else {\n                        reject(new Error('No LUFS measurement received'));\n                    }\n                });\n\n                loudnessMeter.on('error', (error) => {\n                    console.error('Needles error:', error);\n                    reject(error);\n                });\n\n                // Start analysis\n                try {\n                    loudnessMeter.start();\n                } catch (error) {\n                    reject(error);\n                }\n\n                // Timeout after 5 seconds\n                setTimeout(() => {\n                    if (lufsResult === null) {\n                        reject(new Error('LUFS calculation timeout'));\n                    }\n                }, 5000);\n            });\n\n        } catch (error) {\n            console.error('Error setting up LUFS calculation:', error);\n            throw error;\n        }\n    }\n\n    /**\n     * Calculate LUFS using the needles library - COMPLEX VERSION (DISABLED)\n     */\n    async calculateLUFSWithNeedles(wavBufferBase64) {\n        try {\n            // Initialize audio context if needed\n            if (!this.audioContext) {\n                this.audioContext = new(window.AudioContext || window.webkitAudioContext)();\n            }\n\n            // Decode base64 WAV buffer\n            const wavBuffer = Uint8Array.from(atob(wavBufferBase64), c => c.charCodeAt(0));\n\n            // Decode audio data\n            const audioBuffer = await this.audioContext.decodeAudioData(wavBuffer.buffer);\n\n            // Create offline context for analysis\n            const offlineContext = new OfflineAudioContext(\n                audioBuffer.numberOfChannels,\n                audioBuffer.length,\n                audioBuffer.sampleRate\n            );\n\n            // Create buffer source\n            const source = offlineContext.createBufferSource();\n            source.buffer = audioBuffer;\n\n            // Create loudness meter\n            const loudnessMeter = new LoudnessMeter({\n                source: source,\n                modes: ['integrated', 'momentary', 'short-term'],\n                workerUri: './needles-worker.js'\n            });\n\n            // Collect LUFS measurements\n            const measurements = {\n                integrated: null,\n                momentary: [],\n                shortTerm: []\n            };\n\n            return new Promise((resolve, reject) => {\n                loudnessMeter.on('dataavailable', (event) => {\n                    const { mode, value } = event.data;\n\n                    if (mode === 'integrated') {\n                        measurements.integrated = value;\n                    } else if (mode === 'momentary') {\n                        measurements.momentary.push(value);\n                    } else if (mode === 'short-term') {\n                        measurements.shortTerm.push(value);\n                    }\n                });\n\n                loudnessMeter.on('stop', () => {\n                    // Calculate average momentary and short-term values\n                    const avgMomentary = measurements.momentary.length > 0 ?\n                        measurements.momentary.reduce((a, b) => a + b) / measurements.momentary.length :\n                        measurements.integrated;\n\n                    const avgShortTerm = measurements.shortTerm.length > 0 ?\n                        measurements.shortTerm.reduce((a, b) => a + b) / measurements.shortTerm.length :\n                        measurements.integrated;\n\n                    resolve({\n                        integrated: measurements.integrated || -23.0, // Default if no measurement\n                        momentary: avgMomentary || measurements.integrated || -23.0,\n                        shortTerm: avgShortTerm || measurements.integrated || -23.0\n                    });\n                });\n\n                loudnessMeter.on('error', (error) => {\n                    console.error('Needles LUFS calculation error:', error);\n                    // Fallback to estimated LUFS\n                    resolve({\n                        integrated: -16.0 + (Math.random() * 8 - 4), // -20 to -12 LUFS\n                        momentary: -16.0 + (Math.random() * 8 - 4),\n                        shortTerm: -16.0 + (Math.random() * 8 - 4)\n                    });\n                });\n\n                // Start analysis\n                loudnessMeter.start();\n\n                // Set timeout as fallback\n                setTimeout(() => {\n                    if (measurements.integrated === null) {\n                        console.warn('LUFS calculation timeout, using fallback');\n                        resolve({\n                            integrated: -16.0 + (Math.random() * 8 - 4),\n                            momentary: -16.0 + (Math.random() * 8 - 4),\n                            shortTerm: -16.0 + (Math.random() * 8 - 4)\n                        });\n                    }\n                }, 10000); // 10 second timeout\n            });\n\n        } catch (error) {\n            console.error('Error in LUFS calculation:', error);\n            // Return fallback values\n            return {\n                integrated: -16.0 + (Math.random() * 8 - 4),\n                momentary: -16.0 + (Math.random() * 8 - 4),\n                shortTerm: -16.0 + (Math.random() * 8 - 4)\n            };\n        }\n    }\n\n    enhanceAnalysisData(basicData) {\n        const { lufs, truePeak } = basicData;\n\n        // Calculate normalization parameters\n        const targetLUFS = -14.0;\n        const maxTruePeak = -1.0;\n\n        // Calculate required gain for LUFS normalization\n        const lufsGain = targetLUFS - lufs;\n\n        // Calculate required gain for True Peak limiting\n        const tpGain = maxTruePeak - truePeak;\n\n        // The actual gain applied is the minimum of both constraints\n        const gainApplied = Math.min(lufsGain, tpGain);\n\n        // Calculate normalized values\n        const normalizedLufs = lufs + gainApplied;\n        const normalizedTruePeak = truePeak + gainApplied;\n\n        // Determine bottleneck\n        const bottleneck = tpGain < lufsGain ? 'truePeak' : 'lufs';\n\n        return {\n            ...basicData,\n            lufs,\n            truePeak,\n            normalizedLufs,\n            normalizedTruePeak,\n            gainApplied,\n            bottleneck,\n            targetLUFS,\n            maxTruePeak,\n            lufsHeadroom: targetLUFS - normalizedLufs,\n            tpHeadroom: maxTruePeak - normalizedTruePeak,\n            duration: basicData.duration || this.estimateDuration(basicData.filePath)\n        };\n    }\n\n\n\n    generateMockAnalysis(filePath) {\n        // Generate realistic mock data for development\n        const mockLufs = -18 + (Math.random() * 8); // -18 to -10 LUFS\n        const mockTruePeak = -6 + (Math.random() * 5); // -6 to -1 dBTP\n        const mockDuration = 180 + (Math.random() * 120); // 3-5 minutes\n\n        const basicData = {\n            filePath,\n            lufs: mockLufs,\n            truePeak: mockTruePeak,\n            duration: mockDuration\n        };\n\n        return this.enhanceAnalysisData(basicData);\n    }\n\n    estimateDuration(filePath) {\n        // Mock duration estimation - in real implementation this would\n        // be calculated during audio file analysis\n        return 180 + (Math.random() * 120);\n    }\n\n    async loadAudioBuffer(filePath) {\n        try {\n            if (!this.audioContext) {\n                await this.initializeAudioContext();\n            }\n\n            // In a real implementation, this would load the audio file\n            // For now, we'll create a mock audio buffer\n            return this.createMockAudioBuffer();\n        } catch (error) {\n            console.error('Error loading audio buffer:', error);\n            return null;\n        }\n    }\n\n    createMockAudioBuffer() {\n        if (!this.audioContext) return null;\n\n        const sampleRate = this.audioContext.sampleRate;\n        const duration = 3; // 3 seconds of mock audio\n        const frameCount = sampleRate * duration;\n\n        const audioBuffer = this.audioContext.createBuffer(2, frameCount, sampleRate);\n\n        // Generate mock stereo audio data\n        for (let channel = 0; channel < audioBuffer.numberOfChannels; channel++) {\n            const channelData = audioBuffer.getChannelData(channel);\n            for (let i = 0; i < frameCount; i++) {\n                // Generate a simple sine wave with some noise\n                const t = i / sampleRate;\n                channelData[i] = Math.sin(2 * Math.PI * 440 * t) * 0.3 * Math.exp(-t * 0.5) +\n                    (Math.random() - 0.5) * 0.1;\n            }\n        }\n\n        return audioBuffer;\n    }\n\n    calculateWaveformData(audioBuffer, targetSamples = 1000) {\n        if (!audioBuffer) return null;\n\n        const channelData = audioBuffer.getChannelData(0); // Use left channel\n        const samplesPerPixel = Math.floor(channelData.length / targetSamples);\n        const waveformData = [];\n\n        for (let i = 0; i < targetSamples; i++) {\n            const start = i * samplesPerPixel;\n            const end = Math.min(start + samplesPerPixel, channelData.length);\n\n            let min = 0;\n            let max = 0;\n\n            for (let j = start; j < end; j++) {\n                const sample = channelData[j];\n                if (sample > max) max = sample;\n                if (sample < min) min = sample;\n            }\n\n            // Store the peak amplitude for this segment\n            waveformData.push(Math.max(Math.abs(min), Math.abs(max)));\n        }\n\n        return waveformData;\n    }\n\n    async normalizeAudio(audioBuffer, targetLUFS = -14, maxTruePeak = -1) {\n        try {\n            if (window.electronAPI) {\n                // Use Electron's audio processing\n                const result = await window.electronAPI.normalizeAudio(\n                    audioBuffer,\n                    targetLUFS,\n                    maxTruePeak\n                );\n                return result;\n            } else {\n                // Mock normalization for development\n                return {\n                    normalizedBuffer: audioBuffer,\n                    gainApplied: 0,\n                    finalLUFS: targetLUFS,\n                    finalTruePeak: maxTruePeak\n                };\n            }\n        } catch (error) {\n            console.error('Error normalizing audio:', error);\n            return null;\n        }\n    }\n\n    // Utility method to format LUFS values\n    formatLUFS(lufs) {\n        return `${lufs.toFixed(1)} LUFS`;\n    }\n\n    // Utility method to format True Peak values\n    formatTruePeak(truePeak) {\n        return `${truePeak.toFixed(1)} dBTP`;\n    }\n\n    // Utility method to format gain values\n    formatGain(gain) {\n        const sign = gain >= 0 ? '+' : '';\n        return `${sign}${gain.toFixed(1)} dB`;\n    }\n\n    // Clean up resources\n    dispose() {\n        if (this.audioContext && this.audioContext.state !== 'closed') {\n            this.audioContext.close();\n        }\n    }\n}\n\nexport default AudioAnalyzer;"], "mappings": "AAAA,OAAOA,WAAW,MAAM,eAAe;AACvC,SAASC,aAAa,QAAQ,sBAAsB;AAEpD,MAAMC,aAAa,CAAC;EAChBC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,WAAW,GAAG,IAAIJ,WAAW,CAAC,CAAC;IACpC,IAAI,CAACK,aAAa,GAAG,IAAIC,GAAG,CAAC,CAAC;IAC9B,IAAI,CAACC,YAAY,GAAG,IAAI;EAC5B;EAEA,MAAMC,WAAWA,CAACC,QAAQ,EAAE;IACxB,IAAI;MACA;MACA,IAAI,IAAI,CAACJ,aAAa,CAACK,GAAG,CAACD,QAAQ,CAAC,EAAE;QAClC,OAAO,IAAI,CAACJ,aAAa,CAACM,GAAG,CAACF,QAAQ,CAAC;MAC3C;MAEAG,OAAO,CAACC,GAAG,CAAC,0BAA0BJ,QAAQ,EAAE,CAAC;;MAEjD;MACAG,OAAO,CAACC,GAAG,CAAC,+CAA+C,CAAC;MAC5D,MAAMC,SAAS,GAAG,MAAM,IAAI,CAACV,WAAW,CAACW,aAAa,CAACN,QAAQ,CAAC;MAChEG,OAAO,CAACC,GAAG,CAAC,sCAAsC,CAAC;MACnD,MAAMG,YAAY,GAAG,IAAI,CAACZ,WAAW,CAACa,oBAAoB,CAAC,IAAI,CAAC;MAChEL,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEG,YAAY,GAAGA,YAAY,CAACE,MAAM,GAAG,MAAM,CAAC;;MAE/E;MACA,MAAMC,OAAO,GAAG,IAAI,CAACf,WAAW,CAACgB,qBAAqB,CAAC,CAAC;;MAExD;MACA,IAAIC,SAAS,GAAG,CAAC,IAAI,CAAC,CAAC;MACvB,IAAI;QACAA,SAAS,GAAG,MAAM,IAAI,CAACC,iBAAiB,CAACR,SAAS,CAACS,MAAM,CAAC;QAC1DX,OAAO,CAACC,GAAG,CAAC,yBAAyBQ,SAAS,CAACG,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC;MAChE,CAAC,CAAC,OAAOC,KAAK,EAAE;QACZb,OAAO,CAACa,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;QAChD;QACAJ,SAAS,GAAGF,OAAO,GAAGA,OAAO,CAACO,KAAK,GAAG,GAAG,GAAG,CAAC,IAAI;MACrD;;MAEA;MACA,MAAMC,gBAAgB,GAAG;QACrBlB,QAAQ;QACRmB,QAAQ,EAAEd,SAAS,CAACc,QAAQ;QAC5BC,UAAU,EAAEf,SAAS,CAACe,UAAU;QAChCC,QAAQ,EAAEhB,SAAS,CAACiB,gBAAgB;QAEpC;QACAf,YAAY;QAEZ;QACAgB,IAAI,EAAEb,OAAO,GAAGA,OAAO,CAACa,IAAI,GAAG,GAAG;QAClCC,MAAM,EAAEd,OAAO,GAAGA,OAAO,CAACc,MAAM,GAAG,CAAC,GAAG;QACvCC,GAAG,EAAEf,OAAO,GAAGA,OAAO,CAACe,GAAG,GAAG,GAAG;QAChCR,KAAK,EAAEP,OAAO,GAAGA,OAAO,CAACO,KAAK,GAAG,CAAC,IAAI;QAEtC;QACAS,IAAI,EAAEd,SAAS;QACfe,QAAQ,EAAEjB,OAAO,GAAGA,OAAO,CAACc,MAAM,GAAG,GAAG,GAAG,CAAC,GAAG;QAAE;;QAEjD;QACAI,UAAU,EAAE,CAAC,IAAI;QACjBC,WAAW,EAAE,CAAC,GAAG;QAEjBC,SAAS,EAAEC,IAAI,CAACC,GAAG,CAAC;MACxB,CAAC;;MAED;MACA,MAAMC,gBAAgB,GAAG,IAAI,CAACC,mBAAmB,CAAChB,gBAAgB,CAAC;;MAEnE;MACA,IAAI,CAACtB,aAAa,CAACuC,GAAG,CAACnC,QAAQ,EAAEiC,gBAAgB,CAAC;MAElD9B,OAAO,CAACC,GAAG,CAAC,0BAA0BJ,QAAQ,EAAE,CAAC;MACjDG,OAAO,CAACC,GAAG,CAAC,SAAS6B,gBAAgB,CAACP,IAAI,CAACX,OAAO,CAAC,CAAC,CAAC,gBAAgBkB,gBAAgB,CAACN,QAAQ,CAACZ,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC;MAEjH,OAAOkB,gBAAgB;IAC3B,CAAC,CAAC,OAAOjB,KAAK,EAAE;MACZb,OAAO,CAACa,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;MACnD,OAAO,IAAI,CAACoB,oBAAoB,CAACpC,QAAQ,CAAC;IAC9C;EACJ;;EAEA;AACJ;AACA;EACI,MAAMa,iBAAiBA,CAACwB,WAAW,EAAE;IACjC,IAAI;MACA;MACA,IAAI,CAAC,IAAI,CAACvC,YAAY,EAAE;QACpB,IAAI,CAACA,YAAY,GAAG,KAAIwC,MAAM,CAACC,YAAY,IAAID,MAAM,CAACE,kBAAkB,EAAE,CAAC;MAC/E;;MAEA;MACA,MAAMC,cAAc,GAAG,IAAIC,mBAAmB,CAC1CL,WAAW,CAACf,gBAAgB,EAC5Be,WAAW,CAAC5B,MAAM,EAClB4B,WAAW,CAACjB,UAChB,CAAC;;MAED;MACA,MAAMuB,MAAM,GAAGF,cAAc,CAACG,kBAAkB,CAAC,CAAC;MAClDD,MAAM,CAAC7B,MAAM,GAAGuB,WAAW;;MAE3B;MACA,MAAMQ,aAAa,GAAG,IAAIrD,aAAa,CAAC;QACpCmD,MAAM,EAAEA,MAAM;QACdG,KAAK,EAAE,CAAC,YAAY,CAAC;QACrBC,SAAS,EAAE;MACf,CAAC,CAAC;MAEF,OAAO,IAAIC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;QACpC,IAAIC,UAAU,GAAG,IAAI;QAErBN,aAAa,CAACO,EAAE,CAAC,eAAe,EAAGC,KAAK,IAAK;UACzC,IAAIA,KAAK,CAACC,IAAI,CAACC,IAAI,KAAK,YAAY,EAAE;YAClCJ,UAAU,GAAGE,KAAK,CAACC,IAAI,CAACE,KAAK;YAC7BrD,OAAO,CAACC,GAAG,CAAC,wBAAwB+C,UAAU,EAAE,CAAC;UACrD;QACJ,CAAC,CAAC;QAEFN,aAAa,CAACO,EAAE,CAAC,MAAM,EAAE,MAAM;UAC3B,IAAID,UAAU,KAAK,IAAI,EAAE;YACrBF,OAAO,CAACE,UAAU,CAAC;UACvB,CAAC,MAAM;YACHD,MAAM,CAAC,IAAIO,KAAK,CAAC,8BAA8B,CAAC,CAAC;UACrD;QACJ,CAAC,CAAC;QAEFZ,aAAa,CAACO,EAAE,CAAC,OAAO,EAAGpC,KAAK,IAAK;UACjCb,OAAO,CAACa,KAAK,CAAC,gBAAgB,EAAEA,KAAK,CAAC;UACtCkC,MAAM,CAAClC,KAAK,CAAC;QACjB,CAAC,CAAC;;QAEF;QACA,IAAI;UACA6B,aAAa,CAACa,KAAK,CAAC,CAAC;QACzB,CAAC,CAAC,OAAO1C,KAAK,EAAE;UACZkC,MAAM,CAAClC,KAAK,CAAC;QACjB;;QAEA;QACA2C,UAAU,CAAC,MAAM;UACb,IAAIR,UAAU,KAAK,IAAI,EAAE;YACrBD,MAAM,CAAC,IAAIO,KAAK,CAAC,0BAA0B,CAAC,CAAC;UACjD;QACJ,CAAC,EAAE,IAAI,CAAC;MACZ,CAAC,CAAC;IAEN,CAAC,CAAC,OAAOzC,KAAK,EAAE;MACZb,OAAO,CAACa,KAAK,CAAC,oCAAoC,EAAEA,KAAK,CAAC;MAC1D,MAAMA,KAAK;IACf;EACJ;;EAEA;AACJ;AACA;EACI,MAAM4C,wBAAwBA,CAACC,eAAe,EAAE;IAC5C,IAAI;MACA;MACA,IAAI,CAAC,IAAI,CAAC/D,YAAY,EAAE;QACpB,IAAI,CAACA,YAAY,GAAG,KAAIwC,MAAM,CAACC,YAAY,IAAID,MAAM,CAACE,kBAAkB,EAAE,CAAC;MAC/E;;MAEA;MACA,MAAMsB,SAAS,GAAGC,UAAU,CAACC,IAAI,CAACC,IAAI,CAACJ,eAAe,CAAC,EAAEK,CAAC,IAAIA,CAAC,CAACC,UAAU,CAAC,CAAC,CAAC,CAAC;;MAE9E;MACA,MAAM9B,WAAW,GAAG,MAAM,IAAI,CAACvC,YAAY,CAACsE,eAAe,CAACN,SAAS,CAAChD,MAAM,CAAC;;MAE7E;MACA,MAAM2B,cAAc,GAAG,IAAIC,mBAAmB,CAC1CL,WAAW,CAACf,gBAAgB,EAC5Be,WAAW,CAAC5B,MAAM,EAClB4B,WAAW,CAACjB,UAChB,CAAC;;MAED;MACA,MAAMuB,MAAM,GAAGF,cAAc,CAACG,kBAAkB,CAAC,CAAC;MAClDD,MAAM,CAAC7B,MAAM,GAAGuB,WAAW;;MAE3B;MACA,MAAMQ,aAAa,GAAG,IAAIrD,aAAa,CAAC;QACpCmD,MAAM,EAAEA,MAAM;QACdG,KAAK,EAAE,CAAC,YAAY,EAAE,WAAW,EAAE,YAAY,CAAC;QAChDC,SAAS,EAAE;MACf,CAAC,CAAC;;MAEF;MACA,MAAMsB,YAAY,GAAG;QACjBC,UAAU,EAAE,IAAI;QAChBC,SAAS,EAAE,EAAE;QACbC,SAAS,EAAE;MACf,CAAC;MAED,OAAO,IAAIxB,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;QACpCL,aAAa,CAACO,EAAE,CAAC,eAAe,EAAGC,KAAK,IAAK;UACzC,MAAM;YAAEE,IAAI;YAAEC;UAAM,CAAC,GAAGH,KAAK,CAACC,IAAI;UAElC,IAAIC,IAAI,KAAK,YAAY,EAAE;YACvBc,YAAY,CAACC,UAAU,GAAGd,KAAK;UACnC,CAAC,MAAM,IAAID,IAAI,KAAK,WAAW,EAAE;YAC7Bc,YAAY,CAACE,SAAS,CAACE,IAAI,CAACjB,KAAK,CAAC;UACtC,CAAC,MAAM,IAAID,IAAI,KAAK,YAAY,EAAE;YAC9Bc,YAAY,CAACG,SAAS,CAACC,IAAI,CAACjB,KAAK,CAAC;UACtC;QACJ,CAAC,CAAC;QAEFX,aAAa,CAACO,EAAE,CAAC,MAAM,EAAE,MAAM;UAC3B;UACA,MAAMsB,YAAY,GAAGL,YAAY,CAACE,SAAS,CAAC9D,MAAM,GAAG,CAAC,GAClD4D,YAAY,CAACE,SAAS,CAACI,MAAM,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,GAAGC,CAAC,CAAC,GAAGR,YAAY,CAACE,SAAS,CAAC9D,MAAM,GAC9E4D,YAAY,CAACC,UAAU;UAE3B,MAAMQ,YAAY,GAAGT,YAAY,CAACG,SAAS,CAAC/D,MAAM,GAAG,CAAC,GAClD4D,YAAY,CAACG,SAAS,CAACG,MAAM,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,GAAGC,CAAC,CAAC,GAAGR,YAAY,CAACG,SAAS,CAAC/D,MAAM,GAC9E4D,YAAY,CAACC,UAAU;UAE3BrB,OAAO,CAAC;YACJqB,UAAU,EAAED,YAAY,CAACC,UAAU,IAAI,CAAC,IAAI;YAAE;YAC9CC,SAAS,EAAEG,YAAY,IAAIL,YAAY,CAACC,UAAU,IAAI,CAAC,IAAI;YAC3DE,SAAS,EAAEM,YAAY,IAAIT,YAAY,CAACC,UAAU,IAAI,CAAC;UAC3D,CAAC,CAAC;QACN,CAAC,CAAC;QAEFzB,aAAa,CAACO,EAAE,CAAC,OAAO,EAAGpC,KAAK,IAAK;UACjCb,OAAO,CAACa,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;UACvD;UACAiC,OAAO,CAAC;YACJqB,UAAU,EAAE,CAAC,IAAI,IAAIS,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;YAAE;YAC7CT,SAAS,EAAE,CAAC,IAAI,IAAIQ,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;YAC1CR,SAAS,EAAE,CAAC,IAAI,IAAIO,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC;UAC7C,CAAC,CAAC;QACN,CAAC,CAAC;;QAEF;QACAnC,aAAa,CAACa,KAAK,CAAC,CAAC;;QAErB;QACAC,UAAU,CAAC,MAAM;UACb,IAAIU,YAAY,CAACC,UAAU,KAAK,IAAI,EAAE;YAClCnE,OAAO,CAAC8E,IAAI,CAAC,0CAA0C,CAAC;YACxDhC,OAAO,CAAC;cACJqB,UAAU,EAAE,CAAC,IAAI,IAAIS,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;cAC3CT,SAAS,EAAE,CAAC,IAAI,IAAIQ,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;cAC1CR,SAAS,EAAE,CAAC,IAAI,IAAIO,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC;YAC7C,CAAC,CAAC;UACN;QACJ,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;MACf,CAAC,CAAC;IAEN,CAAC,CAAC,OAAOhE,KAAK,EAAE;MACZb,OAAO,CAACa,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;MAClD;MACA,OAAO;QACHsD,UAAU,EAAE,CAAC,IAAI,IAAIS,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QAC3CT,SAAS,EAAE,CAAC,IAAI,IAAIQ,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QAC1CR,SAAS,EAAE,CAAC,IAAI,IAAIO,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC;MAC7C,CAAC;IACL;EACJ;EAEA9C,mBAAmBA,CAACgD,SAAS,EAAE;IAC3B,MAAM;MAAExD,IAAI;MAAEC;IAAS,CAAC,GAAGuD,SAAS;;IAEpC;IACA,MAAMtD,UAAU,GAAG,CAAC,IAAI;IACxB,MAAMC,WAAW,GAAG,CAAC,GAAG;;IAExB;IACA,MAAMsD,QAAQ,GAAGvD,UAAU,GAAGF,IAAI;;IAElC;IACA,MAAM0D,MAAM,GAAGvD,WAAW,GAAGF,QAAQ;;IAErC;IACA,MAAM0D,WAAW,GAAGN,IAAI,CAACO,GAAG,CAACH,QAAQ,EAAEC,MAAM,CAAC;;IAE9C;IACA,MAAMG,cAAc,GAAG7D,IAAI,GAAG2D,WAAW;IACzC,MAAMG,kBAAkB,GAAG7D,QAAQ,GAAG0D,WAAW;;IAEjD;IACA,MAAMI,UAAU,GAAGL,MAAM,GAAGD,QAAQ,GAAG,UAAU,GAAG,MAAM;IAE1D,OAAO;MACH,GAAGD,SAAS;MACZxD,IAAI;MACJC,QAAQ;MACR4D,cAAc;MACdC,kBAAkB;MAClBH,WAAW;MACXI,UAAU;MACV7D,UAAU;MACVC,WAAW;MACX6D,YAAY,EAAE9D,UAAU,GAAG2D,cAAc;MACzCI,UAAU,EAAE9D,WAAW,GAAG2D,kBAAkB;MAC5CrE,QAAQ,EAAE+D,SAAS,CAAC/D,QAAQ,IAAI,IAAI,CAACyE,gBAAgB,CAACV,SAAS,CAAClF,QAAQ;IAC5E,CAAC;EACL;EAIAoC,oBAAoBA,CAACpC,QAAQ,EAAE;IAC3B;IACA,MAAM6F,QAAQ,GAAG,CAAC,EAAE,GAAId,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,CAAE,CAAC,CAAC;IAC5C,MAAMc,YAAY,GAAG,CAAC,CAAC,GAAIf,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,CAAE,CAAC,CAAC;IAC/C,MAAMe,YAAY,GAAG,GAAG,GAAIhB,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAI,CAAC,CAAC;;IAElD,MAAME,SAAS,GAAG;MACdlF,QAAQ;MACR0B,IAAI,EAAEmE,QAAQ;MACdlE,QAAQ,EAAEmE,YAAY;MACtB3E,QAAQ,EAAE4E;IACd,CAAC;IAED,OAAO,IAAI,CAAC7D,mBAAmB,CAACgD,SAAS,CAAC;EAC9C;EAEAU,gBAAgBA,CAAC5F,QAAQ,EAAE;IACvB;IACA;IACA,OAAO,GAAG,GAAI+E,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAI;EACtC;EAEA,MAAMgB,eAAeA,CAAChG,QAAQ,EAAE;IAC5B,IAAI;MACA,IAAI,CAAC,IAAI,CAACF,YAAY,EAAE;QACpB,MAAM,IAAI,CAACmG,sBAAsB,CAAC,CAAC;MACvC;;MAEA;MACA;MACA,OAAO,IAAI,CAACC,qBAAqB,CAAC,CAAC;IACvC,CAAC,CAAC,OAAOlF,KAAK,EAAE;MACZb,OAAO,CAACa,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;MACnD,OAAO,IAAI;IACf;EACJ;EAEAkF,qBAAqBA,CAAA,EAAG;IACpB,IAAI,CAAC,IAAI,CAACpG,YAAY,EAAE,OAAO,IAAI;IAEnC,MAAMsB,UAAU,GAAG,IAAI,CAACtB,YAAY,CAACsB,UAAU;IAC/C,MAAMD,QAAQ,GAAG,CAAC,CAAC,CAAC;IACpB,MAAMgF,UAAU,GAAG/E,UAAU,GAAGD,QAAQ;IAExC,MAAMkB,WAAW,GAAG,IAAI,CAACvC,YAAY,CAACsG,YAAY,CAAC,CAAC,EAAED,UAAU,EAAE/E,UAAU,CAAC;;IAE7E;IACA,KAAK,IAAIiF,OAAO,GAAG,CAAC,EAAEA,OAAO,GAAGhE,WAAW,CAACf,gBAAgB,EAAE+E,OAAO,EAAE,EAAE;MACrE,MAAMC,WAAW,GAAGjE,WAAW,CAACkE,cAAc,CAACF,OAAO,CAAC;MACvD,KAAK,IAAIG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGL,UAAU,EAAEK,CAAC,EAAE,EAAE;QACjC;QACA,MAAMC,CAAC,GAAGD,CAAC,GAAGpF,UAAU;QACxBkF,WAAW,CAACE,CAAC,CAAC,GAAGzB,IAAI,CAAC2B,GAAG,CAAC,CAAC,GAAG3B,IAAI,CAAC4B,EAAE,GAAG,GAAG,GAAGF,CAAC,CAAC,GAAG,GAAG,GAAG1B,IAAI,CAAC6B,GAAG,CAAC,CAACH,CAAC,GAAG,GAAG,CAAC,GACvE,CAAC1B,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG,IAAI,GAAG;MACnC;IACJ;IAEA,OAAO3C,WAAW;EACtB;EAEAwE,qBAAqBA,CAACxE,WAAW,EAAEyE,aAAa,GAAG,IAAI,EAAE;IACrD,IAAI,CAACzE,WAAW,EAAE,OAAO,IAAI;IAE7B,MAAMiE,WAAW,GAAGjE,WAAW,CAACkE,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC;IACnD,MAAMQ,eAAe,GAAGhC,IAAI,CAACiC,KAAK,CAACV,WAAW,CAAC7F,MAAM,GAAGqG,aAAa,CAAC;IACtE,MAAMvG,YAAY,GAAG,EAAE;IAEvB,KAAK,IAAIiG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGM,aAAa,EAAEN,CAAC,EAAE,EAAE;MACpC,MAAM9C,KAAK,GAAG8C,CAAC,GAAGO,eAAe;MACjC,MAAME,GAAG,GAAGlC,IAAI,CAACO,GAAG,CAAC5B,KAAK,GAAGqD,eAAe,EAAET,WAAW,CAAC7F,MAAM,CAAC;MAEjE,IAAI6E,GAAG,GAAG,CAAC;MACX,IAAI4B,GAAG,GAAG,CAAC;MAEX,KAAK,IAAIC,CAAC,GAAGzD,KAAK,EAAEyD,CAAC,GAAGF,GAAG,EAAEE,CAAC,EAAE,EAAE;QAC9B,MAAMC,MAAM,GAAGd,WAAW,CAACa,CAAC,CAAC;QAC7B,IAAIC,MAAM,GAAGF,GAAG,EAAEA,GAAG,GAAGE,MAAM;QAC9B,IAAIA,MAAM,GAAG9B,GAAG,EAAEA,GAAG,GAAG8B,MAAM;MAClC;;MAEA;MACA7G,YAAY,CAACkE,IAAI,CAACM,IAAI,CAACmC,GAAG,CAACnC,IAAI,CAACsC,GAAG,CAAC/B,GAAG,CAAC,EAAEP,IAAI,CAACsC,GAAG,CAACH,GAAG,CAAC,CAAC,CAAC;IAC7D;IAEA,OAAO3G,YAAY;EACvB;EAEA,MAAM+G,cAAcA,CAACjF,WAAW,EAAET,UAAU,GAAG,CAAC,EAAE,EAAEC,WAAW,GAAG,CAAC,CAAC,EAAE;IAClE,IAAI;MACA,IAAIS,MAAM,CAACiF,WAAW,EAAE;QACpB;QACA,MAAMC,MAAM,GAAG,MAAMlF,MAAM,CAACiF,WAAW,CAACD,cAAc,CAClDjF,WAAW,EACXT,UAAU,EACVC,WACJ,CAAC;QACD,OAAO2F,MAAM;MACjB,CAAC,MAAM;QACH;QACA,OAAO;UACHC,gBAAgB,EAAEpF,WAAW;UAC7BgD,WAAW,EAAE,CAAC;UACdqC,SAAS,EAAE9F,UAAU;UACrB+F,aAAa,EAAE9F;QACnB,CAAC;MACL;IACJ,CAAC,CAAC,OAAOb,KAAK,EAAE;MACZb,OAAO,CAACa,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;MAChD,OAAO,IAAI;IACf;EACJ;;EAEA;EACA4G,UAAUA,CAAClG,IAAI,EAAE;IACb,OAAO,GAAGA,IAAI,CAACX,OAAO,CAAC,CAAC,CAAC,OAAO;EACpC;;EAEA;EACA8G,cAAcA,CAAClG,QAAQ,EAAE;IACrB,OAAO,GAAGA,QAAQ,CAACZ,OAAO,CAAC,CAAC,CAAC,OAAO;EACxC;;EAEA;EACA+G,UAAUA,CAACC,IAAI,EAAE;IACb,MAAMC,IAAI,GAAGD,IAAI,IAAI,CAAC,GAAG,GAAG,GAAG,EAAE;IACjC,OAAO,GAAGC,IAAI,GAAGD,IAAI,CAAChH,OAAO,CAAC,CAAC,CAAC,KAAK;EACzC;;EAEA;EACAkH,OAAOA,CAAA,EAAG;IACN,IAAI,IAAI,CAACnI,YAAY,IAAI,IAAI,CAACA,YAAY,CAACoI,KAAK,KAAK,QAAQ,EAAE;MAC3D,IAAI,CAACpI,YAAY,CAACqI,KAAK,CAAC,CAAC;IAC7B;EACJ;AACJ;AAEA,eAAe1I,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}