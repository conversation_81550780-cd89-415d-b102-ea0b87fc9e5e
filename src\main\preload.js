const { contextBridge, ipc<PERSON><PERSON><PERSON> } = require('electron');

// Expose protected methods that allow the renderer process to use
// the ipc<PERSON><PERSON>er without exposing the entire object
contextBridge.exposeInMainWorld('electronAPI', {
    // File operations
    openFileDialog: () => ipcRenderer.invoke('open-file-dialog'),
    processDroppedFiles: (filePaths) => ipcRenderer.invoke('process-dropped-files', filePaths),

    // Audio analysis
    analyzeAudioFile: (filePath) => ipcRenderer.invoke('analyze-audio-file', filePath),
    updateLufsAnalysis: (filePath, lufsData) => ipcRenderer.invoke('update-lufs-analysis', filePath, lufsData),
    readAudioFile: (filePath) => ipcRenderer.invoke('read-audio-file', filePath),
    normalizeAudio: (audioData, targetLUFS, maxTruePeak) =>
        ipcRenderer.invoke('normalize-audio', audioData, targetLUFS, maxTruePeak),

    // Platform info
    platform: process.platform,

    // Version info
    versions: {
        node: process.versions.node,
        chrome: process.versions.chrome,
        electron: process.versions.electron
    }
});