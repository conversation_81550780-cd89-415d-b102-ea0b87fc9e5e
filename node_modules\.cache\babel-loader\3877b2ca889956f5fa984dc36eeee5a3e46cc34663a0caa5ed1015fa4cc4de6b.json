{"ast": null, "code": "// Calculations from https://doxy.audacityteam.org/_e_b_u_r128_8cpp_source.html\n\nfunction preFilterCoefficients(fs) {\n  const db = 3.999843853973347;\n  const f0 = 1681.974450955533;\n  const Q = 0.7071752369554196;\n  const K = Math.tan(Math.PI * f0 / fs);\n  const Vh = Math.pow(10, db / 20);\n  const Vb = Math.pow(Vh, 0.4996667741545416);\n  const denominator0 = 1 + K / Q + K * K;\n  const denominator1 = 2 * (K * K - 1) / denominator0;\n  const denominator2 = (1 - K / Q + K * K) / denominator0;\n  const numerator0 = (Vh + Vb * K / Q + K * K) / denominator0;\n  const numerator1 = 2 * (K * K - Vh) / denominator0;\n  const numerator2 = (Vh - Vb * K / Q + K * K) / denominator0;\n  return {\n    numerators: [numerator0, numerator1, numerator2],\n    denominators: [1, denominator1, denominator2]\n  };\n}\nfunction weightingFilterCoefficients(fs) {\n  const f0 = 38.13547087602444;\n  const Q = 0.5003270373238773;\n  const K = Math.tan(Math.PI * f0 / fs);\n  const denominator1 = 2 * (K * K - 1) / (1 + K / Q + K * K);\n  const denominator2 = (1 - K / Q + K * K) / (1 + K / Q + K * K);\n  const numerator0 = 1;\n  const numerator1 = -2;\n  const numerator2 = 1;\n  return {\n    numerators: [numerator0, numerator1, numerator2],\n    denominators: [1, denominator1, denominator2]\n  };\n}\n\n// Use biquad filters with matched frequency responses when IIR filters are unsupported\n\nfunction preFilter(audioContext) {\n  if ('createIIRFilter' in audioContext) {\n    const coefficients = preFilterCoefficients(audioContext.sampleRate);\n    return audioContext.createIIRFilter(coefficients.numerators, coefficients.denominators);\n  } else {\n    const filter = audioContext.createBiquadFilter();\n    filter.type = 'highshelf';\n    filter.frequency.value = 1500;\n    filter.gain.value = 4;\n    return filter;\n  }\n}\nfunction weightingFilter(audioContext) {\n  if ('createIIRFilter' in audioContext) {\n    const coefficients = weightingFilterCoefficients(audioContext.sampleRate);\n    return audioContext.createIIRFilter(coefficients.numerators, coefficients.denominators);\n  } else {\n    const filter = audioContext.createBiquadFilter();\n    filter.type = 'highpass';\n    filter.frequency.value = 38;\n    filter.Q.value = -6;\n    return filter;\n  }\n}\nvar events = {\n  on: function (type, listener) {\n    (this._listeners[type] = this._listeners[type] || []).push(listener);\n  },\n  off: function (type, listener) {\n    if (!type) {\n      this._listeners = {};\n      return;\n    }\n    if (listener) {\n      this._listeners[type] = (this._listeners[type] || []).filter(l => l !== listener);\n    } else {\n      delete this._listeners[type];\n    }\n  },\n  trigger: function (type, data) {\n    (this._listeners[type] || []).forEach(listener => {\n      listener({\n        type: type,\n        data: data\n      });\n    });\n  }\n};\n\n/**\n * Wrapper around the AudioWorkletNode.\n * Sets up the worker and the node to provide a standard interface for\n * processing audio.\n */\n\nclass AudioWorkletAdapter {\n  constructor(controller, name, uri) {\n    this.source = controller.source;\n    this.context = this.source.context;\n    this.name = name;\n    this.uri = uri;\n    this.node.then(node => {\n      node.port.onmessage = function (event) {\n        controller.trigger(event.data.type, event.data);\n      };\n    });\n  }\n  get node() {\n    if (this._node) return this._node;\n    this._node = new Promise((resolve, reject) => {\n      return this.context.audioWorklet.addModule(this.uri).then(() => {\n        return resolve(new AudioWorkletNode(this.context, this.name));\n      }).catch(reject);\n    });\n    return this._node;\n  }\n  message(data) {\n    this.node.then(node => node.port.postMessage(data));\n  }\n}\n\n/**\n * Wrapper around the ScriptProcessorNode.\n * Sets up the worker and the node to provide a standard interface for\n * processing audio.\n */\n\nclass ScriptProcessorAdapter {\n  constructor(controller, path) {\n    this.source = controller.source;\n    this.context = this.source.context;\n    this.worker = new Worker(path);\n    this.node.then(node => {\n      node.onaudioprocess = event => {\n        const channels = [];\n        for (var i = 0; i < this.source.channelCount; i++) {\n          channels[i] = event.inputBuffer.getChannelData(i);\n        }\n        this.worker.postMessage({\n          type: 'process',\n          input: channels\n        });\n      };\n    });\n    this.worker.onmessage = event => {\n      controller.trigger(event.data.type, event.data);\n    };\n  }\n  get node() {\n    if (this._node) return this._node;\n    this._node = new Promise((resolve, reject) => {\n      resolve(this._createNode(1024, this.source.channelCount, this.source.channelCount));\n    });\n    return this._node;\n  }\n  message(data) {\n    this.worker.postMessage(data);\n  }\n  _createNode() {\n    return (this.context.createScriptProcessor || this.context.createJavaScriptNode).apply(this.context, arguments);\n  }\n}\n\n/**\n * Adapter for offline analysis.\n * No need to set up scriptProcessorNode or audioWorkletNode. Audio data is\n * already decoded and can just be passed to the worker. `node` is just a\n * placeholder gain node for adapter API parity.\n */\nclass OfflineAdapter {\n  constructor(controller, path) {\n    this.source = controller.source;\n    this.context = this.source.context;\n    this.worker = new Worker(path);\n    this.worker.onmessage = event => {\n      controller.trigger(event.data.type, event.data);\n    };\n  }\n  message(data) {\n    this.worker.postMessage(data);\n  }\n  get node() {\n    if (this._node) return this._node;\n    this._node = new Promise((resolve, reject) => {\n      resolve(this.context.createGain());\n    });\n    return this._node;\n  }\n}\n\n// Disable AudioWorklet because it currently results in glitchy audio playback\nconst audioWorkletEnabled = false;\n\n/**\n * Factory which returns either an OfflineAdapter, AudioWorkletAdapter, or\n * ScriptProcessorAdapter, depending on browser support / mode.\n */\n\nfunction WorkerAdapter({\n  context,\n  source,\n  controller\n}) {\n  const adapter = _adapter(controller);\n  adapter.node.then(node => {\n    node.connect(context.destination);\n    source.connect(node);\n  });\n  return adapter;\n}\nfunction _adapter(controller) {\n  if (controller.offline) {\n    return new OfflineAdapter(controller, controller.workerUri);\n  }\n  if ('AudioWorkletNode' in window && audioWorkletEnabled) {\n    return new AudioWorkletAdapter(controller, 'needles-worklet', controller.workletUri);\n  } else {\n    return new ScriptProcessorAdapter(controller, controller.workerUri);\n  }\n}\nclass InvalidStateError extends Error {\n  constructor(message) {\n    super(message);\n    this.name = 'InvalidStateError';\n  }\n}\nconst OfflineAudioContext = window.OfflineAudioContext || window.webkitOfflineAudioContext;\nclass Controller {\n  constructor(options) {\n    this.state = 'inactive';\n    this._listeners = {};\n    Object.assign(this, events);\n    this.workerUri = options.workerUri;\n    this.workletUri = options.workletUri;\n    this.source = options.source;\n    this.weightedSource = options.weightedSource;\n    this.context = this.source.context;\n    this.offline = this.context instanceof OfflineAudioContext;\n    this.workerAdapter.message({\n      type: 'initialize',\n      attributes: {\n        sampleRate: this.context.sampleRate,\n        modes: options.modes\n      }\n    });\n  }\n  get workerAdapter() {\n    return this._workerAdapter = this._workerAdapter || new WorkerAdapter({\n      controller: this,\n      context: this.context,\n      source: this.weightedSource || this.source\n    });\n  }\n  input(audioBuffer) {\n    const chunkLength = 16384;\n    const audioBufferLength = audioBuffer.length;\n    const channelLength = audioBuffer.numberOfChannels;\n    if (this.offline) {\n      this.workerAdapter.message({\n        type: 'set',\n        key: 'duration',\n        value: audioBuffer.duration * 1000\n      });\n    }\n\n    // Refactor to support Safari (where copyFromChannel is unsupported)\n    for (var i = 0; i < audioBufferLength; i += chunkLength) {\n      const block = [];\n      for (var channel = 0; channel < channelLength; channel++) {\n        block[channel] = new Float32Array(chunkLength);\n        audioBuffer.copyFromChannel(block[channel], channel, i);\n      }\n      this.workerAdapter.message({\n        type: 'process',\n        input: block\n      });\n    }\n  }\n  start() {\n    if (this.state !== 'inactive') this._throwInvalidStateErrorFor('start');\n    this.state = 'recording';\n    this.workerAdapter.message({\n      type: 'record'\n    });\n    if (this.offline) {\n      this.source.start();\n      this._startRendering().then(renderedBuffer => this.input(renderedBuffer));\n    }\n  }\n  pause() {\n    if (this.state === 'inactive') this._throwInvalidStateErrorFor('pause');\n    this.state = 'paused';\n    this.workerAdapter.message({\n      type: 'pause'\n    });\n  }\n  resume() {\n    if (this.state === 'inactive') this._throwInvalidStateErrorFor('resume');\n    this.state = 'recording';\n    this.workerAdapter.message({\n      type: 'resume'\n    });\n  }\n  stop() {\n    if (this.state === 'inactive') this._throwInvalidStateErrorFor('stop');\n    this.state = 'inactive';\n    this.workerAdapter.message({\n      type: 'stop'\n    });\n  }\n  reset() {\n    this.workerAdapter.message({\n      type: 'reset'\n    });\n  }\n  _startRendering() {\n    return new Promise((resolve, reject) => {\n      this.context.startRendering();\n      this.context.addEventListener('complete', event => {\n        resolve(event.renderedBuffer);\n      });\n    });\n  }\n  _throwInvalidStateErrorFor(action) {\n    throw new InvalidStateError(`Failed to execute '${action}' on 'Needles': The Needles's state is '${this.state}'.`);\n  }\n}\nfunction LoudnessMeter(options) {\n  options.modes = options.modes || ['momentary', 'short-term', 'integrated'];\n  const context = options.source.context;\n  const filter1 = preFilter(context);\n  const filter2 = weightingFilter(context);\n  options.source.connect(filter1);\n  filter1.connect(filter2);\n  return new Controller({\n    ...options,\n    weightedSource: filter2\n  });\n}\nexport { LoudnessMeter };", "map": {"version": 3, "names": ["preFilterCoefficients", "fs", "db", "f0", "Q", "K", "Math", "tan", "PI", "Vh", "pow", "Vb", "denominator0", "denominator1", "denominator2", "numerator0", "numerator1", "numerator2", "numerators", "denominators", "weightingFilterCoefficients", "preFilter", "audioContext", "coefficients", "sampleRate", "createIIRFilter", "filter", "createBiquadFilter", "type", "frequency", "value", "gain", "weightingFilter", "events", "on", "listener", "_listeners", "push", "off", "l", "trigger", "data", "for<PERSON>ach", "AudioWorkletAdapter", "constructor", "controller", "name", "uri", "source", "context", "node", "then", "port", "onmessage", "event", "_node", "Promise", "resolve", "reject", "audioWorklet", "addModule", "AudioWorkletNode", "catch", "message", "postMessage", "ScriptProcessorAdapter", "path", "worker", "Worker", "onaudioprocess", "channels", "i", "channelCount", "inputBuffer", "getChannelData", "input", "_createNode", "createScriptProcessor", "createJavaScriptNode", "apply", "arguments", "OfflineAdapter", "createGain", "audioWorkletEnabled", "WorkerAdapter", "adapter", "_adapter", "connect", "destination", "offline", "workerUri", "window", "workletUri", "InvalidStateError", "Error", "OfflineAudioContext", "webkitOfflineAudioContext", "Controller", "options", "state", "Object", "assign", "weightedSource", "workerAdapter", "attributes", "modes", "_workerAdapter", "audioBuffer", "chunkLength", "audioBufferLength", "length", "channelLength", "numberOfChannels", "key", "duration", "block", "channel", "Float32Array", "copyFromChannel", "start", "_throwInvalidStateErrorFor", "_startRendering", "<PERSON><PERSON><PERSON><PERSON>", "pause", "resume", "stop", "reset", "startRendering", "addEventListener", "action", "LoudnessMeter", "filter1", "filter2"], "sources": ["C:/Users/<USER>/Desktop/VSC Folder/AlbumPlayer/node_modules/@domchristie/needles/dist/needles.js"], "sourcesContent": ["// Calculations from https://doxy.audacityteam.org/_e_b_u_r128_8cpp_source.html\n\nfunction preFilterCoefficients (fs) {\n  const db = 3.999843853973347;\n  const f0 = 1681.974450955533;\n  const Q = 0.7071752369554196;\n  const K = Math.tan(Math.PI * f0 / fs);\n\n  const Vh = Math.pow(10, db / 20);\n  const Vb = Math.pow(Vh, 0.4996667741545416);\n\n  const denominator0 = 1 + K / Q + K * K;\n  const denominator1 = 2 * (K * K - 1) / denominator0;\n  const denominator2 = (1 - K / Q + K * K) / denominator0;\n  const numerator0 = (Vh + Vb * K / Q + K * K) / denominator0;\n  const numerator1 = 2 * (K * K - Vh) / denominator0;\n  const numerator2 = (Vh - Vb * K / Q + K * K) / denominator0;\n\n  return {\n    numerators: [numerator0, numerator1, numerator2],\n    denominators: [1, denominator1, denominator2]\n  }\n}\n\nfunction weightingFilterCoefficients (fs) {\n  const f0 = 38.13547087602444;\n  const Q = 0.5003270373238773;\n  const K = Math.tan(Math.PI * f0 / fs);\n\n  const denominator1 = 2 * (K * K - 1) / (1 + K / Q + K * K);\n  const denominator2 = (1 - K / Q + K * K) / (1 + K / Q + K * K);\n  const numerator0 = 1;\n  const numerator1 = -2;\n  const numerator2 = 1;\n\n  return {\n    numerators: [numerator0, numerator1, numerator2],\n    denominators: [1, denominator1, denominator2]\n  }\n}\n\n\n// Use biquad filters with matched frequency responses when IIR filters are unsupported\n\nfunction preFilter (audioContext) {\n  if ('createIIRFilter' in audioContext) {\n    const coefficients = preFilterCoefficients(audioContext.sampleRate);\n    return audioContext.createIIRFilter(\n      coefficients.numerators,\n      coefficients.denominators\n    )\n  } else {\n    const filter = audioContext.createBiquadFilter();\n    filter.type = 'highshelf';\n    filter.frequency.value = 1500;\n    filter.gain.value = 4;\n    return filter\n  }\n}\n\nfunction weightingFilter(audioContext) {\n  if ('createIIRFilter' in audioContext) {\n    const coefficients = weightingFilterCoefficients(audioContext.sampleRate);\n    return audioContext.createIIRFilter(\n      coefficients.numerators,\n      coefficients.denominators\n    )\n  } else {\n    const filter = audioContext.createBiquadFilter();\n    filter.type = 'highpass';\n    filter.frequency.value = 38;\n    filter.Q.value = -6;\n    return filter\n  }\n\n}\n\nvar events = {\n  on: function (type, listener) {\n    (this._listeners[type] = this._listeners[type] || []).push(listener);\n  },\n\n  off: function (type, listener) {\n    if (!type) {\n      this._listeners = {};\n      return\n    }\n\n    if (listener) {\n      this._listeners[type] = (this._listeners[type] || []).filter(l => l !== listener);\n    } else {\n      delete this._listeners[type];\n    }\n  },\n\n  trigger: function (type, data) {\n    (this._listeners[type] || []).forEach((listener) => {\n      listener({ type: type, data: data });\n    });\n  }\n};\n\n/**\n * Wrapper around the AudioWorkletNode.\n * Sets up the worker and the node to provide a standard interface for\n * processing audio.\n */\n\nclass AudioWorkletAdapter {\n  constructor (controller, name, uri) {\n    this.source = controller.source;\n    this.context = this.source.context;\n    this.name = name;\n    this.uri = uri;\n\n    this.node.then((node) => {\n      node.port.onmessage = function (event) {\n        controller.trigger(event.data.type, event.data);\n      };\n    });\n  }\n\n  get node () {\n    if (this._node) return this._node\n\n    this._node = new Promise((resolve, reject) => {\n      return this.context.audioWorklet.addModule(this.uri).then(() => {\n        return resolve(new AudioWorkletNode(this.context, this.name))\n      }).catch(reject)\n    });\n\n    return this._node\n  }\n\n  message (data) {\n    this.node.then((node) => node.port.postMessage(data));\n  }\n}\n\n/**\n * Wrapper around the ScriptProcessorNode.\n * Sets up the worker and the node to provide a standard interface for\n * processing audio.\n */\n\nclass ScriptProcessorAdapter {\n  constructor (controller, path) {\n    this.source = controller.source;\n    this.context = this.source.context;\n    this.worker = new Worker(path);\n\n    this.node.then((node) => {\n      node.onaudioprocess = (event) => {\n        const channels = [];\n        for (var i = 0; i < this.source.channelCount; i++) {\n          channels[i] = event.inputBuffer.getChannelData(i);\n        }\n        this.worker.postMessage({ type: 'process', input: channels });\n      };\n    });\n\n    this.worker.onmessage = (event) => {\n      controller.trigger(event.data.type, event.data);\n    };\n  }\n\n  get node () {\n    if (this._node) return this._node\n\n    this._node = new Promise((resolve, reject) => {\n      resolve(this._createNode(1024, this.source.channelCount, this.source.channelCount));\n    });\n\n    return this._node\n  }\n\n  message (data) {\n    this.worker.postMessage(data);\n  }\n\n  _createNode () {\n    return (\n      this.context.createScriptProcessor || this.context.createJavaScriptNode\n    ).apply(this.context, arguments)\n  }\n}\n\n/**\n * Adapter for offline analysis.\n * No need to set up scriptProcessorNode or audioWorkletNode. Audio data is\n * already decoded and can just be passed to the worker. `node` is just a\n * placeholder gain node for adapter API parity.\n */\nclass OfflineAdapter {\n  constructor (controller, path) {\n    this.source = controller.source;\n    this.context = this.source.context;\n    this.worker = new Worker(path);\n\n    this.worker.onmessage = (event) => {\n      controller.trigger(event.data.type, event.data);\n    };\n  }\n\n  message (data) {\n    this.worker.postMessage(data);\n  }\n\n  get node () {\n    if (this._node) return this._node\n    this._node = new Promise((resolve, reject) => {\n      resolve(this.context.createGain());\n    });\n    return this._node\n  }\n}\n\n// Disable AudioWorklet because it currently results in glitchy audio playback\nconst audioWorkletEnabled = false;\n\n/**\n * Factory which returns either an OfflineAdapter, AudioWorkletAdapter, or\n * ScriptProcessorAdapter, depending on browser support / mode.\n */\n\nfunction WorkerAdapter ({context, source, controller}) {\n  const adapter = _adapter(controller);\n\n  adapter.node.then((node) => {\n    node.connect(context.destination);\n    source.connect(node);\n  });\n\n  return adapter\n}\n\nfunction _adapter (controller) {\n  if (controller.offline) {\n    return new OfflineAdapter(controller, controller.workerUri)\n  }\n\n  if ('AudioWorkletNode' in window && audioWorkletEnabled) {\n    return new AudioWorkletAdapter(controller, 'needles-worklet', controller.workletUri)\n  } else {\n    return new ScriptProcessorAdapter(controller, controller.workerUri)\n  }\n}\n\nclass InvalidStateError extends Error {\n  constructor (message) {\n    super(message);\n    this.name = 'InvalidStateError';\n  }\n}\n\nconst OfflineAudioContext = window.OfflineAudioContext || window.webkitOfflineAudioContext;\n\nclass Controller {\n  constructor (options) {\n    this.state = 'inactive';\n    this._listeners = {};\n    Object.assign(this, events);\n\n    this.workerUri = options.workerUri;\n    this.workletUri = options.workletUri;\n    this.source = options.source;\n    this.weightedSource = options.weightedSource;\n    this.context = this.source.context;\n    this.offline = this.context instanceof OfflineAudioContext;\n\n    this.workerAdapter.message({\n      type: 'initialize',\n      attributes: {\n        sampleRate: this.context.sampleRate,\n        modes: options.modes\n      },\n    });\n  }\n\n  get workerAdapter () {\n    return this._workerAdapter = this._workerAdapter || new WorkerAdapter({\n      controller: this,\n      context: this.context,\n      source: this.weightedSource || this.source\n    })\n  }\n\n  input (audioBuffer) {\n    const chunkLength = 16384;\n    const audioBufferLength = audioBuffer.length;\n    const channelLength = audioBuffer.numberOfChannels;\n\n    if (this.offline) {\n      this.workerAdapter.message({\n        type: 'set',\n        key: 'duration',\n        value: audioBuffer.duration * 1000\n      });\n    }\n\n    // Refactor to support Safari (where copyFromChannel is unsupported)\n    for (var i = 0; i < audioBufferLength; i += chunkLength) {\n      const block = [];\n      for (var channel = 0; channel < channelLength; channel++) {\n        block[channel] = new Float32Array(chunkLength);\n        audioBuffer.copyFromChannel(block[channel], channel, i);\n      }\n      this.workerAdapter.message({ type: 'process', input: block });\n    }\n  }\n\n  start () {\n    if (this.state !== 'inactive') this._throwInvalidStateErrorFor('start');\n    this.state = 'recording';\n    this.workerAdapter.message({ type: 'record' });\n\n    if (this.offline) {\n      this.source.start();\n      this._startRendering().then(renderedBuffer => this.input(renderedBuffer));\n    }\n  }\n\n  pause () {\n    if (this.state === 'inactive') this._throwInvalidStateErrorFor('pause');\n    this.state = 'paused';\n    this.workerAdapter.message({ type: 'pause' });\n  }\n\n  resume () {\n    if (this.state === 'inactive') this._throwInvalidStateErrorFor('resume');\n    this.state = 'recording';\n    this.workerAdapter.message({ type: 'resume' });\n  }\n\n  stop () {\n    if (this.state === 'inactive') this._throwInvalidStateErrorFor('stop');\n    this.state = 'inactive';\n    this.workerAdapter.message({ type: 'stop' });\n  }\n\n  reset () {\n    this.workerAdapter.message({ type: 'reset' });\n  }\n\n  _startRendering () {\n    return new Promise((resolve, reject) => {\n      this.context.startRendering();\n      this.context.addEventListener('complete', (event) => {\n        resolve(event.renderedBuffer);\n      });\n    })\n  }\n\n  _throwInvalidStateErrorFor (action) {\n    throw new InvalidStateError(`Failed to execute '${action}' on 'Needles': The Needles's state is '${this.state}'.`)\n  }\n}\n\nfunction LoudnessMeter (options) {\n  options.modes = options.modes || [\n    'momentary',\n    'short-term',\n    'integrated'\n  ];\n  const context = options.source.context;\n  const filter1 = preFilter(context);\n  const filter2 = weightingFilter(context);\n  options.source.connect(filter1);\n  filter1.connect(filter2);\n\n  return new Controller({ ...options, weightedSource: filter2 })\n}\n\nexport { LoudnessMeter };\n"], "mappings": "AAAA;;AAEA,SAASA,qBAAqBA,CAAEC,EAAE,EAAE;EAClC,MAAMC,EAAE,GAAG,iBAAiB;EAC5B,MAAMC,EAAE,GAAG,iBAAiB;EAC5B,MAAMC,CAAC,GAAG,kBAAkB;EAC5B,MAAMC,CAAC,GAAGC,IAAI,CAACC,GAAG,CAACD,IAAI,CAACE,EAAE,GAAGL,EAAE,GAAGF,EAAE,CAAC;EAErC,MAAMQ,EAAE,GAAGH,IAAI,CAACI,GAAG,CAAC,EAAE,EAAER,EAAE,GAAG,EAAE,CAAC;EAChC,MAAMS,EAAE,GAAGL,IAAI,CAACI,GAAG,CAACD,EAAE,EAAE,kBAAkB,CAAC;EAE3C,MAAMG,YAAY,GAAG,CAAC,GAAGP,CAAC,GAAGD,CAAC,GAAGC,CAAC,GAAGA,CAAC;EACtC,MAAMQ,YAAY,GAAG,CAAC,IAAIR,CAAC,GAAGA,CAAC,GAAG,CAAC,CAAC,GAAGO,YAAY;EACnD,MAAME,YAAY,GAAG,CAAC,CAAC,GAAGT,CAAC,GAAGD,CAAC,GAAGC,CAAC,GAAGA,CAAC,IAAIO,YAAY;EACvD,MAAMG,UAAU,GAAG,CAACN,EAAE,GAAGE,EAAE,GAAGN,CAAC,GAAGD,CAAC,GAAGC,CAAC,GAAGA,CAAC,IAAIO,YAAY;EAC3D,MAAMI,UAAU,GAAG,CAAC,IAAIX,CAAC,GAAGA,CAAC,GAAGI,EAAE,CAAC,GAAGG,YAAY;EAClD,MAAMK,UAAU,GAAG,CAACR,EAAE,GAAGE,EAAE,GAAGN,CAAC,GAAGD,CAAC,GAAGC,CAAC,GAAGA,CAAC,IAAIO,YAAY;EAE3D,OAAO;IACLM,UAAU,EAAE,CAACH,UAAU,EAAEC,UAAU,EAAEC,UAAU,CAAC;IAChDE,YAAY,EAAE,CAAC,CAAC,EAAEN,YAAY,EAAEC,YAAY;EAC9C,CAAC;AACH;AAEA,SAASM,2BAA2BA,CAAEnB,EAAE,EAAE;EACxC,MAAME,EAAE,GAAG,iBAAiB;EAC5B,MAAMC,CAAC,GAAG,kBAAkB;EAC5B,MAAMC,CAAC,GAAGC,IAAI,CAACC,GAAG,CAACD,IAAI,CAACE,EAAE,GAAGL,EAAE,GAAGF,EAAE,CAAC;EAErC,MAAMY,YAAY,GAAG,CAAC,IAAIR,CAAC,GAAGA,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,GAAGA,CAAC,GAAGD,CAAC,GAAGC,CAAC,GAAGA,CAAC,CAAC;EAC1D,MAAMS,YAAY,GAAG,CAAC,CAAC,GAAGT,CAAC,GAAGD,CAAC,GAAGC,CAAC,GAAGA,CAAC,KAAK,CAAC,GAAGA,CAAC,GAAGD,CAAC,GAAGC,CAAC,GAAGA,CAAC,CAAC;EAC9D,MAAMU,UAAU,GAAG,CAAC;EACpB,MAAMC,UAAU,GAAG,CAAC,CAAC;EACrB,MAAMC,UAAU,GAAG,CAAC;EAEpB,OAAO;IACLC,UAAU,EAAE,CAACH,UAAU,EAAEC,UAAU,EAAEC,UAAU,CAAC;IAChDE,YAAY,EAAE,CAAC,CAAC,EAAEN,YAAY,EAAEC,YAAY;EAC9C,CAAC;AACH;;AAGA;;AAEA,SAASO,SAASA,CAAEC,YAAY,EAAE;EAChC,IAAI,iBAAiB,IAAIA,YAAY,EAAE;IACrC,MAAMC,YAAY,GAAGvB,qBAAqB,CAACsB,YAAY,CAACE,UAAU,CAAC;IACnE,OAAOF,YAAY,CAACG,eAAe,CACjCF,YAAY,CAACL,UAAU,EACvBK,YAAY,CAACJ,YACf,CAAC;EACH,CAAC,MAAM;IACL,MAAMO,MAAM,GAAGJ,YAAY,CAACK,kBAAkB,CAAC,CAAC;IAChDD,MAAM,CAACE,IAAI,GAAG,WAAW;IACzBF,MAAM,CAACG,SAAS,CAACC,KAAK,GAAG,IAAI;IAC7BJ,MAAM,CAACK,IAAI,CAACD,KAAK,GAAG,CAAC;IACrB,OAAOJ,MAAM;EACf;AACF;AAEA,SAASM,eAAeA,CAACV,YAAY,EAAE;EACrC,IAAI,iBAAiB,IAAIA,YAAY,EAAE;IACrC,MAAMC,YAAY,GAAGH,2BAA2B,CAACE,YAAY,CAACE,UAAU,CAAC;IACzE,OAAOF,YAAY,CAACG,eAAe,CACjCF,YAAY,CAACL,UAAU,EACvBK,YAAY,CAACJ,YACf,CAAC;EACH,CAAC,MAAM;IACL,MAAMO,MAAM,GAAGJ,YAAY,CAACK,kBAAkB,CAAC,CAAC;IAChDD,MAAM,CAACE,IAAI,GAAG,UAAU;IACxBF,MAAM,CAACG,SAAS,CAACC,KAAK,GAAG,EAAE;IAC3BJ,MAAM,CAACtB,CAAC,CAAC0B,KAAK,GAAG,CAAC,CAAC;IACnB,OAAOJ,MAAM;EACf;AAEF;AAEA,IAAIO,MAAM,GAAG;EACXC,EAAE,EAAE,SAAAA,CAAUN,IAAI,EAAEO,QAAQ,EAAE;IAC5B,CAAC,IAAI,CAACC,UAAU,CAACR,IAAI,CAAC,GAAG,IAAI,CAACQ,UAAU,CAACR,IAAI,CAAC,IAAI,EAAE,EAAES,IAAI,CAACF,QAAQ,CAAC;EACtE,CAAC;EAEDG,GAAG,EAAE,SAAAA,CAAUV,IAAI,EAAEO,QAAQ,EAAE;IAC7B,IAAI,CAACP,IAAI,EAAE;MACT,IAAI,CAACQ,UAAU,GAAG,CAAC,CAAC;MACpB;IACF;IAEA,IAAID,QAAQ,EAAE;MACZ,IAAI,CAACC,UAAU,CAACR,IAAI,CAAC,GAAG,CAAC,IAAI,CAACQ,UAAU,CAACR,IAAI,CAAC,IAAI,EAAE,EAAEF,MAAM,CAACa,CAAC,IAAIA,CAAC,KAAKJ,QAAQ,CAAC;IACnF,CAAC,MAAM;MACL,OAAO,IAAI,CAACC,UAAU,CAACR,IAAI,CAAC;IAC9B;EACF,CAAC;EAEDY,OAAO,EAAE,SAAAA,CAAUZ,IAAI,EAAEa,IAAI,EAAE;IAC7B,CAAC,IAAI,CAACL,UAAU,CAACR,IAAI,CAAC,IAAI,EAAE,EAAEc,OAAO,CAAEP,QAAQ,IAAK;MAClDA,QAAQ,CAAC;QAAEP,IAAI,EAAEA,IAAI;QAAEa,IAAI,EAAEA;MAAK,CAAC,CAAC;IACtC,CAAC,CAAC;EACJ;AACF,CAAC;;AAED;AACA;AACA;AACA;AACA;;AAEA,MAAME,mBAAmB,CAAC;EACxBC,WAAWA,CAAEC,UAAU,EAAEC,IAAI,EAAEC,GAAG,EAAE;IAClC,IAAI,CAACC,MAAM,GAAGH,UAAU,CAACG,MAAM;IAC/B,IAAI,CAACC,OAAO,GAAG,IAAI,CAACD,MAAM,CAACC,OAAO;IAClC,IAAI,CAACH,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACC,GAAG,GAAGA,GAAG;IAEd,IAAI,CAACG,IAAI,CAACC,IAAI,CAAED,IAAI,IAAK;MACvBA,IAAI,CAACE,IAAI,CAACC,SAAS,GAAG,UAAUC,KAAK,EAAE;QACrCT,UAAU,CAACL,OAAO,CAACc,KAAK,CAACb,IAAI,CAACb,IAAI,EAAE0B,KAAK,CAACb,IAAI,CAAC;MACjD,CAAC;IACH,CAAC,CAAC;EACJ;EAEA,IAAIS,IAAIA,CAAA,EAAI;IACV,IAAI,IAAI,CAACK,KAAK,EAAE,OAAO,IAAI,CAACA,KAAK;IAEjC,IAAI,CAACA,KAAK,GAAG,IAAIC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;MAC5C,OAAO,IAAI,CAACT,OAAO,CAACU,YAAY,CAACC,SAAS,CAAC,IAAI,CAACb,GAAG,CAAC,CAACI,IAAI,CAAC,MAAM;QAC9D,OAAOM,OAAO,CAAC,IAAII,gBAAgB,CAAC,IAAI,CAACZ,OAAO,EAAE,IAAI,CAACH,IAAI,CAAC,CAAC;MAC/D,CAAC,CAAC,CAACgB,KAAK,CAACJ,MAAM,CAAC;IAClB,CAAC,CAAC;IAEF,OAAO,IAAI,CAACH,KAAK;EACnB;EAEAQ,OAAOA,CAAEtB,IAAI,EAAE;IACb,IAAI,CAACS,IAAI,CAACC,IAAI,CAAED,IAAI,IAAKA,IAAI,CAACE,IAAI,CAACY,WAAW,CAACvB,IAAI,CAAC,CAAC;EACvD;AACF;;AAEA;AACA;AACA;AACA;AACA;;AAEA,MAAMwB,sBAAsB,CAAC;EAC3BrB,WAAWA,CAAEC,UAAU,EAAEqB,IAAI,EAAE;IAC7B,IAAI,CAAClB,MAAM,GAAGH,UAAU,CAACG,MAAM;IAC/B,IAAI,CAACC,OAAO,GAAG,IAAI,CAACD,MAAM,CAACC,OAAO;IAClC,IAAI,CAACkB,MAAM,GAAG,IAAIC,MAAM,CAACF,IAAI,CAAC;IAE9B,IAAI,CAAChB,IAAI,CAACC,IAAI,CAAED,IAAI,IAAK;MACvBA,IAAI,CAACmB,cAAc,GAAIf,KAAK,IAAK;QAC/B,MAAMgB,QAAQ,GAAG,EAAE;QACnB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACvB,MAAM,CAACwB,YAAY,EAAED,CAAC,EAAE,EAAE;UACjDD,QAAQ,CAACC,CAAC,CAAC,GAAGjB,KAAK,CAACmB,WAAW,CAACC,cAAc,CAACH,CAAC,CAAC;QACnD;QACA,IAAI,CAACJ,MAAM,CAACH,WAAW,CAAC;UAAEpC,IAAI,EAAE,SAAS;UAAE+C,KAAK,EAAEL;QAAS,CAAC,CAAC;MAC/D,CAAC;IACH,CAAC,CAAC;IAEF,IAAI,CAACH,MAAM,CAACd,SAAS,GAAIC,KAAK,IAAK;MACjCT,UAAU,CAACL,OAAO,CAACc,KAAK,CAACb,IAAI,CAACb,IAAI,EAAE0B,KAAK,CAACb,IAAI,CAAC;IACjD,CAAC;EACH;EAEA,IAAIS,IAAIA,CAAA,EAAI;IACV,IAAI,IAAI,CAACK,KAAK,EAAE,OAAO,IAAI,CAACA,KAAK;IAEjC,IAAI,CAACA,KAAK,GAAG,IAAIC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;MAC5CD,OAAO,CAAC,IAAI,CAACmB,WAAW,CAAC,IAAI,EAAE,IAAI,CAAC5B,MAAM,CAACwB,YAAY,EAAE,IAAI,CAACxB,MAAM,CAACwB,YAAY,CAAC,CAAC;IACrF,CAAC,CAAC;IAEF,OAAO,IAAI,CAACjB,KAAK;EACnB;EAEAQ,OAAOA,CAAEtB,IAAI,EAAE;IACb,IAAI,CAAC0B,MAAM,CAACH,WAAW,CAACvB,IAAI,CAAC;EAC/B;EAEAmC,WAAWA,CAAA,EAAI;IACb,OAAO,CACL,IAAI,CAAC3B,OAAO,CAAC4B,qBAAqB,IAAI,IAAI,CAAC5B,OAAO,CAAC6B,oBAAoB,EACvEC,KAAK,CAAC,IAAI,CAAC9B,OAAO,EAAE+B,SAAS,CAAC;EAClC;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,cAAc,CAAC;EACnBrC,WAAWA,CAAEC,UAAU,EAAEqB,IAAI,EAAE;IAC7B,IAAI,CAAClB,MAAM,GAAGH,UAAU,CAACG,MAAM;IAC/B,IAAI,CAACC,OAAO,GAAG,IAAI,CAACD,MAAM,CAACC,OAAO;IAClC,IAAI,CAACkB,MAAM,GAAG,IAAIC,MAAM,CAACF,IAAI,CAAC;IAE9B,IAAI,CAACC,MAAM,CAACd,SAAS,GAAIC,KAAK,IAAK;MACjCT,UAAU,CAACL,OAAO,CAACc,KAAK,CAACb,IAAI,CAACb,IAAI,EAAE0B,KAAK,CAACb,IAAI,CAAC;IACjD,CAAC;EACH;EAEAsB,OAAOA,CAAEtB,IAAI,EAAE;IACb,IAAI,CAAC0B,MAAM,CAACH,WAAW,CAACvB,IAAI,CAAC;EAC/B;EAEA,IAAIS,IAAIA,CAAA,EAAI;IACV,IAAI,IAAI,CAACK,KAAK,EAAE,OAAO,IAAI,CAACA,KAAK;IACjC,IAAI,CAACA,KAAK,GAAG,IAAIC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;MAC5CD,OAAO,CAAC,IAAI,CAACR,OAAO,CAACiC,UAAU,CAAC,CAAC,CAAC;IACpC,CAAC,CAAC;IACF,OAAO,IAAI,CAAC3B,KAAK;EACnB;AACF;;AAEA;AACA,MAAM4B,mBAAmB,GAAG,KAAK;;AAEjC;AACA;AACA;AACA;;AAEA,SAASC,aAAaA,CAAE;EAACnC,OAAO;EAAED,MAAM;EAAEH;AAAU,CAAC,EAAE;EACrD,MAAMwC,OAAO,GAAGC,QAAQ,CAACzC,UAAU,CAAC;EAEpCwC,OAAO,CAACnC,IAAI,CAACC,IAAI,CAAED,IAAI,IAAK;IAC1BA,IAAI,CAACqC,OAAO,CAACtC,OAAO,CAACuC,WAAW,CAAC;IACjCxC,MAAM,CAACuC,OAAO,CAACrC,IAAI,CAAC;EACtB,CAAC,CAAC;EAEF,OAAOmC,OAAO;AAChB;AAEA,SAASC,QAAQA,CAAEzC,UAAU,EAAE;EAC7B,IAAIA,UAAU,CAAC4C,OAAO,EAAE;IACtB,OAAO,IAAIR,cAAc,CAACpC,UAAU,EAAEA,UAAU,CAAC6C,SAAS,CAAC;EAC7D;EAEA,IAAI,kBAAkB,IAAIC,MAAM,IAAIR,mBAAmB,EAAE;IACvD,OAAO,IAAIxC,mBAAmB,CAACE,UAAU,EAAE,iBAAiB,EAAEA,UAAU,CAAC+C,UAAU,CAAC;EACtF,CAAC,MAAM;IACL,OAAO,IAAI3B,sBAAsB,CAACpB,UAAU,EAAEA,UAAU,CAAC6C,SAAS,CAAC;EACrE;AACF;AAEA,MAAMG,iBAAiB,SAASC,KAAK,CAAC;EACpClD,WAAWA,CAAEmB,OAAO,EAAE;IACpB,KAAK,CAACA,OAAO,CAAC;IACd,IAAI,CAACjB,IAAI,GAAG,mBAAmB;EACjC;AACF;AAEA,MAAMiD,mBAAmB,GAAGJ,MAAM,CAACI,mBAAmB,IAAIJ,MAAM,CAACK,yBAAyB;AAE1F,MAAMC,UAAU,CAAC;EACfrD,WAAWA,CAAEsD,OAAO,EAAE;IACpB,IAAI,CAACC,KAAK,GAAG,UAAU;IACvB,IAAI,CAAC/D,UAAU,GAAG,CAAC,CAAC;IACpBgE,MAAM,CAACC,MAAM,CAAC,IAAI,EAAEpE,MAAM,CAAC;IAE3B,IAAI,CAACyD,SAAS,GAAGQ,OAAO,CAACR,SAAS;IAClC,IAAI,CAACE,UAAU,GAAGM,OAAO,CAACN,UAAU;IACpC,IAAI,CAAC5C,MAAM,GAAGkD,OAAO,CAAClD,MAAM;IAC5B,IAAI,CAACsD,cAAc,GAAGJ,OAAO,CAACI,cAAc;IAC5C,IAAI,CAACrD,OAAO,GAAG,IAAI,CAACD,MAAM,CAACC,OAAO;IAClC,IAAI,CAACwC,OAAO,GAAG,IAAI,CAACxC,OAAO,YAAY8C,mBAAmB;IAE1D,IAAI,CAACQ,aAAa,CAACxC,OAAO,CAAC;MACzBnC,IAAI,EAAE,YAAY;MAClB4E,UAAU,EAAE;QACVhF,UAAU,EAAE,IAAI,CAACyB,OAAO,CAACzB,UAAU;QACnCiF,KAAK,EAAEP,OAAO,CAACO;MACjB;IACF,CAAC,CAAC;EACJ;EAEA,IAAIF,aAAaA,CAAA,EAAI;IACnB,OAAO,IAAI,CAACG,cAAc,GAAG,IAAI,CAACA,cAAc,IAAI,IAAItB,aAAa,CAAC;MACpEvC,UAAU,EAAE,IAAI;MAChBI,OAAO,EAAE,IAAI,CAACA,OAAO;MACrBD,MAAM,EAAE,IAAI,CAACsD,cAAc,IAAI,IAAI,CAACtD;IACtC,CAAC,CAAC;EACJ;EAEA2B,KAAKA,CAAEgC,WAAW,EAAE;IAClB,MAAMC,WAAW,GAAG,KAAK;IACzB,MAAMC,iBAAiB,GAAGF,WAAW,CAACG,MAAM;IAC5C,MAAMC,aAAa,GAAGJ,WAAW,CAACK,gBAAgB;IAElD,IAAI,IAAI,CAACvB,OAAO,EAAE;MAChB,IAAI,CAACc,aAAa,CAACxC,OAAO,CAAC;QACzBnC,IAAI,EAAE,KAAK;QACXqF,GAAG,EAAE,UAAU;QACfnF,KAAK,EAAE6E,WAAW,CAACO,QAAQ,GAAG;MAChC,CAAC,CAAC;IACJ;;IAEA;IACA,KAAK,IAAI3C,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGsC,iBAAiB,EAAEtC,CAAC,IAAIqC,WAAW,EAAE;MACvD,MAAMO,KAAK,GAAG,EAAE;MAChB,KAAK,IAAIC,OAAO,GAAG,CAAC,EAAEA,OAAO,GAAGL,aAAa,EAAEK,OAAO,EAAE,EAAE;QACxDD,KAAK,CAACC,OAAO,CAAC,GAAG,IAAIC,YAAY,CAACT,WAAW,CAAC;QAC9CD,WAAW,CAACW,eAAe,CAACH,KAAK,CAACC,OAAO,CAAC,EAAEA,OAAO,EAAE7C,CAAC,CAAC;MACzD;MACA,IAAI,CAACgC,aAAa,CAACxC,OAAO,CAAC;QAAEnC,IAAI,EAAE,SAAS;QAAE+C,KAAK,EAAEwC;MAAM,CAAC,CAAC;IAC/D;EACF;EAEAI,KAAKA,CAAA,EAAI;IACP,IAAI,IAAI,CAACpB,KAAK,KAAK,UAAU,EAAE,IAAI,CAACqB,0BAA0B,CAAC,OAAO,CAAC;IACvE,IAAI,CAACrB,KAAK,GAAG,WAAW;IACxB,IAAI,CAACI,aAAa,CAACxC,OAAO,CAAC;MAAEnC,IAAI,EAAE;IAAS,CAAC,CAAC;IAE9C,IAAI,IAAI,CAAC6D,OAAO,EAAE;MAChB,IAAI,CAACzC,MAAM,CAACuE,KAAK,CAAC,CAAC;MACnB,IAAI,CAACE,eAAe,CAAC,CAAC,CAACtE,IAAI,CAACuE,cAAc,IAAI,IAAI,CAAC/C,KAAK,CAAC+C,cAAc,CAAC,CAAC;IAC3E;EACF;EAEAC,KAAKA,CAAA,EAAI;IACP,IAAI,IAAI,CAACxB,KAAK,KAAK,UAAU,EAAE,IAAI,CAACqB,0BAA0B,CAAC,OAAO,CAAC;IACvE,IAAI,CAACrB,KAAK,GAAG,QAAQ;IACrB,IAAI,CAACI,aAAa,CAACxC,OAAO,CAAC;MAAEnC,IAAI,EAAE;IAAQ,CAAC,CAAC;EAC/C;EAEAgG,MAAMA,CAAA,EAAI;IACR,IAAI,IAAI,CAACzB,KAAK,KAAK,UAAU,EAAE,IAAI,CAACqB,0BAA0B,CAAC,QAAQ,CAAC;IACxE,IAAI,CAACrB,KAAK,GAAG,WAAW;IACxB,IAAI,CAACI,aAAa,CAACxC,OAAO,CAAC;MAAEnC,IAAI,EAAE;IAAS,CAAC,CAAC;EAChD;EAEAiG,IAAIA,CAAA,EAAI;IACN,IAAI,IAAI,CAAC1B,KAAK,KAAK,UAAU,EAAE,IAAI,CAACqB,0BAA0B,CAAC,MAAM,CAAC;IACtE,IAAI,CAACrB,KAAK,GAAG,UAAU;IACvB,IAAI,CAACI,aAAa,CAACxC,OAAO,CAAC;MAAEnC,IAAI,EAAE;IAAO,CAAC,CAAC;EAC9C;EAEAkG,KAAKA,CAAA,EAAI;IACP,IAAI,CAACvB,aAAa,CAACxC,OAAO,CAAC;MAAEnC,IAAI,EAAE;IAAQ,CAAC,CAAC;EAC/C;EAEA6F,eAAeA,CAAA,EAAI;IACjB,OAAO,IAAIjE,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;MACtC,IAAI,CAACT,OAAO,CAAC8E,cAAc,CAAC,CAAC;MAC7B,IAAI,CAAC9E,OAAO,CAAC+E,gBAAgB,CAAC,UAAU,EAAG1E,KAAK,IAAK;QACnDG,OAAO,CAACH,KAAK,CAACoE,cAAc,CAAC;MAC/B,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ;EAEAF,0BAA0BA,CAAES,MAAM,EAAE;IAClC,MAAM,IAAIpC,iBAAiB,CAAC,sBAAsBoC,MAAM,2CAA2C,IAAI,CAAC9B,KAAK,IAAI,CAAC;EACpH;AACF;AAEA,SAAS+B,aAAaA,CAAEhC,OAAO,EAAE;EAC/BA,OAAO,CAACO,KAAK,GAAGP,OAAO,CAACO,KAAK,IAAI,CAC/B,WAAW,EACX,YAAY,EACZ,YAAY,CACb;EACD,MAAMxD,OAAO,GAAGiD,OAAO,CAAClD,MAAM,CAACC,OAAO;EACtC,MAAMkF,OAAO,GAAG9G,SAAS,CAAC4B,OAAO,CAAC;EAClC,MAAMmF,OAAO,GAAGpG,eAAe,CAACiB,OAAO,CAAC;EACxCiD,OAAO,CAAClD,MAAM,CAACuC,OAAO,CAAC4C,OAAO,CAAC;EAC/BA,OAAO,CAAC5C,OAAO,CAAC6C,OAAO,CAAC;EAExB,OAAO,IAAInC,UAAU,CAAC;IAAE,GAAGC,OAAO;IAAEI,cAAc,EAAE8B;EAAQ,CAAC,CAAC;AAChE;AAEA,SAASF,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}