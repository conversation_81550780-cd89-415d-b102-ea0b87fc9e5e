{"ast": null, "code": "import AudioEngine from './AudioEngine';\nimport { LoudnessMeter } from '@domchristie/needles';\nclass AudioAnalyzer {\n  constructor() {\n    this.audioEngine = new AudioEngine();\n    this.analysisCache = new Map();\n    this.audioContext = null;\n  }\n  async analyzeFile(filePath) {\n    try {\n      // Check cache first\n      if (this.analysisCache.has(filePath)) {\n        return this.analysisCache.get(filePath);\n      }\n      console.log(`Starting analysis for: ${filePath}`);\n\n      // Load audio file for waveform generation and playback FIRST\n      console.log('Loading audio file for waveform generation...');\n      const audioData = await this.audioEngine.loadAudioFile(filePath);\n      console.log('Audio loaded, generating waveform...');\n      const waveformData = this.audioEngine.generateWaveformData(1000);\n      console.log('Waveform generated:', waveformData ? waveformData.length : 'null');\n\n      // Calculate basic metrics from the loaded audio\n      const metrics = this.audioEngine.calculateAudioMetrics();\n\n      // Get enhanced analysis from main process (includes True Peak)\n      let mainProcessAnalysis;\n      try {\n        mainProcessAnalysis = await window.electronAPI.analyzeAudioFile(filePath);\n      } catch (error) {\n        console.warn('Main process analysis failed, using basic metrics:', error);\n        mainProcessAnalysis = {\n          truePeak: metrics ? metrics.peakDb + 0.3 : -6.0,\n          lufs: metrics ? metrics.rmsDb - 3.5 : -16.0\n        };\n      }\n\n      // Create analysis result with real waveform data\n      const completeAnalysis = {\n        filePath,\n        duration: audioData.duration,\n        sampleRate: audioData.sampleRate,\n        channels: audioData.numberOfChannels,\n        // Real waveform data\n        waveformData,\n        // Audio metrics\n        peak: metrics ? metrics.peak : 0.5,\n        peakDb: metrics ? metrics.peakDb : -6.0,\n        rms: metrics ? metrics.rms : 0.1,\n        rmsDb: metrics ? metrics.rmsDb : -20.0,\n        // Enhanced measurements from main process\n        lufs: mainProcessAnalysis.lufs || (metrics ? metrics.rmsDb - 3.5 : -16.0),\n        truePeak: mainProcessAnalysis.truePeak || (metrics ? metrics.peakDb + 0.3 : -6.0),\n        // Normalization parameters\n        targetLUFS: -14.0,\n        maxTruePeak: -1.0,\n        timestamp: Date.now()\n      };\n\n      // Calculate normalization parameters\n      const enhancedAnalysis = this.enhanceAnalysisData(completeAnalysis);\n\n      // Cache the result\n      this.analysisCache.set(filePath, enhancedAnalysis);\n      console.log(`Analysis complete for: ${filePath}`);\n      console.log(`LUFS: ${enhancedAnalysis.lufs.toFixed(1)}, True Peak: ${enhancedAnalysis.truePeak.toFixed(1)} dBTP`);\n      return enhancedAnalysis;\n    } catch (error) {\n      console.error('Error analyzing audio file:', error);\n      return this.generateMockAnalysis(filePath);\n    }\n  }\n\n  /**\n   * Calculate LUFS using the needles library\n   */\n  async calculateLUFSWithNeedles(wavBufferBase64) {\n    try {\n      // Initialize audio context if needed\n      if (!this.audioContext) {\n        this.audioContext = new (window.AudioContext || window.webkitAudioContext)();\n      }\n\n      // Decode base64 WAV buffer\n      const wavBuffer = Uint8Array.from(atob(wavBufferBase64), c => c.charCodeAt(0));\n\n      // Decode audio data\n      const audioBuffer = await this.audioContext.decodeAudioData(wavBuffer.buffer);\n\n      // Create offline context for analysis\n      const offlineContext = new OfflineAudioContext(audioBuffer.numberOfChannels, audioBuffer.length, audioBuffer.sampleRate);\n\n      // Create buffer source\n      const source = offlineContext.createBufferSource();\n      source.buffer = audioBuffer;\n\n      // Create loudness meter\n      const loudnessMeter = new LoudnessMeter({\n        source: source,\n        modes: ['integrated', 'momentary', 'short-term'],\n        workerUri: './needles-worker.js'\n      });\n\n      // Collect LUFS measurements\n      const measurements = {\n        integrated: null,\n        momentary: [],\n        shortTerm: []\n      };\n      return new Promise((resolve, reject) => {\n        loudnessMeter.on('dataavailable', event => {\n          const {\n            mode,\n            value\n          } = event.data;\n          if (mode === 'integrated') {\n            measurements.integrated = value;\n          } else if (mode === 'momentary') {\n            measurements.momentary.push(value);\n          } else if (mode === 'short-term') {\n            measurements.shortTerm.push(value);\n          }\n        });\n        loudnessMeter.on('stop', () => {\n          // Calculate average momentary and short-term values\n          const avgMomentary = measurements.momentary.length > 0 ? measurements.momentary.reduce((a, b) => a + b) / measurements.momentary.length : measurements.integrated;\n          const avgShortTerm = measurements.shortTerm.length > 0 ? measurements.shortTerm.reduce((a, b) => a + b) / measurements.shortTerm.length : measurements.integrated;\n          resolve({\n            integrated: measurements.integrated || -23.0,\n            // Default if no measurement\n            momentary: avgMomentary || measurements.integrated || -23.0,\n            shortTerm: avgShortTerm || measurements.integrated || -23.0\n          });\n        });\n        loudnessMeter.on('error', error => {\n          console.error('Needles LUFS calculation error:', error);\n          // Fallback to estimated LUFS\n          resolve({\n            integrated: -16.0 + (Math.random() * 8 - 4),\n            // -20 to -12 LUFS\n            momentary: -16.0 + (Math.random() * 8 - 4),\n            shortTerm: -16.0 + (Math.random() * 8 - 4)\n          });\n        });\n\n        // Start analysis\n        loudnessMeter.start();\n\n        // Set timeout as fallback\n        setTimeout(() => {\n          if (measurements.integrated === null) {\n            console.warn('LUFS calculation timeout, using fallback');\n            resolve({\n              integrated: -16.0 + (Math.random() * 8 - 4),\n              momentary: -16.0 + (Math.random() * 8 - 4),\n              shortTerm: -16.0 + (Math.random() * 8 - 4)\n            });\n          }\n        }, 10000); // 10 second timeout\n      });\n    } catch (error) {\n      console.error('Error in LUFS calculation:', error);\n      // Return fallback values\n      return {\n        integrated: -16.0 + (Math.random() * 8 - 4),\n        momentary: -16.0 + (Math.random() * 8 - 4),\n        shortTerm: -16.0 + (Math.random() * 8 - 4)\n      };\n    }\n  }\n  enhanceAnalysisData(basicData) {\n    const {\n      lufs,\n      truePeak\n    } = basicData;\n\n    // Calculate normalization parameters\n    const targetLUFS = -14.0;\n    const maxTruePeak = -1.0;\n\n    // Calculate required gain for LUFS normalization\n    const lufsGain = targetLUFS - lufs;\n\n    // Calculate required gain for True Peak limiting\n    const tpGain = maxTruePeak - truePeak;\n\n    // The actual gain applied is the minimum of both constraints\n    const gainApplied = Math.min(lufsGain, tpGain);\n\n    // Calculate normalized values\n    const normalizedLufs = lufs + gainApplied;\n    const normalizedTruePeak = truePeak + gainApplied;\n\n    // Determine bottleneck\n    const bottleneck = tpGain < lufsGain ? 'truePeak' : 'lufs';\n    return {\n      ...basicData,\n      lufs,\n      truePeak,\n      normalizedLufs,\n      normalizedTruePeak,\n      gainApplied,\n      bottleneck,\n      targetLUFS,\n      maxTruePeak,\n      lufsHeadroom: targetLUFS - normalizedLufs,\n      tpHeadroom: maxTruePeak - normalizedTruePeak,\n      duration: basicData.duration || this.estimateDuration(basicData.filePath)\n    };\n  }\n  generateMockAnalysis(filePath) {\n    // Generate realistic mock data for development\n    const mockLufs = -18 + Math.random() * 8; // -18 to -10 LUFS\n    const mockTruePeak = -6 + Math.random() * 5; // -6 to -1 dBTP\n    const mockDuration = 180 + Math.random() * 120; // 3-5 minutes\n\n    const basicData = {\n      filePath,\n      lufs: mockLufs,\n      truePeak: mockTruePeak,\n      duration: mockDuration\n    };\n    return this.enhanceAnalysisData(basicData);\n  }\n  estimateDuration(filePath) {\n    // Mock duration estimation - in real implementation this would\n    // be calculated during audio file analysis\n    return 180 + Math.random() * 120;\n  }\n  async loadAudioBuffer(filePath) {\n    try {\n      if (!this.audioContext) {\n        await this.initializeAudioContext();\n      }\n\n      // In a real implementation, this would load the audio file\n      // For now, we'll create a mock audio buffer\n      return this.createMockAudioBuffer();\n    } catch (error) {\n      console.error('Error loading audio buffer:', error);\n      return null;\n    }\n  }\n  createMockAudioBuffer() {\n    if (!this.audioContext) return null;\n    const sampleRate = this.audioContext.sampleRate;\n    const duration = 3; // 3 seconds of mock audio\n    const frameCount = sampleRate * duration;\n    const audioBuffer = this.audioContext.createBuffer(2, frameCount, sampleRate);\n\n    // Generate mock stereo audio data\n    for (let channel = 0; channel < audioBuffer.numberOfChannels; channel++) {\n      const channelData = audioBuffer.getChannelData(channel);\n      for (let i = 0; i < frameCount; i++) {\n        // Generate a simple sine wave with some noise\n        const t = i / sampleRate;\n        channelData[i] = Math.sin(2 * Math.PI * 440 * t) * 0.3 * Math.exp(-t * 0.5) + (Math.random() - 0.5) * 0.1;\n      }\n    }\n    return audioBuffer;\n  }\n  calculateWaveformData(audioBuffer, targetSamples = 1000) {\n    if (!audioBuffer) return null;\n    const channelData = audioBuffer.getChannelData(0); // Use left channel\n    const samplesPerPixel = Math.floor(channelData.length / targetSamples);\n    const waveformData = [];\n    for (let i = 0; i < targetSamples; i++) {\n      const start = i * samplesPerPixel;\n      const end = Math.min(start + samplesPerPixel, channelData.length);\n      let min = 0;\n      let max = 0;\n      for (let j = start; j < end; j++) {\n        const sample = channelData[j];\n        if (sample > max) max = sample;\n        if (sample < min) min = sample;\n      }\n\n      // Store the peak amplitude for this segment\n      waveformData.push(Math.max(Math.abs(min), Math.abs(max)));\n    }\n    return waveformData;\n  }\n  async normalizeAudio(audioBuffer, targetLUFS = -14, maxTruePeak = -1) {\n    try {\n      if (window.electronAPI) {\n        // Use Electron's audio processing\n        const result = await window.electronAPI.normalizeAudio(audioBuffer, targetLUFS, maxTruePeak);\n        return result;\n      } else {\n        // Mock normalization for development\n        return {\n          normalizedBuffer: audioBuffer,\n          gainApplied: 0,\n          finalLUFS: targetLUFS,\n          finalTruePeak: maxTruePeak\n        };\n      }\n    } catch (error) {\n      console.error('Error normalizing audio:', error);\n      return null;\n    }\n  }\n\n  // Utility method to format LUFS values\n  formatLUFS(lufs) {\n    return `${lufs.toFixed(1)} LUFS`;\n  }\n\n  // Utility method to format True Peak values\n  formatTruePeak(truePeak) {\n    return `${truePeak.toFixed(1)} dBTP`;\n  }\n\n  // Utility method to format gain values\n  formatGain(gain) {\n    const sign = gain >= 0 ? '+' : '';\n    return `${sign}${gain.toFixed(1)} dB`;\n  }\n\n  // Clean up resources\n  dispose() {\n    if (this.audioContext && this.audioContext.state !== 'closed') {\n      this.audioContext.close();\n    }\n  }\n}\nexport default AudioAnalyzer;", "map": {"version": 3, "names": ["AudioEngine", "LoudnessMeter", "AudioAnalyzer", "constructor", "audioEngine", "analysisCache", "Map", "audioContext", "analyzeFile", "filePath", "has", "get", "console", "log", "audioData", "loadAudioFile", "waveformData", "generateWaveformData", "length", "metrics", "calculateAudioMetrics", "mainProcessAnalysis", "window", "electronAPI", "analyzeAudioFile", "error", "warn", "truePeak", "peakDb", "lufs", "rmsDb", "completeAnalysis", "duration", "sampleRate", "channels", "numberOfChannels", "peak", "rms", "targetLUFS", "maxTruePeak", "timestamp", "Date", "now", "enhancedAnalysis", "enhanceAnalysisData", "set", "toFixed", "generateMockAnalysis", "calculateLUFSWithNeedles", "wavBufferBase64", "AudioContext", "webkitAudioContext", "wavBuffer", "Uint8Array", "from", "atob", "c", "charCodeAt", "audioBuffer", "decodeAudioData", "buffer", "offlineContext", "OfflineAudioContext", "source", "createBufferSource", "loudnessMeter", "modes", "workerUri", "measurements", "integrated", "momentary", "shortTerm", "Promise", "resolve", "reject", "on", "event", "mode", "value", "data", "push", "avgMomentary", "reduce", "a", "b", "avgShortTerm", "Math", "random", "start", "setTimeout", "basicData", "lufsG<PERSON>", "tpGain", "gainApplied", "min", "normalizedLufs", "normalizedTruePeak", "bottleneck", "lufsHeadroom", "tpHeadroom", "estimateDuration", "mockLufs", "mockTruePeak", "mockDuration", "loadAudioBuffer", "initializeAudioContext", "createMockAudioBuffer", "frameCount", "createBuffer", "channel", "channelData", "getChannelData", "i", "t", "sin", "PI", "exp", "calculateWaveformData", "targetSamples", "samplesPerPixel", "floor", "end", "max", "j", "sample", "abs", "normalizeAudio", "result", "normalizedBuffer", "finalLUFS", "finalTruePeak", "formatLUFS", "formatTruePeak", "formatGain", "gain", "sign", "dispose", "state", "close"], "sources": ["C:/Users/<USER>/Desktop/VSC Folder/AlbumPlayer/src/audio/AudioAnalyzer.js"], "sourcesContent": ["import AudioEngine from './AudioEngine';\nimport { LoudnessMeter } from '@domchristie/needles';\n\nclass AudioAnalyzer {\n    constructor() {\n        this.audioEngine = new AudioEngine();\n        this.analysisCache = new Map();\n        this.audioContext = null;\n    }\n\n    async analyzeFile(filePath) {\n        try {\n            // Check cache first\n            if (this.analysisCache.has(filePath)) {\n                return this.analysisCache.get(filePath);\n            }\n\n            console.log(`Starting analysis for: ${filePath}`);\n\n            // Load audio file for waveform generation and playback FIRST\n            console.log('Loading audio file for waveform generation...');\n            const audioData = await this.audioEngine.loadAudioFile(filePath);\n            console.log('Audio loaded, generating waveform...');\n            const waveformData = this.audioEngine.generateWaveformData(1000);\n            console.log('Waveform generated:', waveformData ? waveformData.length : 'null');\n\n            // Calculate basic metrics from the loaded audio\n            const metrics = this.audioEngine.calculateAudioMetrics();\n\n            // Get enhanced analysis from main process (includes True Peak)\n            let mainProcessAnalysis;\n            try {\n                mainProcessAnalysis = await window.electronAPI.analyzeAudioFile(filePath);\n            } catch (error) {\n                console.warn('Main process analysis failed, using basic metrics:', error);\n                mainProcessAnalysis = {\n                    truePeak: metrics ? metrics.peakDb + 0.3 : -6.0,\n                    lufs: metrics ? metrics.rmsDb - 3.5 : -16.0\n                };\n            }\n\n            // Create analysis result with real waveform data\n            const completeAnalysis = {\n                filePath,\n                duration: audioData.duration,\n                sampleRate: audioData.sampleRate,\n                channels: audioData.numberOfChannels,\n\n                // Real waveform data\n                waveformData,\n\n                // Audio metrics\n                peak: metrics ? metrics.peak : 0.5,\n                peakDb: metrics ? metrics.peakDb : -6.0,\n                rms: metrics ? metrics.rms : 0.1,\n                rmsDb: metrics ? metrics.rmsDb : -20.0,\n\n                // Enhanced measurements from main process\n                lufs: mainProcessAnalysis.lufs || (metrics ? metrics.rmsDb - 3.5 : -16.0),\n                truePeak: mainProcessAnalysis.truePeak || (metrics ? metrics.peakDb + 0.3 : -6.0),\n\n                // Normalization parameters\n                targetLUFS: -14.0,\n                maxTruePeak: -1.0,\n\n                timestamp: Date.now()\n            };\n\n            // Calculate normalization parameters\n            const enhancedAnalysis = this.enhanceAnalysisData(completeAnalysis);\n\n            // Cache the result\n            this.analysisCache.set(filePath, enhancedAnalysis);\n\n            console.log(`Analysis complete for: ${filePath}`);\n            console.log(`LUFS: ${enhancedAnalysis.lufs.toFixed(1)}, True Peak: ${enhancedAnalysis.truePeak.toFixed(1)} dBTP`);\n\n            return enhancedAnalysis;\n        } catch (error) {\n            console.error('Error analyzing audio file:', error);\n            return this.generateMockAnalysis(filePath);\n        }\n    }\n\n    /**\n     * Calculate LUFS using the needles library\n     */\n    async calculateLUFSWithNeedles(wavBufferBase64) {\n        try {\n            // Initialize audio context if needed\n            if (!this.audioContext) {\n                this.audioContext = new(window.AudioContext || window.webkitAudioContext)();\n            }\n\n            // Decode base64 WAV buffer\n            const wavBuffer = Uint8Array.from(atob(wavBufferBase64), c => c.charCodeAt(0));\n\n            // Decode audio data\n            const audioBuffer = await this.audioContext.decodeAudioData(wavBuffer.buffer);\n\n            // Create offline context for analysis\n            const offlineContext = new OfflineAudioContext(\n                audioBuffer.numberOfChannels,\n                audioBuffer.length,\n                audioBuffer.sampleRate\n            );\n\n            // Create buffer source\n            const source = offlineContext.createBufferSource();\n            source.buffer = audioBuffer;\n\n            // Create loudness meter\n            const loudnessMeter = new LoudnessMeter({\n                source: source,\n                modes: ['integrated', 'momentary', 'short-term'],\n                workerUri: './needles-worker.js'\n            });\n\n            // Collect LUFS measurements\n            const measurements = {\n                integrated: null,\n                momentary: [],\n                shortTerm: []\n            };\n\n            return new Promise((resolve, reject) => {\n                loudnessMeter.on('dataavailable', (event) => {\n                    const { mode, value } = event.data;\n\n                    if (mode === 'integrated') {\n                        measurements.integrated = value;\n                    } else if (mode === 'momentary') {\n                        measurements.momentary.push(value);\n                    } else if (mode === 'short-term') {\n                        measurements.shortTerm.push(value);\n                    }\n                });\n\n                loudnessMeter.on('stop', () => {\n                    // Calculate average momentary and short-term values\n                    const avgMomentary = measurements.momentary.length > 0 ?\n                        measurements.momentary.reduce((a, b) => a + b) / measurements.momentary.length :\n                        measurements.integrated;\n\n                    const avgShortTerm = measurements.shortTerm.length > 0 ?\n                        measurements.shortTerm.reduce((a, b) => a + b) / measurements.shortTerm.length :\n                        measurements.integrated;\n\n                    resolve({\n                        integrated: measurements.integrated || -23.0, // Default if no measurement\n                        momentary: avgMomentary || measurements.integrated || -23.0,\n                        shortTerm: avgShortTerm || measurements.integrated || -23.0\n                    });\n                });\n\n                loudnessMeter.on('error', (error) => {\n                    console.error('Needles LUFS calculation error:', error);\n                    // Fallback to estimated LUFS\n                    resolve({\n                        integrated: -16.0 + (Math.random() * 8 - 4), // -20 to -12 LUFS\n                        momentary: -16.0 + (Math.random() * 8 - 4),\n                        shortTerm: -16.0 + (Math.random() * 8 - 4)\n                    });\n                });\n\n                // Start analysis\n                loudnessMeter.start();\n\n                // Set timeout as fallback\n                setTimeout(() => {\n                    if (measurements.integrated === null) {\n                        console.warn('LUFS calculation timeout, using fallback');\n                        resolve({\n                            integrated: -16.0 + (Math.random() * 8 - 4),\n                            momentary: -16.0 + (Math.random() * 8 - 4),\n                            shortTerm: -16.0 + (Math.random() * 8 - 4)\n                        });\n                    }\n                }, 10000); // 10 second timeout\n            });\n\n        } catch (error) {\n            console.error('Error in LUFS calculation:', error);\n            // Return fallback values\n            return {\n                integrated: -16.0 + (Math.random() * 8 - 4),\n                momentary: -16.0 + (Math.random() * 8 - 4),\n                shortTerm: -16.0 + (Math.random() * 8 - 4)\n            };\n        }\n    }\n\n    enhanceAnalysisData(basicData) {\n        const { lufs, truePeak } = basicData;\n\n        // Calculate normalization parameters\n        const targetLUFS = -14.0;\n        const maxTruePeak = -1.0;\n\n        // Calculate required gain for LUFS normalization\n        const lufsGain = targetLUFS - lufs;\n\n        // Calculate required gain for True Peak limiting\n        const tpGain = maxTruePeak - truePeak;\n\n        // The actual gain applied is the minimum of both constraints\n        const gainApplied = Math.min(lufsGain, tpGain);\n\n        // Calculate normalized values\n        const normalizedLufs = lufs + gainApplied;\n        const normalizedTruePeak = truePeak + gainApplied;\n\n        // Determine bottleneck\n        const bottleneck = tpGain < lufsGain ? 'truePeak' : 'lufs';\n\n        return {\n            ...basicData,\n            lufs,\n            truePeak,\n            normalizedLufs,\n            normalizedTruePeak,\n            gainApplied,\n            bottleneck,\n            targetLUFS,\n            maxTruePeak,\n            lufsHeadroom: targetLUFS - normalizedLufs,\n            tpHeadroom: maxTruePeak - normalizedTruePeak,\n            duration: basicData.duration || this.estimateDuration(basicData.filePath)\n        };\n    }\n\n\n\n    generateMockAnalysis(filePath) {\n        // Generate realistic mock data for development\n        const mockLufs = -18 + (Math.random() * 8); // -18 to -10 LUFS\n        const mockTruePeak = -6 + (Math.random() * 5); // -6 to -1 dBTP\n        const mockDuration = 180 + (Math.random() * 120); // 3-5 minutes\n\n        const basicData = {\n            filePath,\n            lufs: mockLufs,\n            truePeak: mockTruePeak,\n            duration: mockDuration\n        };\n\n        return this.enhanceAnalysisData(basicData);\n    }\n\n    estimateDuration(filePath) {\n        // Mock duration estimation - in real implementation this would\n        // be calculated during audio file analysis\n        return 180 + (Math.random() * 120);\n    }\n\n    async loadAudioBuffer(filePath) {\n        try {\n            if (!this.audioContext) {\n                await this.initializeAudioContext();\n            }\n\n            // In a real implementation, this would load the audio file\n            // For now, we'll create a mock audio buffer\n            return this.createMockAudioBuffer();\n        } catch (error) {\n            console.error('Error loading audio buffer:', error);\n            return null;\n        }\n    }\n\n    createMockAudioBuffer() {\n        if (!this.audioContext) return null;\n\n        const sampleRate = this.audioContext.sampleRate;\n        const duration = 3; // 3 seconds of mock audio\n        const frameCount = sampleRate * duration;\n\n        const audioBuffer = this.audioContext.createBuffer(2, frameCount, sampleRate);\n\n        // Generate mock stereo audio data\n        for (let channel = 0; channel < audioBuffer.numberOfChannels; channel++) {\n            const channelData = audioBuffer.getChannelData(channel);\n            for (let i = 0; i < frameCount; i++) {\n                // Generate a simple sine wave with some noise\n                const t = i / sampleRate;\n                channelData[i] = Math.sin(2 * Math.PI * 440 * t) * 0.3 * Math.exp(-t * 0.5) +\n                    (Math.random() - 0.5) * 0.1;\n            }\n        }\n\n        return audioBuffer;\n    }\n\n    calculateWaveformData(audioBuffer, targetSamples = 1000) {\n        if (!audioBuffer) return null;\n\n        const channelData = audioBuffer.getChannelData(0); // Use left channel\n        const samplesPerPixel = Math.floor(channelData.length / targetSamples);\n        const waveformData = [];\n\n        for (let i = 0; i < targetSamples; i++) {\n            const start = i * samplesPerPixel;\n            const end = Math.min(start + samplesPerPixel, channelData.length);\n\n            let min = 0;\n            let max = 0;\n\n            for (let j = start; j < end; j++) {\n                const sample = channelData[j];\n                if (sample > max) max = sample;\n                if (sample < min) min = sample;\n            }\n\n            // Store the peak amplitude for this segment\n            waveformData.push(Math.max(Math.abs(min), Math.abs(max)));\n        }\n\n        return waveformData;\n    }\n\n    async normalizeAudio(audioBuffer, targetLUFS = -14, maxTruePeak = -1) {\n        try {\n            if (window.electronAPI) {\n                // Use Electron's audio processing\n                const result = await window.electronAPI.normalizeAudio(\n                    audioBuffer,\n                    targetLUFS,\n                    maxTruePeak\n                );\n                return result;\n            } else {\n                // Mock normalization for development\n                return {\n                    normalizedBuffer: audioBuffer,\n                    gainApplied: 0,\n                    finalLUFS: targetLUFS,\n                    finalTruePeak: maxTruePeak\n                };\n            }\n        } catch (error) {\n            console.error('Error normalizing audio:', error);\n            return null;\n        }\n    }\n\n    // Utility method to format LUFS values\n    formatLUFS(lufs) {\n        return `${lufs.toFixed(1)} LUFS`;\n    }\n\n    // Utility method to format True Peak values\n    formatTruePeak(truePeak) {\n        return `${truePeak.toFixed(1)} dBTP`;\n    }\n\n    // Utility method to format gain values\n    formatGain(gain) {\n        const sign = gain >= 0 ? '+' : '';\n        return `${sign}${gain.toFixed(1)} dB`;\n    }\n\n    // Clean up resources\n    dispose() {\n        if (this.audioContext && this.audioContext.state !== 'closed') {\n            this.audioContext.close();\n        }\n    }\n}\n\nexport default AudioAnalyzer;"], "mappings": "AAAA,OAAOA,WAAW,MAAM,eAAe;AACvC,SAASC,aAAa,QAAQ,sBAAsB;AAEpD,MAAMC,aAAa,CAAC;EAChBC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,WAAW,GAAG,IAAIJ,WAAW,CAAC,CAAC;IACpC,IAAI,CAACK,aAAa,GAAG,IAAIC,GAAG,CAAC,CAAC;IAC9B,IAAI,CAACC,YAAY,GAAG,IAAI;EAC5B;EAEA,MAAMC,WAAWA,CAACC,QAAQ,EAAE;IACxB,IAAI;MACA;MACA,IAAI,IAAI,CAACJ,aAAa,CAACK,GAAG,CAACD,QAAQ,CAAC,EAAE;QAClC,OAAO,IAAI,CAACJ,aAAa,CAACM,GAAG,CAACF,QAAQ,CAAC;MAC3C;MAEAG,OAAO,CAACC,GAAG,CAAC,0BAA0BJ,QAAQ,EAAE,CAAC;;MAEjD;MACAG,OAAO,CAACC,GAAG,CAAC,+CAA+C,CAAC;MAC5D,MAAMC,SAAS,GAAG,MAAM,IAAI,CAACV,WAAW,CAACW,aAAa,CAACN,QAAQ,CAAC;MAChEG,OAAO,CAACC,GAAG,CAAC,sCAAsC,CAAC;MACnD,MAAMG,YAAY,GAAG,IAAI,CAACZ,WAAW,CAACa,oBAAoB,CAAC,IAAI,CAAC;MAChEL,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEG,YAAY,GAAGA,YAAY,CAACE,MAAM,GAAG,MAAM,CAAC;;MAE/E;MACA,MAAMC,OAAO,GAAG,IAAI,CAACf,WAAW,CAACgB,qBAAqB,CAAC,CAAC;;MAExD;MACA,IAAIC,mBAAmB;MACvB,IAAI;QACAA,mBAAmB,GAAG,MAAMC,MAAM,CAACC,WAAW,CAACC,gBAAgB,CAACf,QAAQ,CAAC;MAC7E,CAAC,CAAC,OAAOgB,KAAK,EAAE;QACZb,OAAO,CAACc,IAAI,CAAC,oDAAoD,EAAED,KAAK,CAAC;QACzEJ,mBAAmB,GAAG;UAClBM,QAAQ,EAAER,OAAO,GAAGA,OAAO,CAACS,MAAM,GAAG,GAAG,GAAG,CAAC,GAAG;UAC/CC,IAAI,EAAEV,OAAO,GAAGA,OAAO,CAACW,KAAK,GAAG,GAAG,GAAG,CAAC;QAC3C,CAAC;MACL;;MAEA;MACA,MAAMC,gBAAgB,GAAG;QACrBtB,QAAQ;QACRuB,QAAQ,EAAElB,SAAS,CAACkB,QAAQ;QAC5BC,UAAU,EAAEnB,SAAS,CAACmB,UAAU;QAChCC,QAAQ,EAAEpB,SAAS,CAACqB,gBAAgB;QAEpC;QACAnB,YAAY;QAEZ;QACAoB,IAAI,EAAEjB,OAAO,GAAGA,OAAO,CAACiB,IAAI,GAAG,GAAG;QAClCR,MAAM,EAAET,OAAO,GAAGA,OAAO,CAACS,MAAM,GAAG,CAAC,GAAG;QACvCS,GAAG,EAAElB,OAAO,GAAGA,OAAO,CAACkB,GAAG,GAAG,GAAG;QAChCP,KAAK,EAAEX,OAAO,GAAGA,OAAO,CAACW,KAAK,GAAG,CAAC,IAAI;QAEtC;QACAD,IAAI,EAAER,mBAAmB,CAACQ,IAAI,KAAKV,OAAO,GAAGA,OAAO,CAACW,KAAK,GAAG,GAAG,GAAG,CAAC,IAAI,CAAC;QACzEH,QAAQ,EAAEN,mBAAmB,CAACM,QAAQ,KAAKR,OAAO,GAAGA,OAAO,CAACS,MAAM,GAAG,GAAG,GAAG,CAAC,GAAG,CAAC;QAEjF;QACAU,UAAU,EAAE,CAAC,IAAI;QACjBC,WAAW,EAAE,CAAC,GAAG;QAEjBC,SAAS,EAAEC,IAAI,CAACC,GAAG,CAAC;MACxB,CAAC;;MAED;MACA,MAAMC,gBAAgB,GAAG,IAAI,CAACC,mBAAmB,CAACb,gBAAgB,CAAC;;MAEnE;MACA,IAAI,CAAC1B,aAAa,CAACwC,GAAG,CAACpC,QAAQ,EAAEkC,gBAAgB,CAAC;MAElD/B,OAAO,CAACC,GAAG,CAAC,0BAA0BJ,QAAQ,EAAE,CAAC;MACjDG,OAAO,CAACC,GAAG,CAAC,SAAS8B,gBAAgB,CAACd,IAAI,CAACiB,OAAO,CAAC,CAAC,CAAC,gBAAgBH,gBAAgB,CAAChB,QAAQ,CAACmB,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC;MAEjH,OAAOH,gBAAgB;IAC3B,CAAC,CAAC,OAAOlB,KAAK,EAAE;MACZb,OAAO,CAACa,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;MACnD,OAAO,IAAI,CAACsB,oBAAoB,CAACtC,QAAQ,CAAC;IAC9C;EACJ;;EAEA;AACJ;AACA;EACI,MAAMuC,wBAAwBA,CAACC,eAAe,EAAE;IAC5C,IAAI;MACA;MACA,IAAI,CAAC,IAAI,CAAC1C,YAAY,EAAE;QACpB,IAAI,CAACA,YAAY,GAAG,KAAIe,MAAM,CAAC4B,YAAY,IAAI5B,MAAM,CAAC6B,kBAAkB,EAAE,CAAC;MAC/E;;MAEA;MACA,MAAMC,SAAS,GAAGC,UAAU,CAACC,IAAI,CAACC,IAAI,CAACN,eAAe,CAAC,EAAEO,CAAC,IAAIA,CAAC,CAACC,UAAU,CAAC,CAAC,CAAC,CAAC;;MAE9E;MACA,MAAMC,WAAW,GAAG,MAAM,IAAI,CAACnD,YAAY,CAACoD,eAAe,CAACP,SAAS,CAACQ,MAAM,CAAC;;MAE7E;MACA,MAAMC,cAAc,GAAG,IAAIC,mBAAmB,CAC1CJ,WAAW,CAACvB,gBAAgB,EAC5BuB,WAAW,CAACxC,MAAM,EAClBwC,WAAW,CAACzB,UAChB,CAAC;;MAED;MACA,MAAM8B,MAAM,GAAGF,cAAc,CAACG,kBAAkB,CAAC,CAAC;MAClDD,MAAM,CAACH,MAAM,GAAGF,WAAW;;MAE3B;MACA,MAAMO,aAAa,GAAG,IAAIhE,aAAa,CAAC;QACpC8D,MAAM,EAAEA,MAAM;QACdG,KAAK,EAAE,CAAC,YAAY,EAAE,WAAW,EAAE,YAAY,CAAC;QAChDC,SAAS,EAAE;MACf,CAAC,CAAC;;MAEF;MACA,MAAMC,YAAY,GAAG;QACjBC,UAAU,EAAE,IAAI;QAChBC,SAAS,EAAE,EAAE;QACbC,SAAS,EAAE;MACf,CAAC;MAED,OAAO,IAAIC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;QACpCT,aAAa,CAACU,EAAE,CAAC,eAAe,EAAGC,KAAK,IAAK;UACzC,MAAM;YAAEC,IAAI;YAAEC;UAAM,CAAC,GAAGF,KAAK,CAACG,IAAI;UAElC,IAAIF,IAAI,KAAK,YAAY,EAAE;YACvBT,YAAY,CAACC,UAAU,GAAGS,KAAK;UACnC,CAAC,MAAM,IAAID,IAAI,KAAK,WAAW,EAAE;YAC7BT,YAAY,CAACE,SAAS,CAACU,IAAI,CAACF,KAAK,CAAC;UACtC,CAAC,MAAM,IAAID,IAAI,KAAK,YAAY,EAAE;YAC9BT,YAAY,CAACG,SAAS,CAACS,IAAI,CAACF,KAAK,CAAC;UACtC;QACJ,CAAC,CAAC;QAEFb,aAAa,CAACU,EAAE,CAAC,MAAM,EAAE,MAAM;UAC3B;UACA,MAAMM,YAAY,GAAGb,YAAY,CAACE,SAAS,CAACpD,MAAM,GAAG,CAAC,GAClDkD,YAAY,CAACE,SAAS,CAACY,MAAM,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,GAAGC,CAAC,CAAC,GAAGhB,YAAY,CAACE,SAAS,CAACpD,MAAM,GAC9EkD,YAAY,CAACC,UAAU;UAE3B,MAAMgB,YAAY,GAAGjB,YAAY,CAACG,SAAS,CAACrD,MAAM,GAAG,CAAC,GAClDkD,YAAY,CAACG,SAAS,CAACW,MAAM,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,GAAGC,CAAC,CAAC,GAAGhB,YAAY,CAACG,SAAS,CAACrD,MAAM,GAC9EkD,YAAY,CAACC,UAAU;UAE3BI,OAAO,CAAC;YACJJ,UAAU,EAAED,YAAY,CAACC,UAAU,IAAI,CAAC,IAAI;YAAE;YAC9CC,SAAS,EAAEW,YAAY,IAAIb,YAAY,CAACC,UAAU,IAAI,CAAC,IAAI;YAC3DE,SAAS,EAAEc,YAAY,IAAIjB,YAAY,CAACC,UAAU,IAAI,CAAC;UAC3D,CAAC,CAAC;QACN,CAAC,CAAC;QAEFJ,aAAa,CAACU,EAAE,CAAC,OAAO,EAAGlD,KAAK,IAAK;UACjCb,OAAO,CAACa,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;UACvD;UACAgD,OAAO,CAAC;YACJJ,UAAU,EAAE,CAAC,IAAI,IAAIiB,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;YAAE;YAC7CjB,SAAS,EAAE,CAAC,IAAI,IAAIgB,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;YAC1ChB,SAAS,EAAE,CAAC,IAAI,IAAIe,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC;UAC7C,CAAC,CAAC;QACN,CAAC,CAAC;;QAEF;QACAtB,aAAa,CAACuB,KAAK,CAAC,CAAC;;QAErB;QACAC,UAAU,CAAC,MAAM;UACb,IAAIrB,YAAY,CAACC,UAAU,KAAK,IAAI,EAAE;YAClCzD,OAAO,CAACc,IAAI,CAAC,0CAA0C,CAAC;YACxD+C,OAAO,CAAC;cACJJ,UAAU,EAAE,CAAC,IAAI,IAAIiB,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;cAC3CjB,SAAS,EAAE,CAAC,IAAI,IAAIgB,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;cAC1ChB,SAAS,EAAE,CAAC,IAAI,IAAIe,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC;YAC7C,CAAC,CAAC;UACN;QACJ,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;MACf,CAAC,CAAC;IAEN,CAAC,CAAC,OAAO9D,KAAK,EAAE;MACZb,OAAO,CAACa,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;MAClD;MACA,OAAO;QACH4C,UAAU,EAAE,CAAC,IAAI,IAAIiB,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QAC3CjB,SAAS,EAAE,CAAC,IAAI,IAAIgB,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QAC1ChB,SAAS,EAAE,CAAC,IAAI,IAAIe,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC;MAC7C,CAAC;IACL;EACJ;EAEA3C,mBAAmBA,CAAC8C,SAAS,EAAE;IAC3B,MAAM;MAAE7D,IAAI;MAAEF;IAAS,CAAC,GAAG+D,SAAS;;IAEpC;IACA,MAAMpD,UAAU,GAAG,CAAC,IAAI;IACxB,MAAMC,WAAW,GAAG,CAAC,GAAG;;IAExB;IACA,MAAMoD,QAAQ,GAAGrD,UAAU,GAAGT,IAAI;;IAElC;IACA,MAAM+D,MAAM,GAAGrD,WAAW,GAAGZ,QAAQ;;IAErC;IACA,MAAMkE,WAAW,GAAGP,IAAI,CAACQ,GAAG,CAACH,QAAQ,EAAEC,MAAM,CAAC;;IAE9C;IACA,MAAMG,cAAc,GAAGlE,IAAI,GAAGgE,WAAW;IACzC,MAAMG,kBAAkB,GAAGrE,QAAQ,GAAGkE,WAAW;;IAEjD;IACA,MAAMI,UAAU,GAAGL,MAAM,GAAGD,QAAQ,GAAG,UAAU,GAAG,MAAM;IAE1D,OAAO;MACH,GAAGD,SAAS;MACZ7D,IAAI;MACJF,QAAQ;MACRoE,cAAc;MACdC,kBAAkB;MAClBH,WAAW;MACXI,UAAU;MACV3D,UAAU;MACVC,WAAW;MACX2D,YAAY,EAAE5D,UAAU,GAAGyD,cAAc;MACzCI,UAAU,EAAE5D,WAAW,GAAGyD,kBAAkB;MAC5ChE,QAAQ,EAAE0D,SAAS,CAAC1D,QAAQ,IAAI,IAAI,CAACoE,gBAAgB,CAACV,SAAS,CAACjF,QAAQ;IAC5E,CAAC;EACL;EAIAsC,oBAAoBA,CAACtC,QAAQ,EAAE;IAC3B;IACA,MAAM4F,QAAQ,GAAG,CAAC,EAAE,GAAIf,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,CAAE,CAAC,CAAC;IAC5C,MAAMe,YAAY,GAAG,CAAC,CAAC,GAAIhB,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,CAAE,CAAC,CAAC;IAC/C,MAAMgB,YAAY,GAAG,GAAG,GAAIjB,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAI,CAAC,CAAC;;IAElD,MAAMG,SAAS,GAAG;MACdjF,QAAQ;MACRoB,IAAI,EAAEwE,QAAQ;MACd1E,QAAQ,EAAE2E,YAAY;MACtBtE,QAAQ,EAAEuE;IACd,CAAC;IAED,OAAO,IAAI,CAAC3D,mBAAmB,CAAC8C,SAAS,CAAC;EAC9C;EAEAU,gBAAgBA,CAAC3F,QAAQ,EAAE;IACvB;IACA;IACA,OAAO,GAAG,GAAI6E,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAI;EACtC;EAEA,MAAMiB,eAAeA,CAAC/F,QAAQ,EAAE;IAC5B,IAAI;MACA,IAAI,CAAC,IAAI,CAACF,YAAY,EAAE;QACpB,MAAM,IAAI,CAACkG,sBAAsB,CAAC,CAAC;MACvC;;MAEA;MACA;MACA,OAAO,IAAI,CAACC,qBAAqB,CAAC,CAAC;IACvC,CAAC,CAAC,OAAOjF,KAAK,EAAE;MACZb,OAAO,CAACa,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;MACnD,OAAO,IAAI;IACf;EACJ;EAEAiF,qBAAqBA,CAAA,EAAG;IACpB,IAAI,CAAC,IAAI,CAACnG,YAAY,EAAE,OAAO,IAAI;IAEnC,MAAM0B,UAAU,GAAG,IAAI,CAAC1B,YAAY,CAAC0B,UAAU;IAC/C,MAAMD,QAAQ,GAAG,CAAC,CAAC,CAAC;IACpB,MAAM2E,UAAU,GAAG1E,UAAU,GAAGD,QAAQ;IAExC,MAAM0B,WAAW,GAAG,IAAI,CAACnD,YAAY,CAACqG,YAAY,CAAC,CAAC,EAAED,UAAU,EAAE1E,UAAU,CAAC;;IAE7E;IACA,KAAK,IAAI4E,OAAO,GAAG,CAAC,EAAEA,OAAO,GAAGnD,WAAW,CAACvB,gBAAgB,EAAE0E,OAAO,EAAE,EAAE;MACrE,MAAMC,WAAW,GAAGpD,WAAW,CAACqD,cAAc,CAACF,OAAO,CAAC;MACvD,KAAK,IAAIG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGL,UAAU,EAAEK,CAAC,EAAE,EAAE;QACjC;QACA,MAAMC,CAAC,GAAGD,CAAC,GAAG/E,UAAU;QACxB6E,WAAW,CAACE,CAAC,CAAC,GAAG1B,IAAI,CAAC4B,GAAG,CAAC,CAAC,GAAG5B,IAAI,CAAC6B,EAAE,GAAG,GAAG,GAAGF,CAAC,CAAC,GAAG,GAAG,GAAG3B,IAAI,CAAC8B,GAAG,CAAC,CAACH,CAAC,GAAG,GAAG,CAAC,GACvE,CAAC3B,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG,IAAI,GAAG;MACnC;IACJ;IAEA,OAAO7B,WAAW;EACtB;EAEA2D,qBAAqBA,CAAC3D,WAAW,EAAE4D,aAAa,GAAG,IAAI,EAAE;IACrD,IAAI,CAAC5D,WAAW,EAAE,OAAO,IAAI;IAE7B,MAAMoD,WAAW,GAAGpD,WAAW,CAACqD,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC;IACnD,MAAMQ,eAAe,GAAGjC,IAAI,CAACkC,KAAK,CAACV,WAAW,CAAC5F,MAAM,GAAGoG,aAAa,CAAC;IACtE,MAAMtG,YAAY,GAAG,EAAE;IAEvB,KAAK,IAAIgG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGM,aAAa,EAAEN,CAAC,EAAE,EAAE;MACpC,MAAMxB,KAAK,GAAGwB,CAAC,GAAGO,eAAe;MACjC,MAAME,GAAG,GAAGnC,IAAI,CAACQ,GAAG,CAACN,KAAK,GAAG+B,eAAe,EAAET,WAAW,CAAC5F,MAAM,CAAC;MAEjE,IAAI4E,GAAG,GAAG,CAAC;MACX,IAAI4B,GAAG,GAAG,CAAC;MAEX,KAAK,IAAIC,CAAC,GAAGnC,KAAK,EAAEmC,CAAC,GAAGF,GAAG,EAAEE,CAAC,EAAE,EAAE;QAC9B,MAAMC,MAAM,GAAGd,WAAW,CAACa,CAAC,CAAC;QAC7B,IAAIC,MAAM,GAAGF,GAAG,EAAEA,GAAG,GAAGE,MAAM;QAC9B,IAAIA,MAAM,GAAG9B,GAAG,EAAEA,GAAG,GAAG8B,MAAM;MAClC;;MAEA;MACA5G,YAAY,CAACgE,IAAI,CAACM,IAAI,CAACoC,GAAG,CAACpC,IAAI,CAACuC,GAAG,CAAC/B,GAAG,CAAC,EAAER,IAAI,CAACuC,GAAG,CAACH,GAAG,CAAC,CAAC,CAAC;IAC7D;IAEA,OAAO1G,YAAY;EACvB;EAEA,MAAM8G,cAAcA,CAACpE,WAAW,EAAEpB,UAAU,GAAG,CAAC,EAAE,EAAEC,WAAW,GAAG,CAAC,CAAC,EAAE;IAClE,IAAI;MACA,IAAIjB,MAAM,CAACC,WAAW,EAAE;QACpB;QACA,MAAMwG,MAAM,GAAG,MAAMzG,MAAM,CAACC,WAAW,CAACuG,cAAc,CAClDpE,WAAW,EACXpB,UAAU,EACVC,WACJ,CAAC;QACD,OAAOwF,MAAM;MACjB,CAAC,MAAM;QACH;QACA,OAAO;UACHC,gBAAgB,EAAEtE,WAAW;UAC7BmC,WAAW,EAAE,CAAC;UACdoC,SAAS,EAAE3F,UAAU;UACrB4F,aAAa,EAAE3F;QACnB,CAAC;MACL;IACJ,CAAC,CAAC,OAAOd,KAAK,EAAE;MACZb,OAAO,CAACa,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;MAChD,OAAO,IAAI;IACf;EACJ;;EAEA;EACA0G,UAAUA,CAACtG,IAAI,EAAE;IACb,OAAO,GAAGA,IAAI,CAACiB,OAAO,CAAC,CAAC,CAAC,OAAO;EACpC;;EAEA;EACAsF,cAAcA,CAACzG,QAAQ,EAAE;IACrB,OAAO,GAAGA,QAAQ,CAACmB,OAAO,CAAC,CAAC,CAAC,OAAO;EACxC;;EAEA;EACAuF,UAAUA,CAACC,IAAI,EAAE;IACb,MAAMC,IAAI,GAAGD,IAAI,IAAI,CAAC,GAAG,GAAG,GAAG,EAAE;IACjC,OAAO,GAAGC,IAAI,GAAGD,IAAI,CAACxF,OAAO,CAAC,CAAC,CAAC,KAAK;EACzC;;EAEA;EACA0F,OAAOA,CAAA,EAAG;IACN,IAAI,IAAI,CAACjI,YAAY,IAAI,IAAI,CAACA,YAAY,CAACkI,KAAK,KAAK,QAAQ,EAAE;MAC3D,IAAI,CAAClI,YAAY,CAACmI,KAAK,CAAC,CAAC;IAC7B;EACJ;AACJ;AAEA,eAAexI,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}