{"ast": null, "code": "class AudioEngine {\n  constructor() {\n    this.audioContext = null;\n    this.currentSource = null;\n    this.currentBuffer = null;\n    this.isPlaying = false;\n    this.startTime = 0;\n    this.pauseTime = 0;\n    this.currentPosition = 0;\n    this.duration = 0;\n    this.volume = 0.8;\n    this.gainNode = null;\n    this.onTimeUpdate = null;\n    this.onEnded = null;\n    this.onError = null;\n    this.onLoadStart = null;\n    this.onLoadEnd = null;\n    this.audioCache = new Map(); // Cache for preloaded audio buffers\n\n    this.initializeAudioContext();\n  }\n  async initializeAudioContext() {\n    try {\n      this.audioContext = new (window.AudioContext || window.webkitAudioContext)();\n\n      // Create gain node for volume control\n      this.gainNode = this.audioContext.createGain();\n      this.gainNode.connect(this.audioContext.destination);\n      this.gainNode.gain.value = this.volume;\n\n      // Resume context if suspended (required by some browsers)\n      if (this.audioContext.state === 'suspended') {\n        await this.audioContext.resume();\n      }\n      console.log('AudioContext initialized successfully');\n    } catch (error) {\n      console.error('Failed to initialize AudioContext:', error);\n    }\n  }\n  async preloadAudioFile(file) {\n    try {\n      if (!this.audioContext) {\n        await this.initializeAudioContext();\n      }\n\n      // Create a cache key\n      const cacheKey = file instanceof File ? file.name + file.size : file;\n\n      // Check if already cached\n      if (this.audioCache.has(cacheKey)) {\n        return this.audioCache.get(cacheKey);\n      }\n      let arrayBuffer;\n      if (file instanceof File) {\n        // Browser File object\n        arrayBuffer = await file.arrayBuffer();\n      } else if (typeof file === 'string') {\n        // File path (for Electron) - use file:// protocol\n        const fileUrl = file.startsWith('file://') ? file : `file://${file.replace(/\\\\/g, '/')}`;\n        try {\n          const response = await fetch(fileUrl);\n          arrayBuffer = await response.arrayBuffer();\n        } catch (error) {\n          console.error('Error reading file:', error);\n          throw new Error(`Failed to read audio file: ${file}`);\n        }\n      } else {\n        throw new Error('Invalid file type');\n      }\n\n      // Decode audio data\n      const audioBuffer = await this.audioContext.decodeAudioData(arrayBuffer);\n\n      // Cache the buffer\n      this.audioCache.set(cacheKey, audioBuffer);\n      console.log(`Audio preloaded: ${audioBuffer.duration.toFixed(2)}s, ${audioBuffer.sampleRate}Hz`);\n      return audioBuffer;\n    } catch (error) {\n      console.error('Error preloading audio file:', error);\n      throw error;\n    }\n  }\n  async loadAudioFile(file) {\n    try {\n      if (!this.audioContext) {\n        await this.initializeAudioContext();\n      }\n\n      // Notify load start\n      if (this.onLoadStart) {\n        this.onLoadStart();\n      }\n\n      // Stop current playback\n      this.stop();\n\n      // Try to get from cache first, otherwise preload\n      const cacheKey = file instanceof File ? file.name + file.size : file;\n      let audioBuffer = this.audioCache.get(cacheKey);\n      if (!audioBuffer) {\n        audioBuffer = await this.preloadAudioFile(file);\n      }\n      this.currentBuffer = audioBuffer;\n      this.duration = this.currentBuffer.duration;\n      this.currentPosition = 0;\n      console.log(`Audio loaded from cache: ${this.duration.toFixed(2)}s`);\n\n      // Notify load end\n      if (this.onLoadEnd) {\n        this.onLoadEnd();\n      }\n      return {\n        duration: this.duration,\n        sampleRate: this.currentBuffer.sampleRate,\n        numberOfChannels: this.currentBuffer.numberOfChannels,\n        buffer: this.currentBuffer\n      };\n    } catch (error) {\n      console.error('Error loading audio file:', error);\n\n      // Notify error\n      if (this.onError) {\n        this.onError(error);\n      }\n      throw error;\n    }\n  }\n  play(startTime = 0) {\n    if (!this.currentBuffer || !this.audioContext) {\n      console.warn('No audio buffer loaded');\n      return;\n    }\n\n    // Stop current playback\n    this.stop();\n    try {\n      // Create new source\n      this.currentSource = this.audioContext.createBufferSource();\n      this.currentSource.buffer = this.currentBuffer;\n      this.currentSource.connect(this.gainNode);\n\n      // Set up ended callback\n      this.currentSource.onended = () => {\n        this.isPlaying = false;\n        if (this.onEnded) {\n          this.onEnded();\n        }\n      };\n\n      // Start playback\n      const offset = startTime || this.currentPosition;\n      this.currentSource.start(0, offset);\n      this.startTime = this.audioContext.currentTime - offset;\n      this.isPlaying = true;\n\n      // Start time update loop\n      this.startTimeUpdateLoop();\n      console.log(`Playback started at ${offset.toFixed(2)}s`);\n    } catch (error) {\n      console.error('Error starting playback:', error);\n      this.isPlaying = false;\n\n      // Notify error\n      if (this.onError) {\n        this.onError(error);\n      }\n    }\n  }\n  pause() {\n    if (this.currentSource && this.isPlaying) {\n      this.currentPosition = this.audioContext.currentTime - this.startTime;\n      this.stop();\n      console.log(`Playback paused at ${this.currentPosition.toFixed(2)}s`);\n    }\n  }\n  stop() {\n    if (this.currentSource) {\n      try {\n        this.currentSource.stop();\n      } catch (error) {\n        // Source might already be stopped\n      }\n      this.currentSource = null;\n    }\n    this.isPlaying = false;\n    this.stopTimeUpdateLoop();\n  }\n  seek(position) {\n    const wasPlaying = this.isPlaying;\n    this.currentPosition = Math.max(0, Math.min(position, this.duration));\n    if (wasPlaying) {\n      this.play(this.currentPosition);\n    }\n    console.log(`Seeked to ${this.currentPosition.toFixed(2)}s`);\n  }\n  setVolume(volume) {\n    this.volume = Math.max(0, Math.min(1, volume));\n    if (this.gainNode) {\n      this.gainNode.gain.value = this.volume;\n    }\n  }\n  getCurrentTime() {\n    if (this.isPlaying && this.audioContext) {\n      return this.audioContext.currentTime - this.startTime;\n    }\n    return this.currentPosition;\n  }\n  getDuration() {\n    return this.duration;\n  }\n  getIsPlaying() {\n    return this.isPlaying;\n  }\n\n  // Time update loop for progress tracking\n  startTimeUpdateLoop() {\n    this.stopTimeUpdateLoop();\n    this.timeUpdateInterval = setInterval(() => {\n      if (this.isPlaying && this.onTimeUpdate) {\n        const currentTime = this.getCurrentTime();\n        this.onTimeUpdate(currentTime, this.duration);\n      }\n    }, 100); // Update every 100ms\n  }\n  stopTimeUpdateLoop() {\n    if (this.timeUpdateInterval) {\n      clearInterval(this.timeUpdateInterval);\n      this.timeUpdateInterval = null;\n    }\n  }\n\n  // Generate waveform data from audio buffer\n  generateWaveformData(targetSamples = 1000) {\n    if (!this.currentBuffer) {\n      console.warn('No current buffer for waveform generation');\n      return null;\n    }\n    console.log(`Generating waveform from buffer: ${this.currentBuffer.duration}s, ${this.currentBuffer.numberOfChannels} channels`);\n    const channelData = this.currentBuffer.getChannelData(0); // Use left channel\n    const samplesPerPixel = Math.floor(channelData.length / targetSamples);\n    const waveformData = [];\n    console.log(`Channel data length: ${channelData.length}, samples per pixel: ${samplesPerPixel}`);\n    for (let i = 0; i < targetSamples; i++) {\n      const start = i * samplesPerPixel;\n      const end = Math.min(start + samplesPerPixel, channelData.length);\n      let min = 0;\n      let max = 0;\n      for (let j = start; j < end; j++) {\n        const sample = channelData[j];\n        if (sample > max) max = sample;\n        if (sample < min) min = sample;\n      }\n\n      // Store the peak amplitude for this segment\n      waveformData.push(Math.max(Math.abs(min), Math.abs(max)));\n    }\n    console.log(`Generated waveform data: ${waveformData.length} points, max value: ${Math.max(...waveformData)}`);\n    return waveformData;\n  }\n\n  // Calculate basic audio metrics\n  calculateAudioMetrics() {\n    if (!this.currentBuffer) return null;\n    const channelData = this.currentBuffer.getChannelData(0);\n    let sum = 0;\n    let peak = 0;\n    for (let i = 0; i < channelData.length; i++) {\n      const sample = Math.abs(channelData[i]);\n      sum += sample * sample;\n      if (sample > peak) peak = sample;\n    }\n    const rms = Math.sqrt(sum / channelData.length);\n    const peakDb = 20 * Math.log10(peak);\n    const rmsDb = 20 * Math.log10(rms);\n    return {\n      peak: peak,\n      peakDb: peakDb,\n      rms: rms,\n      rmsDb: rmsDb,\n      duration: this.duration,\n      sampleRate: this.currentBuffer.sampleRate,\n      channels: this.currentBuffer.numberOfChannels\n    };\n  }\n\n  // Clean up resources\n  dispose() {\n    this.stop();\n    this.stopTimeUpdateLoop();\n    if (this.audioContext && this.audioContext.state !== 'closed') {\n      this.audioContext.close();\n    }\n    this.currentBuffer = null;\n    this.gainNode = null;\n    this.audioContext = null;\n  }\n}\nexport default AudioEngine;", "map": {"version": 3, "names": ["AudioEngine", "constructor", "audioContext", "currentSource", "current<PERSON><PERSON><PERSON>", "isPlaying", "startTime", "pauseTime", "currentPosition", "duration", "volume", "gainNode", "onTimeUpdate", "onEnded", "onError", "onLoadStart", "onLoadEnd", "audioCache", "Map", "initializeAudioContext", "window", "AudioContext", "webkitAudioContext", "createGain", "connect", "destination", "gain", "value", "state", "resume", "console", "log", "error", "preloadAudioFile", "file", "cache<PERSON>ey", "File", "name", "size", "has", "get", "arrayBuffer", "fileUrl", "startsWith", "replace", "response", "fetch", "Error", "audioBuffer", "decodeAudioData", "set", "toFixed", "sampleRate", "loadAudioFile", "stop", "numberOfChannels", "buffer", "play", "warn", "createBufferSource", "onended", "offset", "start", "currentTime", "startTimeUpdateLoop", "pause", "stopTimeUpdateLoop", "seek", "position", "wasPlaying", "Math", "max", "min", "setVolume", "getCurrentTime", "getDuration", "getIsPlaying", "timeUpdateInterval", "setInterval", "clearInterval", "generateWaveformData", "targetSamples", "channelData", "getChannelData", "samplesPerPixel", "floor", "length", "waveformData", "i", "end", "j", "sample", "push", "abs", "calculateAudioMetrics", "sum", "peak", "rms", "sqrt", "peakDb", "log10", "rmsDb", "channels", "dispose", "close"], "sources": ["C:/Users/<USER>/Desktop/VSC Folder/AlbumPlayer/src/audio/AudioEngine.js"], "sourcesContent": ["class AudioEngine {\n    constructor() {\n        this.audioContext = null;\n        this.currentSource = null;\n        this.currentBuffer = null;\n        this.isPlaying = false;\n        this.startTime = 0;\n        this.pauseTime = 0;\n        this.currentPosition = 0;\n        this.duration = 0;\n        this.volume = 0.8;\n        this.gainNode = null;\n        this.onTimeUpdate = null;\n        this.onEnded = null;\n        this.onError = null;\n        this.onLoadStart = null;\n        this.onLoadEnd = null;\n        this.audioCache = new Map(); // Cache for preloaded audio buffers\n\n        this.initializeAudioContext();\n    }\n\n    async initializeAudioContext() {\n        try {\n            this.audioContext = new(window.AudioContext || window.webkitAudioContext)();\n\n            // Create gain node for volume control\n            this.gainNode = this.audioContext.createGain();\n            this.gainNode.connect(this.audioContext.destination);\n            this.gainNode.gain.value = this.volume;\n\n            // Resume context if suspended (required by some browsers)\n            if (this.audioContext.state === 'suspended') {\n                await this.audioContext.resume();\n            }\n\n            console.log('AudioContext initialized successfully');\n        } catch (error) {\n            console.error('Failed to initialize AudioContext:', error);\n        }\n    }\n\n    async preloadAudioFile(file) {\n        try {\n            if (!this.audioContext) {\n                await this.initializeAudioContext();\n            }\n\n            // Create a cache key\n            const cacheKey = file instanceof File ? file.name + file.size : file;\n\n            // Check if already cached\n            if (this.audioCache.has(cacheKey)) {\n                return this.audioCache.get(cacheKey);\n            }\n\n            let arrayBuffer;\n\n            if (file instanceof File) {\n                // Browser File object\n                arrayBuffer = await file.arrayBuffer();\n            } else if (typeof file === 'string') {\n                // File path (for Electron) - use file:// protocol\n                const fileUrl = file.startsWith('file://') ? file : `file://${file.replace(/\\\\/g, '/')}`;\n                try {\n                    const response = await fetch(fileUrl);\n                    arrayBuffer = await response.arrayBuffer();\n                } catch (error) {\n                    console.error('Error reading file:', error);\n                    throw new Error(`Failed to read audio file: ${file}`);\n                }\n            } else {\n                throw new Error('Invalid file type');\n            }\n\n            // Decode audio data\n            const audioBuffer = await this.audioContext.decodeAudioData(arrayBuffer);\n\n            // Cache the buffer\n            this.audioCache.set(cacheKey, audioBuffer);\n\n            console.log(`Audio preloaded: ${audioBuffer.duration.toFixed(2)}s, ${audioBuffer.sampleRate}Hz`);\n\n            return audioBuffer;\n\n        } catch (error) {\n            console.error('Error preloading audio file:', error);\n            throw error;\n        }\n    }\n\n    async loadAudioFile(file) {\n        try {\n            if (!this.audioContext) {\n                await this.initializeAudioContext();\n            }\n\n            // Notify load start\n            if (this.onLoadStart) {\n                this.onLoadStart();\n            }\n\n            // Stop current playback\n            this.stop();\n\n            // Try to get from cache first, otherwise preload\n            const cacheKey = file instanceof File ? file.name + file.size : file;\n            let audioBuffer = this.audioCache.get(cacheKey);\n\n            if (!audioBuffer) {\n                audioBuffer = await this.preloadAudioFile(file);\n            }\n\n            this.currentBuffer = audioBuffer;\n            this.duration = this.currentBuffer.duration;\n            this.currentPosition = 0;\n\n            console.log(`Audio loaded from cache: ${this.duration.toFixed(2)}s`);\n\n            // Notify load end\n            if (this.onLoadEnd) {\n                this.onLoadEnd();\n            }\n\n            return {\n                duration: this.duration,\n                sampleRate: this.currentBuffer.sampleRate,\n                numberOfChannels: this.currentBuffer.numberOfChannels,\n                buffer: this.currentBuffer\n            };\n\n        } catch (error) {\n            console.error('Error loading audio file:', error);\n\n            // Notify error\n            if (this.onError) {\n                this.onError(error);\n            }\n\n            throw error;\n        }\n    }\n\n    play(startTime = 0) {\n        if (!this.currentBuffer || !this.audioContext) {\n            console.warn('No audio buffer loaded');\n            return;\n        }\n\n        // Stop current playback\n        this.stop();\n\n        try {\n            // Create new source\n            this.currentSource = this.audioContext.createBufferSource();\n            this.currentSource.buffer = this.currentBuffer;\n            this.currentSource.connect(this.gainNode);\n\n            // Set up ended callback\n            this.currentSource.onended = () => {\n                this.isPlaying = false;\n                if (this.onEnded) {\n                    this.onEnded();\n                }\n            };\n\n            // Start playback\n            const offset = startTime || this.currentPosition;\n            this.currentSource.start(0, offset);\n            this.startTime = this.audioContext.currentTime - offset;\n            this.isPlaying = true;\n\n            // Start time update loop\n            this.startTimeUpdateLoop();\n\n            console.log(`Playback started at ${offset.toFixed(2)}s`);\n\n        } catch (error) {\n            console.error('Error starting playback:', error);\n            this.isPlaying = false;\n\n            // Notify error\n            if (this.onError) {\n                this.onError(error);\n            }\n        }\n    }\n\n    pause() {\n        if (this.currentSource && this.isPlaying) {\n            this.currentPosition = this.audioContext.currentTime - this.startTime;\n            this.stop();\n            console.log(`Playback paused at ${this.currentPosition.toFixed(2)}s`);\n        }\n    }\n\n    stop() {\n        if (this.currentSource) {\n            try {\n                this.currentSource.stop();\n            } catch (error) {\n                // Source might already be stopped\n            }\n            this.currentSource = null;\n        }\n        this.isPlaying = false;\n        this.stopTimeUpdateLoop();\n    }\n\n    seek(position) {\n        const wasPlaying = this.isPlaying;\n        this.currentPosition = Math.max(0, Math.min(position, this.duration));\n\n        if (wasPlaying) {\n            this.play(this.currentPosition);\n        }\n\n        console.log(`Seeked to ${this.currentPosition.toFixed(2)}s`);\n    }\n\n    setVolume(volume) {\n        this.volume = Math.max(0, Math.min(1, volume));\n        if (this.gainNode) {\n            this.gainNode.gain.value = this.volume;\n        }\n    }\n\n    getCurrentTime() {\n        if (this.isPlaying && this.audioContext) {\n            return this.audioContext.currentTime - this.startTime;\n        }\n        return this.currentPosition;\n    }\n\n    getDuration() {\n        return this.duration;\n    }\n\n    getIsPlaying() {\n        return this.isPlaying;\n    }\n\n    // Time update loop for progress tracking\n    startTimeUpdateLoop() {\n        this.stopTimeUpdateLoop();\n        this.timeUpdateInterval = setInterval(() => {\n            if (this.isPlaying && this.onTimeUpdate) {\n                const currentTime = this.getCurrentTime();\n                this.onTimeUpdate(currentTime, this.duration);\n            }\n        }, 100); // Update every 100ms\n    }\n\n    stopTimeUpdateLoop() {\n        if (this.timeUpdateInterval) {\n            clearInterval(this.timeUpdateInterval);\n            this.timeUpdateInterval = null;\n        }\n    }\n\n    // Generate waveform data from audio buffer\n    generateWaveformData(targetSamples = 1000) {\n        if (!this.currentBuffer) {\n            console.warn('No current buffer for waveform generation');\n            return null;\n        }\n\n        console.log(`Generating waveform from buffer: ${this.currentBuffer.duration}s, ${this.currentBuffer.numberOfChannels} channels`);\n\n        const channelData = this.currentBuffer.getChannelData(0); // Use left channel\n        const samplesPerPixel = Math.floor(channelData.length / targetSamples);\n        const waveformData = [];\n\n        console.log(`Channel data length: ${channelData.length}, samples per pixel: ${samplesPerPixel}`);\n\n        for (let i = 0; i < targetSamples; i++) {\n            const start = i * samplesPerPixel;\n            const end = Math.min(start + samplesPerPixel, channelData.length);\n\n            let min = 0;\n            let max = 0;\n\n            for (let j = start; j < end; j++) {\n                const sample = channelData[j];\n                if (sample > max) max = sample;\n                if (sample < min) min = sample;\n            }\n\n            // Store the peak amplitude for this segment\n            waveformData.push(Math.max(Math.abs(min), Math.abs(max)));\n        }\n\n        console.log(`Generated waveform data: ${waveformData.length} points, max value: ${Math.max(...waveformData)}`);\n        return waveformData;\n    }\n\n    // Calculate basic audio metrics\n    calculateAudioMetrics() {\n        if (!this.currentBuffer) return null;\n\n        const channelData = this.currentBuffer.getChannelData(0);\n        let sum = 0;\n        let peak = 0;\n\n        for (let i = 0; i < channelData.length; i++) {\n            const sample = Math.abs(channelData[i]);\n            sum += sample * sample;\n            if (sample > peak) peak = sample;\n        }\n\n        const rms = Math.sqrt(sum / channelData.length);\n        const peakDb = 20 * Math.log10(peak);\n        const rmsDb = 20 * Math.log10(rms);\n\n        return {\n            peak: peak,\n            peakDb: peakDb,\n            rms: rms,\n            rmsDb: rmsDb,\n            duration: this.duration,\n            sampleRate: this.currentBuffer.sampleRate,\n            channels: this.currentBuffer.numberOfChannels\n        };\n    }\n\n    // Clean up resources\n    dispose() {\n        this.stop();\n        this.stopTimeUpdateLoop();\n\n        if (this.audioContext && this.audioContext.state !== 'closed') {\n            this.audioContext.close();\n        }\n\n        this.currentBuffer = null;\n        this.gainNode = null;\n        this.audioContext = null;\n    }\n}\n\nexport default AudioEngine;"], "mappings": "AAAA,MAAMA,WAAW,CAAC;EACdC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,YAAY,GAAG,IAAI;IACxB,IAAI,CAACC,aAAa,GAAG,IAAI;IACzB,IAAI,CAACC,aAAa,GAAG,IAAI;IACzB,IAAI,CAACC,SAAS,GAAG,KAAK;IACtB,IAAI,CAACC,SAAS,GAAG,CAAC;IAClB,IAAI,CAACC,SAAS,GAAG,CAAC;IAClB,IAAI,CAACC,eAAe,GAAG,CAAC;IACxB,IAAI,CAACC,QAAQ,GAAG,CAAC;IACjB,IAAI,CAACC,MAAM,GAAG,GAAG;IACjB,IAAI,CAACC,QAAQ,GAAG,IAAI;IACpB,IAAI,CAACC,YAAY,GAAG,IAAI;IACxB,IAAI,CAACC,OAAO,GAAG,IAAI;IACnB,IAAI,CAACC,OAAO,GAAG,IAAI;IACnB,IAAI,CAACC,WAAW,GAAG,IAAI;IACvB,IAAI,CAACC,SAAS,GAAG,IAAI;IACrB,IAAI,CAACC,UAAU,GAAG,IAAIC,GAAG,CAAC,CAAC,CAAC,CAAC;;IAE7B,IAAI,CAACC,sBAAsB,CAAC,CAAC;EACjC;EAEA,MAAMA,sBAAsBA,CAAA,EAAG;IAC3B,IAAI;MACA,IAAI,CAACjB,YAAY,GAAG,KAAIkB,MAAM,CAACC,YAAY,IAAID,MAAM,CAACE,kBAAkB,EAAE,CAAC;;MAE3E;MACA,IAAI,CAACX,QAAQ,GAAG,IAAI,CAACT,YAAY,CAACqB,UAAU,CAAC,CAAC;MAC9C,IAAI,CAACZ,QAAQ,CAACa,OAAO,CAAC,IAAI,CAACtB,YAAY,CAACuB,WAAW,CAAC;MACpD,IAAI,CAACd,QAAQ,CAACe,IAAI,CAACC,KAAK,GAAG,IAAI,CAACjB,MAAM;;MAEtC;MACA,IAAI,IAAI,CAACR,YAAY,CAAC0B,KAAK,KAAK,WAAW,EAAE;QACzC,MAAM,IAAI,CAAC1B,YAAY,CAAC2B,MAAM,CAAC,CAAC;MACpC;MAEAC,OAAO,CAACC,GAAG,CAAC,uCAAuC,CAAC;IACxD,CAAC,CAAC,OAAOC,KAAK,EAAE;MACZF,OAAO,CAACE,KAAK,CAAC,oCAAoC,EAAEA,KAAK,CAAC;IAC9D;EACJ;EAEA,MAAMC,gBAAgBA,CAACC,IAAI,EAAE;IACzB,IAAI;MACA,IAAI,CAAC,IAAI,CAAChC,YAAY,EAAE;QACpB,MAAM,IAAI,CAACiB,sBAAsB,CAAC,CAAC;MACvC;;MAEA;MACA,MAAMgB,QAAQ,GAAGD,IAAI,YAAYE,IAAI,GAAGF,IAAI,CAACG,IAAI,GAAGH,IAAI,CAACI,IAAI,GAAGJ,IAAI;;MAEpE;MACA,IAAI,IAAI,CAACjB,UAAU,CAACsB,GAAG,CAACJ,QAAQ,CAAC,EAAE;QAC/B,OAAO,IAAI,CAAClB,UAAU,CAACuB,GAAG,CAACL,QAAQ,CAAC;MACxC;MAEA,IAAIM,WAAW;MAEf,IAAIP,IAAI,YAAYE,IAAI,EAAE;QACtB;QACAK,WAAW,GAAG,MAAMP,IAAI,CAACO,WAAW,CAAC,CAAC;MAC1C,CAAC,MAAM,IAAI,OAAOP,IAAI,KAAK,QAAQ,EAAE;QACjC;QACA,MAAMQ,OAAO,GAAGR,IAAI,CAACS,UAAU,CAAC,SAAS,CAAC,GAAGT,IAAI,GAAG,UAAUA,IAAI,CAACU,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC,EAAE;QACxF,IAAI;UACA,MAAMC,QAAQ,GAAG,MAAMC,KAAK,CAACJ,OAAO,CAAC;UACrCD,WAAW,GAAG,MAAMI,QAAQ,CAACJ,WAAW,CAAC,CAAC;QAC9C,CAAC,CAAC,OAAOT,KAAK,EAAE;UACZF,OAAO,CAACE,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAAC;UAC3C,MAAM,IAAIe,KAAK,CAAC,8BAA8Bb,IAAI,EAAE,CAAC;QACzD;MACJ,CAAC,MAAM;QACH,MAAM,IAAIa,KAAK,CAAC,mBAAmB,CAAC;MACxC;;MAEA;MACA,MAAMC,WAAW,GAAG,MAAM,IAAI,CAAC9C,YAAY,CAAC+C,eAAe,CAACR,WAAW,CAAC;;MAExE;MACA,IAAI,CAACxB,UAAU,CAACiC,GAAG,CAACf,QAAQ,EAAEa,WAAW,CAAC;MAE1ClB,OAAO,CAACC,GAAG,CAAC,oBAAoBiB,WAAW,CAACvC,QAAQ,CAAC0C,OAAO,CAAC,CAAC,CAAC,MAAMH,WAAW,CAACI,UAAU,IAAI,CAAC;MAEhG,OAAOJ,WAAW;IAEtB,CAAC,CAAC,OAAOhB,KAAK,EAAE;MACZF,OAAO,CAACE,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACpD,MAAMA,KAAK;IACf;EACJ;EAEA,MAAMqB,aAAaA,CAACnB,IAAI,EAAE;IACtB,IAAI;MACA,IAAI,CAAC,IAAI,CAAChC,YAAY,EAAE;QACpB,MAAM,IAAI,CAACiB,sBAAsB,CAAC,CAAC;MACvC;;MAEA;MACA,IAAI,IAAI,CAACJ,WAAW,EAAE;QAClB,IAAI,CAACA,WAAW,CAAC,CAAC;MACtB;;MAEA;MACA,IAAI,CAACuC,IAAI,CAAC,CAAC;;MAEX;MACA,MAAMnB,QAAQ,GAAGD,IAAI,YAAYE,IAAI,GAAGF,IAAI,CAACG,IAAI,GAAGH,IAAI,CAACI,IAAI,GAAGJ,IAAI;MACpE,IAAIc,WAAW,GAAG,IAAI,CAAC/B,UAAU,CAACuB,GAAG,CAACL,QAAQ,CAAC;MAE/C,IAAI,CAACa,WAAW,EAAE;QACdA,WAAW,GAAG,MAAM,IAAI,CAACf,gBAAgB,CAACC,IAAI,CAAC;MACnD;MAEA,IAAI,CAAC9B,aAAa,GAAG4C,WAAW;MAChC,IAAI,CAACvC,QAAQ,GAAG,IAAI,CAACL,aAAa,CAACK,QAAQ;MAC3C,IAAI,CAACD,eAAe,GAAG,CAAC;MAExBsB,OAAO,CAACC,GAAG,CAAC,4BAA4B,IAAI,CAACtB,QAAQ,CAAC0C,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC;;MAEpE;MACA,IAAI,IAAI,CAACnC,SAAS,EAAE;QAChB,IAAI,CAACA,SAAS,CAAC,CAAC;MACpB;MAEA,OAAO;QACHP,QAAQ,EAAE,IAAI,CAACA,QAAQ;QACvB2C,UAAU,EAAE,IAAI,CAAChD,aAAa,CAACgD,UAAU;QACzCG,gBAAgB,EAAE,IAAI,CAACnD,aAAa,CAACmD,gBAAgB;QACrDC,MAAM,EAAE,IAAI,CAACpD;MACjB,CAAC;IAEL,CAAC,CAAC,OAAO4B,KAAK,EAAE;MACZF,OAAO,CAACE,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;;MAEjD;MACA,IAAI,IAAI,CAAClB,OAAO,EAAE;QACd,IAAI,CAACA,OAAO,CAACkB,KAAK,CAAC;MACvB;MAEA,MAAMA,KAAK;IACf;EACJ;EAEAyB,IAAIA,CAACnD,SAAS,GAAG,CAAC,EAAE;IAChB,IAAI,CAAC,IAAI,CAACF,aAAa,IAAI,CAAC,IAAI,CAACF,YAAY,EAAE;MAC3C4B,OAAO,CAAC4B,IAAI,CAAC,wBAAwB,CAAC;MACtC;IACJ;;IAEA;IACA,IAAI,CAACJ,IAAI,CAAC,CAAC;IAEX,IAAI;MACA;MACA,IAAI,CAACnD,aAAa,GAAG,IAAI,CAACD,YAAY,CAACyD,kBAAkB,CAAC,CAAC;MAC3D,IAAI,CAACxD,aAAa,CAACqD,MAAM,GAAG,IAAI,CAACpD,aAAa;MAC9C,IAAI,CAACD,aAAa,CAACqB,OAAO,CAAC,IAAI,CAACb,QAAQ,CAAC;;MAEzC;MACA,IAAI,CAACR,aAAa,CAACyD,OAAO,GAAG,MAAM;QAC/B,IAAI,CAACvD,SAAS,GAAG,KAAK;QACtB,IAAI,IAAI,CAACQ,OAAO,EAAE;UACd,IAAI,CAACA,OAAO,CAAC,CAAC;QAClB;MACJ,CAAC;;MAED;MACA,MAAMgD,MAAM,GAAGvD,SAAS,IAAI,IAAI,CAACE,eAAe;MAChD,IAAI,CAACL,aAAa,CAAC2D,KAAK,CAAC,CAAC,EAAED,MAAM,CAAC;MACnC,IAAI,CAACvD,SAAS,GAAG,IAAI,CAACJ,YAAY,CAAC6D,WAAW,GAAGF,MAAM;MACvD,IAAI,CAACxD,SAAS,GAAG,IAAI;;MAErB;MACA,IAAI,CAAC2D,mBAAmB,CAAC,CAAC;MAE1BlC,OAAO,CAACC,GAAG,CAAC,uBAAuB8B,MAAM,CAACV,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC;IAE5D,CAAC,CAAC,OAAOnB,KAAK,EAAE;MACZF,OAAO,CAACE,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;MAChD,IAAI,CAAC3B,SAAS,GAAG,KAAK;;MAEtB;MACA,IAAI,IAAI,CAACS,OAAO,EAAE;QACd,IAAI,CAACA,OAAO,CAACkB,KAAK,CAAC;MACvB;IACJ;EACJ;EAEAiC,KAAKA,CAAA,EAAG;IACJ,IAAI,IAAI,CAAC9D,aAAa,IAAI,IAAI,CAACE,SAAS,EAAE;MACtC,IAAI,CAACG,eAAe,GAAG,IAAI,CAACN,YAAY,CAAC6D,WAAW,GAAG,IAAI,CAACzD,SAAS;MACrE,IAAI,CAACgD,IAAI,CAAC,CAAC;MACXxB,OAAO,CAACC,GAAG,CAAC,sBAAsB,IAAI,CAACvB,eAAe,CAAC2C,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC;IACzE;EACJ;EAEAG,IAAIA,CAAA,EAAG;IACH,IAAI,IAAI,CAACnD,aAAa,EAAE;MACpB,IAAI;QACA,IAAI,CAACA,aAAa,CAACmD,IAAI,CAAC,CAAC;MAC7B,CAAC,CAAC,OAAOtB,KAAK,EAAE;QACZ;MAAA;MAEJ,IAAI,CAAC7B,aAAa,GAAG,IAAI;IAC7B;IACA,IAAI,CAACE,SAAS,GAAG,KAAK;IACtB,IAAI,CAAC6D,kBAAkB,CAAC,CAAC;EAC7B;EAEAC,IAAIA,CAACC,QAAQ,EAAE;IACX,MAAMC,UAAU,GAAG,IAAI,CAAChE,SAAS;IACjC,IAAI,CAACG,eAAe,GAAG8D,IAAI,CAACC,GAAG,CAAC,CAAC,EAAED,IAAI,CAACE,GAAG,CAACJ,QAAQ,EAAE,IAAI,CAAC3D,QAAQ,CAAC,CAAC;IAErE,IAAI4D,UAAU,EAAE;MACZ,IAAI,CAACZ,IAAI,CAAC,IAAI,CAACjD,eAAe,CAAC;IACnC;IAEAsB,OAAO,CAACC,GAAG,CAAC,aAAa,IAAI,CAACvB,eAAe,CAAC2C,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC;EAChE;EAEAsB,SAASA,CAAC/D,MAAM,EAAE;IACd,IAAI,CAACA,MAAM,GAAG4D,IAAI,CAACC,GAAG,CAAC,CAAC,EAAED,IAAI,CAACE,GAAG,CAAC,CAAC,EAAE9D,MAAM,CAAC,CAAC;IAC9C,IAAI,IAAI,CAACC,QAAQ,EAAE;MACf,IAAI,CAACA,QAAQ,CAACe,IAAI,CAACC,KAAK,GAAG,IAAI,CAACjB,MAAM;IAC1C;EACJ;EAEAgE,cAAcA,CAAA,EAAG;IACb,IAAI,IAAI,CAACrE,SAAS,IAAI,IAAI,CAACH,YAAY,EAAE;MACrC,OAAO,IAAI,CAACA,YAAY,CAAC6D,WAAW,GAAG,IAAI,CAACzD,SAAS;IACzD;IACA,OAAO,IAAI,CAACE,eAAe;EAC/B;EAEAmE,WAAWA,CAAA,EAAG;IACV,OAAO,IAAI,CAAClE,QAAQ;EACxB;EAEAmE,YAAYA,CAAA,EAAG;IACX,OAAO,IAAI,CAACvE,SAAS;EACzB;;EAEA;EACA2D,mBAAmBA,CAAA,EAAG;IAClB,IAAI,CAACE,kBAAkB,CAAC,CAAC;IACzB,IAAI,CAACW,kBAAkB,GAAGC,WAAW,CAAC,MAAM;MACxC,IAAI,IAAI,CAACzE,SAAS,IAAI,IAAI,CAACO,YAAY,EAAE;QACrC,MAAMmD,WAAW,GAAG,IAAI,CAACW,cAAc,CAAC,CAAC;QACzC,IAAI,CAAC9D,YAAY,CAACmD,WAAW,EAAE,IAAI,CAACtD,QAAQ,CAAC;MACjD;IACJ,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;EACb;EAEAyD,kBAAkBA,CAAA,EAAG;IACjB,IAAI,IAAI,CAACW,kBAAkB,EAAE;MACzBE,aAAa,CAAC,IAAI,CAACF,kBAAkB,CAAC;MACtC,IAAI,CAACA,kBAAkB,GAAG,IAAI;IAClC;EACJ;;EAEA;EACAG,oBAAoBA,CAACC,aAAa,GAAG,IAAI,EAAE;IACvC,IAAI,CAAC,IAAI,CAAC7E,aAAa,EAAE;MACrB0B,OAAO,CAAC4B,IAAI,CAAC,2CAA2C,CAAC;MACzD,OAAO,IAAI;IACf;IAEA5B,OAAO,CAACC,GAAG,CAAC,oCAAoC,IAAI,CAAC3B,aAAa,CAACK,QAAQ,MAAM,IAAI,CAACL,aAAa,CAACmD,gBAAgB,WAAW,CAAC;IAEhI,MAAM2B,WAAW,GAAG,IAAI,CAAC9E,aAAa,CAAC+E,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC;IAC1D,MAAMC,eAAe,GAAGd,IAAI,CAACe,KAAK,CAACH,WAAW,CAACI,MAAM,GAAGL,aAAa,CAAC;IACtE,MAAMM,YAAY,GAAG,EAAE;IAEvBzD,OAAO,CAACC,GAAG,CAAC,wBAAwBmD,WAAW,CAACI,MAAM,wBAAwBF,eAAe,EAAE,CAAC;IAEhG,KAAK,IAAII,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGP,aAAa,EAAEO,CAAC,EAAE,EAAE;MACpC,MAAM1B,KAAK,GAAG0B,CAAC,GAAGJ,eAAe;MACjC,MAAMK,GAAG,GAAGnB,IAAI,CAACE,GAAG,CAACV,KAAK,GAAGsB,eAAe,EAAEF,WAAW,CAACI,MAAM,CAAC;MAEjE,IAAId,GAAG,GAAG,CAAC;MACX,IAAID,GAAG,GAAG,CAAC;MAEX,KAAK,IAAImB,CAAC,GAAG5B,KAAK,EAAE4B,CAAC,GAAGD,GAAG,EAAEC,CAAC,EAAE,EAAE;QAC9B,MAAMC,MAAM,GAAGT,WAAW,CAACQ,CAAC,CAAC;QAC7B,IAAIC,MAAM,GAAGpB,GAAG,EAAEA,GAAG,GAAGoB,MAAM;QAC9B,IAAIA,MAAM,GAAGnB,GAAG,EAAEA,GAAG,GAAGmB,MAAM;MAClC;;MAEA;MACAJ,YAAY,CAACK,IAAI,CAACtB,IAAI,CAACC,GAAG,CAACD,IAAI,CAACuB,GAAG,CAACrB,GAAG,CAAC,EAAEF,IAAI,CAACuB,GAAG,CAACtB,GAAG,CAAC,CAAC,CAAC;IAC7D;IAEAzC,OAAO,CAACC,GAAG,CAAC,4BAA4BwD,YAAY,CAACD,MAAM,uBAAuBhB,IAAI,CAACC,GAAG,CAAC,GAAGgB,YAAY,CAAC,EAAE,CAAC;IAC9G,OAAOA,YAAY;EACvB;;EAEA;EACAO,qBAAqBA,CAAA,EAAG;IACpB,IAAI,CAAC,IAAI,CAAC1F,aAAa,EAAE,OAAO,IAAI;IAEpC,MAAM8E,WAAW,GAAG,IAAI,CAAC9E,aAAa,CAAC+E,cAAc,CAAC,CAAC,CAAC;IACxD,IAAIY,GAAG,GAAG,CAAC;IACX,IAAIC,IAAI,GAAG,CAAC;IAEZ,KAAK,IAAIR,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGN,WAAW,CAACI,MAAM,EAAEE,CAAC,EAAE,EAAE;MACzC,MAAMG,MAAM,GAAGrB,IAAI,CAACuB,GAAG,CAACX,WAAW,CAACM,CAAC,CAAC,CAAC;MACvCO,GAAG,IAAIJ,MAAM,GAAGA,MAAM;MACtB,IAAIA,MAAM,GAAGK,IAAI,EAAEA,IAAI,GAAGL,MAAM;IACpC;IAEA,MAAMM,GAAG,GAAG3B,IAAI,CAAC4B,IAAI,CAACH,GAAG,GAAGb,WAAW,CAACI,MAAM,CAAC;IAC/C,MAAMa,MAAM,GAAG,EAAE,GAAG7B,IAAI,CAAC8B,KAAK,CAACJ,IAAI,CAAC;IACpC,MAAMK,KAAK,GAAG,EAAE,GAAG/B,IAAI,CAAC8B,KAAK,CAACH,GAAG,CAAC;IAElC,OAAO;MACHD,IAAI,EAAEA,IAAI;MACVG,MAAM,EAAEA,MAAM;MACdF,GAAG,EAAEA,GAAG;MACRI,KAAK,EAAEA,KAAK;MACZ5F,QAAQ,EAAE,IAAI,CAACA,QAAQ;MACvB2C,UAAU,EAAE,IAAI,CAAChD,aAAa,CAACgD,UAAU;MACzCkD,QAAQ,EAAE,IAAI,CAAClG,aAAa,CAACmD;IACjC,CAAC;EACL;;EAEA;EACAgD,OAAOA,CAAA,EAAG;IACN,IAAI,CAACjD,IAAI,CAAC,CAAC;IACX,IAAI,CAACY,kBAAkB,CAAC,CAAC;IAEzB,IAAI,IAAI,CAAChE,YAAY,IAAI,IAAI,CAACA,YAAY,CAAC0B,KAAK,KAAK,QAAQ,EAAE;MAC3D,IAAI,CAAC1B,YAAY,CAACsG,KAAK,CAAC,CAAC;IAC7B;IAEA,IAAI,CAACpG,aAAa,GAAG,IAAI;IACzB,IAAI,CAACO,QAAQ,GAAG,IAAI;IACpB,IAAI,CAACT,YAAY,GAAG,IAAI;EAC5B;AACJ;AAEA,eAAeF,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}