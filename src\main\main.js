const { app, BrowserWindow, ipcMain, dialog } = require('electron');
const path = require('path');
const isDev = require('electron-is-dev');
const NeedlesAudioAnalysis = require('./needlesAnalysis');

let mainWindow;
let audioAnalysis;

function createWindow() {
    // Create the browser window
    mainWindow = new BrowserWindow({
        width: 1400,
        height: 900,
        minWidth: 1200,
        minHeight: 700,
        webPreferences: {
            nodeIntegration: false,
            contextIsolation: true,
            enableRemoteModule: false,
            preload: path.join(__dirname, 'preload.js')
        },
        titleBarStyle: 'default',
        show: false,
        backgroundColor: '#1a1a1a'
    });

    // Load the app
    const startUrl = isDev ?
        'http://localhost:3000' :
        `file://${path.join(__dirname, '../build/index.html')}`;

    mainWindow.loadURL(startUrl);

    // Show window when ready to prevent visual flash
    mainWindow.once('ready-to-show', () => {
        mainWindow.show();

        // Open DevTools in development
        if (isDev) {
            mainWindow.webContents.openDevTools();
        }
    });

    mainWindow.on('closed', () => {
        mainWindow = null;
    });
}

// Initialize audio analysis engine
audioAnalysis = new NeedlesAudioAnalysis();

// App event listeners
app.whenReady().then(createWindow);

app.on('window-all-closed', () => {
    if (process.platform !== 'darwin') {
        app.quit();
    }
});

app.on('activate', () => {
    if (BrowserWindow.getAllWindows().length === 0) {
        createWindow();
    }
});

// IPC handlers for file operations
ipcMain.handle('open-file-dialog', async() => {
    const result = await dialog.showOpenDialog(mainWindow, {
        properties: ['openFile', 'multiSelections'],
        filters: [{
                name: 'Audio Files',
                extensions: ['wav', 'flac', 'mp3', 'aac', 'ogg', 'm4a', 'wma']
            },
            {
                name: 'All Files',
                extensions: ['*']
            }
        ]
    });

    return result;
});

// Handle file drops
ipcMain.handle('process-dropped-files', async(event, filePaths) => {
    // Filter for audio files
    const audioExtensions = ['.wav', '.flac', '.mp3', '.aac', '.ogg', '.m4a', '.wma'];
    const audioFiles = filePaths.filter(filePath => {
        const ext = path.extname(filePath).toLowerCase();
        return audioExtensions.includes(ext);
    });

    return audioFiles;
});

// Audio analysis IPC handlers
ipcMain.handle('analyze-audio-file', async(event, filePath) => {
    try {
        console.log(`Analyzing audio file: ${filePath}`);
        const analysis = await audioAnalysis.analyzeAudioFile(filePath);
        return analysis;
    } catch (error) {
        console.error('Audio analysis error:', error);
        throw error;
    }
});

ipcMain.handle('update-lufs-analysis', async(event, filePath, lufsData) => {
    try {
        const updatedAnalysis = audioAnalysis.updateAnalysisWithLUFS(filePath, lufsData);
        return updatedAnalysis;
    } catch (error) {
        console.error('LUFS update error:', error);
        throw error;
    }
});

ipcMain.handle('read-audio-file', async(event, filePath) => {
    try {
        const fs = require('fs');
        const buffer = fs.readFileSync(filePath);
        return Array.from(buffer); // Convert to array for JSON serialization
    } catch (error) {
        console.error('File read error:', error);
        throw error;
    }
});

ipcMain.handle('normalize-audio', async(event, audioData, targetLUFS, maxTruePeak) => {
    // This will be implemented when we add the normalization module
    return { normalizedData: audioData, gainApplied: 0 };
});