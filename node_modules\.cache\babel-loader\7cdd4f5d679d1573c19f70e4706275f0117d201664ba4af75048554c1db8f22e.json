{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\VSC Folder\\\\AlbumPlayer\\\\src\\\\App.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useCallback, useRef, useEffect } from 'react';\nimport styled from 'styled-components';\nimport WaveformPlayer from './components/WaveformPlayer';\nimport ControlPanel from './components/ControlPanel';\nimport FileDropZone from './components/FileDropZone';\nimport AudioAnalyzer from './audio/AudioAnalyzer';\nimport AudioEngine from './audio/AudioEngine';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AppContainer = styled.div`\n  display: flex;\n  flex-direction: column;\n  height: 100vh;\n  background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);\n  color: #ffffff;\n  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;\n`;\n_c = AppContainer;\nconst Header = styled.div`\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 20px 30px;\n  background: rgba(0, 0, 0, 0.3);\n  border-bottom: 1px solid rgba(255, 255, 255, 0.1);\n`;\n_c2 = Header;\nconst Title = styled.h1`\n  margin: 0;\n  font-size: 24px;\n  font-weight: 300;\n  color: #4CAF50;\n`;\n_c3 = Title;\nconst MainContent = styled.div`\n  display: flex;\n  flex: 1;\n  overflow: hidden;\n`;\n_c4 = MainContent;\nconst WaveformContainer = styled.div`\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  padding: 20px;\n  overflow-y: auto;\n  position: relative;\n`;\n_c5 = WaveformContainer;\nconst SidePanel = styled.div`\n  width: 350px;\n  background: rgba(0, 0, 0, 0.4);\n  border-left: 1px solid rgba(255, 255, 255, 0.1);\n  display: flex;\n  flex-direction: column;\n`;\n_c6 = SidePanel;\nconst EmptyState = styled.div`\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  height: 100%;\n  color: rgba(255, 255, 255, 0.6);\n  text-align: center;\n`;\n_c7 = EmptyState;\nconst EmptyStateIcon = styled.div`\n  font-size: 64px;\n  margin-bottom: 20px;\n  opacity: 0.3;\n`;\n_c8 = EmptyStateIcon;\nconst TracksContainer = styled.div`\n  position: relative;\n`;\n_c9 = TracksContainer;\nconst GlobalDropZone = styled.div`\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  z-index: 1000;\n  pointer-events: ${props => props.isDragActive ? 'auto' : 'none'};\n  background: ${props => props.isDragActive ? 'rgba(76, 175, 80, 0.1)' : 'transparent'};\n  border: 2px dashed ${props => props.isDragActive ? '#4CAF50' : 'transparent'};\n  border-radius: 12px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  transition: all 0.3s ease;\n  \n  h2 {\n    color: #4CAF50;\n    font-size: 24px;\n    text-shadow: 0 2px 4px rgba(0,0,0,0.3);\n    margin: 0;\n  }\n`;\n_c0 = GlobalDropZone;\nfunction App() {\n  _s();\n  const [audioFiles, setAudioFiles] = useState([]);\n  const [currentTrack, setCurrentTrack] = useState(null);\n  const [isPlaying, setIsPlaying] = useState(false);\n  const [masterVolume, setMasterVolume] = useState(0.8);\n  const [analysisData, setAnalysisData] = useState({});\n  const [currentTime, setCurrentTime] = useState(0);\n  const [duration, setDuration] = useState(0);\n  const [isDragActive, setIsDragActive] = useState(false);\n  const [isLoading, setIsLoading] = useState(false);\n  const audioAnalyzer = useRef(new AudioAnalyzer());\n  const audioEngine = useRef(new AudioEngine());\n  const lastClickTime = useRef(0);\n  const lastPlayPauseTime = useRef(0);\n\n  // State validation and recovery\n  const validateAndRecoverState = useCallback(() => {\n    if (!currentTrack) return;\n    const engine = audioEngine.current;\n    try {\n      // Check if audio engine state matches React state\n      const engineIsPlaying = engine.getIsPlaying();\n      const engineCurrentTime = engine.getCurrentTime();\n      const engineDuration = engine.getDuration();\n\n      // Sync playing state\n      if (engineIsPlaying !== isPlaying) {\n        console.log(`Syncing play state: engine=${engineIsPlaying}, react=${isPlaying}`);\n        setIsPlaying(engineIsPlaying);\n      }\n\n      // Sync time if there's a significant difference\n      if (Math.abs(engineCurrentTime - currentTime) > 1) {\n        console.log(`Syncing time: engine=${engineCurrentTime}, react=${currentTime}`);\n        setCurrentTime(engineCurrentTime);\n      }\n\n      // Sync duration\n      if (engineDuration > 0 && Math.abs(engineDuration - duration) > 1) {\n        console.log(`Syncing duration: engine=${engineDuration}, react=${duration}`);\n        setDuration(engineDuration);\n      }\n    } catch (error) {\n      console.error('Error in state validation:', error);\n    }\n  }, [currentTrack, isPlaying, currentTime, duration]);\n\n  // Periodic state validation (less frequent to avoid interference)\n  useEffect(() => {\n    const interval = setInterval(validateAndRecoverState, 5000);\n    return () => clearInterval(interval);\n  }, [validateAndRecoverState]);\n\n  // Set up audio engine event handlers\n  useEffect(() => {\n    const engine = audioEngine.current;\n    engine.onTimeUpdate = (time, dur) => {\n      // Validate the time values before setting state\n      if (typeof time === 'number' && !isNaN(time) && time >= 0) {\n        setCurrentTime(time);\n      }\n      if (typeof dur === 'number' && !isNaN(dur) && dur > 0) {\n        setDuration(dur);\n      }\n    };\n    engine.onEnded = () => {\n      console.log('Audio ended');\n      setIsPlaying(false);\n      setCurrentTime(0);\n    };\n    engine.onError = error => {\n      console.error('Audio engine error:', error);\n      setIsPlaying(false);\n      setIsLoading(false);\n    };\n    engine.onLoadStart = () => {\n      console.log('Audio load started');\n      setIsLoading(true);\n    };\n    engine.onLoadEnd = () => {\n      console.log('Audio load ended');\n      setIsLoading(false);\n    };\n\n    // Set initial volume\n    engine.setVolume(masterVolume);\n    return () => {\n      engine.dispose();\n    };\n  }, []);\n\n  // Update volume when masterVolume changes\n  useEffect(() => {\n    audioEngine.current.setVolume(masterVolume);\n  }, [masterVolume]);\n\n  // Add spacebar play/pause functionality\n  useEffect(() => {\n    const handleKeyDown = e => {\n      if (e.code === 'Space' && e.target.tagName !== 'INPUT' && e.target.tagName !== 'TEXTAREA') {\n        e.preventDefault();\n        if (currentTrack) {\n          if (isPlaying) {\n            audioEngine.current.pause();\n            setIsPlaying(false);\n          } else {\n            audioEngine.current.play();\n            setIsPlaying(true);\n          }\n        }\n      }\n    };\n    document.addEventListener('keydown', handleKeyDown);\n    return () => {\n      document.removeEventListener('keydown', handleKeyDown);\n    };\n  }, [currentTrack, isPlaying]);\n\n  // Global drag and drop handlers\n  useEffect(() => {\n    const handleDragEnter = e => {\n      e.preventDefault();\n      e.stopPropagation();\n      if (e.dataTransfer.items && e.dataTransfer.items.length > 0) {\n        setIsDragActive(true);\n      }\n    };\n    const handleDragLeave = e => {\n      e.preventDefault();\n      e.stopPropagation();\n      if (!e.currentTarget.contains(e.relatedTarget)) {\n        setIsDragActive(false);\n      }\n    };\n    const handleDragOver = e => {\n      e.preventDefault();\n      e.stopPropagation();\n    };\n    const handleDrop = async e => {\n      e.preventDefault();\n      e.stopPropagation();\n      setIsDragActive(false);\n      const files = [];\n      if (e.dataTransfer.files && e.dataTransfer.files.length > 0) {\n        for (let i = 0; i < e.dataTransfer.files.length; i++) {\n          const file = e.dataTransfer.files[i];\n          if (isAudioFile(file.name)) {\n            files.push(file);\n          }\n        }\n      }\n      if (files.length > 0) {\n        handleFilesAdded(files);\n      }\n    };\n    document.addEventListener('dragenter', handleDragEnter);\n    document.addEventListener('dragleave', handleDragLeave);\n    document.addEventListener('dragover', handleDragOver);\n    document.addEventListener('drop', handleDrop);\n    return () => {\n      document.removeEventListener('dragenter', handleDragEnter);\n      document.removeEventListener('dragleave', handleDragLeave);\n      document.removeEventListener('dragover', handleDragOver);\n      document.removeEventListener('drop', handleDrop);\n    };\n  }, []);\n  const isAudioFile = filename => {\n    const audioExtensions = ['.wav', '.flac', '.mp3', '.aac', '.ogg', '.m4a', '.wma'];\n    const ext = filename.toLowerCase().substring(filename.lastIndexOf('.'));\n    return audioExtensions.includes(ext);\n  };\n\n  // Handle file loading\n  const handleFilesAdded = useCallback(async files => {\n    const newFiles = [];\n    for (const file of files) {\n      try {\n        // Determine file name and path based on file type\n        let fileName, filePath;\n        if (file instanceof File) {\n          // Browser File object from drag & drop\n          fileName = file.name;\n          filePath = file; // Keep the File object for browser processing\n        } else if (typeof file === 'string') {\n          // File path string from Electron\n          fileName = file.split('/').pop().split('\\\\').pop(); // Handle both / and \\ separators\n          filePath = file;\n        } else {\n          // Fallback\n          fileName = 'Unknown File';\n          filePath = file;\n        }\n\n        // Analyze the audio file\n        const analysis = await audioAnalyzer.current.analyzeFile(filePath);\n\n        // Preload audio for seamless playback\n        try {\n          await audioEngine.current.preloadAudioFile(filePath);\n          console.log(`Preloaded: ${fileName}`);\n        } catch (error) {\n          console.warn(`Failed to preload ${fileName}:`, error);\n        }\n        const audioFile = {\n          id: Date.now() + Math.random(),\n          name: fileName,\n          path: filePath,\n          analysis,\n          color: generateRandomColor(),\n          waveformData: analysis.waveformData || null\n        };\n        newFiles.push(audioFile);\n\n        // Store analysis data\n        setAnalysisData(prev => ({\n          ...prev,\n          [audioFile.id]: analysis\n        }));\n      } catch (error) {\n        console.error('Error analyzing file:', error);\n\n        // Create a basic file entry even if analysis fails\n        let fileName;\n        if (file instanceof File) {\n          fileName = file.name;\n        } else if (typeof file === 'string') {\n          fileName = file.split('/').pop().split('\\\\').pop();\n        } else {\n          fileName = 'Unknown File';\n        }\n        const audioFile = {\n          id: Date.now() + Math.random(),\n          name: fileName,\n          path: file,\n          analysis: null,\n          color: generateRandomColor(),\n          waveformData: null\n        };\n        newFiles.push(audioFile);\n      }\n    }\n    setAudioFiles(prev => [...prev, ...newFiles]);\n\n    // Auto-select first track if none selected\n    if (!currentTrack && newFiles.length > 0) {\n      setCurrentTrack(newFiles[0]);\n    }\n  }, [currentTrack]);\n\n  // Generate random colors for waveforms\n  const generateRandomColor = () => {\n    const colors = ['#4CAF50', '#2196F3', '#FF9800', '#E91E63', '#9C27B0', '#00BCD4', '#FFEB3B', '#FF5722'];\n    return colors[Math.floor(Math.random() * colors.length)];\n  };\n\n  // Handle track selection\n  const handleTrackSelect = useCallback(async track => {\n    if (track.id === (currentTrack && currentTrack.id)) {\n      console.log('Track already selected');\n      return;\n    }\n    if (isLoading) {\n      console.log('Already loading a track, ignoring click');\n      return;\n    }\n    try {\n      console.log(`Loading track: ${track.name}`);\n      setIsLoading(true);\n\n      // Stop current playback\n      audioEngine.current.stop();\n      setIsPlaying(false);\n\n      // Load new track\n      await audioEngine.current.loadAudioFile(track.path);\n      setCurrentTrack(track);\n      setCurrentTime(0);\n      console.log(`Track loaded successfully: ${track.name}`);\n    } catch (error) {\n      console.error('Error loading track:', error);\n      // Reset state on error\n      setCurrentTrack(null);\n      setIsPlaying(false);\n      setCurrentTime(0);\n    } finally {\n      setIsLoading(false);\n    }\n  }, [currentTrack, isLoading]);\n\n  // Handle playback control\n  const handlePlayPause = useCallback(() => {\n    if (!currentTrack) {\n      console.log('No current track selected');\n      return;\n    }\n    if (isLoading) {\n      console.log('Track is loading, cannot play/pause');\n      return;\n    }\n\n    // Debounce rapid play/pause clicks\n    const now = Date.now();\n    if (now - lastPlayPauseTime.current < 200) {\n      console.log('Play/pause debounced');\n      return;\n    }\n    lastPlayPauseTime.current = now;\n    try {\n      const engine = audioEngine.current;\n      const currentEngineState = engine.getIsPlaying();\n      console.log(`Play/Pause clicked - React state: ${isPlaying}, Engine state: ${currentEngineState}`);\n\n      // Use the engine state as the source of truth\n      if (currentEngineState || isPlaying) {\n        console.log('Pausing playback');\n        engine.pause();\n        setIsPlaying(false);\n        console.log('Pause completed - Engine state:', engine.getIsPlaying());\n      } else {\n        console.log('Starting playback');\n        // Ensure the track is loaded before playing\n        if (engine.currentBuffer) {\n          engine.play();\n          setIsPlaying(true);\n          console.log('Play completed - Engine state:', engine.getIsPlaying());\n        } else {\n          console.log('Track not loaded, reloading...');\n          // Reload the track if it's not loaded\n          handleTrackSelect(currentTrack);\n        }\n      }\n    } catch (error) {\n      console.error('Error in play/pause:', error);\n      // Reset state on error\n      setIsPlaying(false);\n    }\n  }, [isPlaying, currentTrack, isLoading, handleTrackSelect]);\n\n  // Handle waveform click (seek)\n  const handleWaveformClick = useCallback(async (track, position) => {\n    if (!track || typeof position !== 'number' || position < 0 || position > 1) {\n      console.error('Invalid waveform click parameters');\n      return;\n    }\n\n    // Debounce rapid clicks\n    const now = Date.now();\n    if (now - lastClickTime.current < 100) {\n      console.log('Click debounced');\n      return;\n    }\n    lastClickTime.current = now;\n    console.log(`Waveform clicked: ${track.name} at position ${position.toFixed(3)}`);\n    try {\n      // If clicking on a different track, switch to it\n      if (track.id !== (currentTrack && currentTrack.id)) {\n        await handleTrackSelect(track);\n        // Wait for the track to load before seeking\n        await new Promise(resolve => setTimeout(resolve, 200));\n      }\n\n      // Ensure the audio engine is ready\n      const engine = audioEngine.current;\n      if (!engine.currentBuffer) {\n        console.warn('Audio not loaded, cannot seek');\n        return;\n      }\n\n      // Seek to position\n      const trackAnalysis = analysisData[track.id];\n      const trackDuration = trackAnalysis && trackAnalysis.duration || engine.getDuration() || 0;\n      if (trackDuration > 0) {\n        const seekTime = Math.max(0, Math.min(trackDuration, position * trackDuration));\n        console.log(`Seeking to: ${seekTime.toFixed(2)}s of ${trackDuration.toFixed(2)}s`);\n\n        // Perform the seek\n        engine.seek(seekTime);\n        setCurrentTime(seekTime);\n\n        // Start playing for seamless playback\n        if (!isPlaying) {\n          engine.play();\n          setIsPlaying(true);\n        }\n\n        // Validate state after a short delay\n        setTimeout(validateAndRecoverState, 100);\n      } else {\n        console.warn('No duration available for seeking');\n      }\n    } catch (error) {\n      console.error('Error in waveform click:', error);\n    }\n  }, [currentTrack, analysisData, handleTrackSelect, isPlaying]);\n\n  // Handle track deletion\n  const handleTrackDelete = useCallback(trackToDelete => {\n    console.log(`Deleting track: ${trackToDelete.name}`);\n\n    // Stop playback if deleting current track\n    if (currentTrack && currentTrack.id === trackToDelete.id) {\n      audioEngine.current.stop();\n      setIsPlaying(false);\n      setCurrentTrack(null);\n      setCurrentTime(0);\n      setDuration(0);\n    }\n\n    // Remove from audio files list\n    setAudioFiles(prev => prev.filter(track => track.id !== trackToDelete.id));\n\n    // Remove from analysis data\n    setAnalysisData(prev => {\n      const newData = {\n        ...prev\n      };\n      delete newData[trackToDelete.id];\n      return newData;\n    });\n  }, [currentTrack]);\n  return /*#__PURE__*/_jsxDEV(AppContainer, {\n    children: [/*#__PURE__*/_jsxDEV(Header, {\n      children: [/*#__PURE__*/_jsxDEV(Title, {\n        children: \" Album Player \"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 560,\n        columnNumber: 9\n      }, this), \" \", /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          fontSize: '14px',\n          opacity: 0.7\n        },\n        children: \"Spotify Normalization Analyzer \"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 561,\n        columnNumber: 40\n      }, this), \" \"]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 558,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(MainContent, {\n      children: [/*#__PURE__*/_jsxDEV(WaveformContainer, {\n        children: [\" \", audioFiles.length === 0 ? /*#__PURE__*/_jsxDEV(FileDropZone, {\n          onFilesAdded: handleFilesAdded,\n          hasFiles: false,\n          children: [/*#__PURE__*/_jsxDEV(EmptyState, {\n            children: [/*#__PURE__*/_jsxDEV(EmptyStateIcon, {\n              children: \" \\uD83C\\uDFB5 \"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 578,\n              columnNumber: 17\n            }, this), \" \", /*#__PURE__*/_jsxDEV(\"h2\", {\n              children: \" Drop Audio Files Here \"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 579,\n              columnNumber: 56\n            }, this), \" \", /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \" Drag and drop your audio files to start analyzing \"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 580,\n              columnNumber: 51\n            }, this), \" \", /*#__PURE__*/_jsxDEV(\"p\", {\n              style: {\n                fontSize: '12px',\n                opacity: 0.5\n              },\n              children: \"Supported: WAV, FLAC, MP3, AAC, OGG \"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 581,\n              columnNumber: 77\n            }, this), \" \"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 576,\n            columnNumber: 17\n          }, this), \" \"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 573,\n          columnNumber: 41\n        }, this) : /*#__PURE__*/_jsxDEV(TracksContainer, {\n          children: [\" \", audioFiles.map((track, index) => /*#__PURE__*/_jsxDEV(WaveformPlayer, {\n            track: track,\n            isActive: currentTrack && currentTrack.id === track.id,\n            isPlaying: isPlaying && currentTrack && currentTrack.id === track.id,\n            onTrackSelect: handleTrackSelect,\n            onWaveformClick: handleWaveformClick,\n            onTrackDelete: handleTrackDelete,\n            onPlayPause: handlePlayPause,\n            analysisData: analysisData[track.id],\n            currentTime: currentTrack && currentTrack.id === track.id ? currentTime : 0,\n            duration: currentTrack && currentTrack.id === track.id ? duration : analysisData[track.id] && analysisData[track.id].duration || 0\n          }, track.id, false, {\n            fileName: _jsxFileName,\n            lineNumber: 591,\n            columnNumber: 60\n          }, this)), \" \", /*#__PURE__*/_jsxDEV(GlobalDropZone, {\n            isDragActive: isDragActive,\n            children: [\" \", isDragActive && /*#__PURE__*/_jsxDEV(\"h2\", {\n              children: \" Drop to Add More Files \"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 607,\n              columnNumber: 41\n            }, this), \" \"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 605,\n            columnNumber: 23\n          }, this), \" \"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 589,\n          columnNumber: 19\n        }, this), \" \"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 571,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(SidePanel, {\n        children: [/*#__PURE__*/_jsxDEV(ControlPanel, {\n          currentTrack: currentTrack,\n          isPlaying: isPlaying,\n          masterVolume: masterVolume,\n          onPlayPause: handlePlayPause,\n          onVolumeChange: setMasterVolume,\n          onFilesAdded: handleFilesAdded,\n          analysisData: currentTrack ? analysisData[currentTrack.id] : null,\n          currentTime: currentTime,\n          duration: duration\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 616,\n          columnNumber: 17\n        }, this), \" \"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 614,\n        columnNumber: 17\n      }, this), \" \"]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 569,\n      columnNumber: 9\n    }, this), \" \"]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 556,\n    columnNumber: 14\n  }, this);\n}\n_s(App, \"jfiLHTZzZOD2CjkrAvwz6imU5vI=\");\n_c1 = App;\nexport default App;\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9, _c0, _c1;\n$RefreshReg$(_c, \"AppContainer\");\n$RefreshReg$(_c2, \"Header\");\n$RefreshReg$(_c3, \"Title\");\n$RefreshReg$(_c4, \"MainContent\");\n$RefreshReg$(_c5, \"WaveformContainer\");\n$RefreshReg$(_c6, \"SidePanel\");\n$RefreshReg$(_c7, \"EmptyState\");\n$RefreshReg$(_c8, \"EmptyStateIcon\");\n$RefreshReg$(_c9, \"TracksContainer\");\n$RefreshReg$(_c0, \"GlobalDropZone\");\n$RefreshReg$(_c1, \"App\");", "map": {"version": 3, "names": ["React", "useState", "useCallback", "useRef", "useEffect", "styled", "WaveformPlayer", "ControlPanel", "FileDropZone", "AudioAnalyzer", "AudioEngine", "jsxDEV", "_jsxDEV", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "div", "_c", "Header", "_c2", "Title", "h1", "_c3", "MainContent", "_c4", "WaveformContainer", "_c5", "SidePanel", "_c6", "EmptyState", "_c7", "EmptyStateIcon", "_c8", "TracksContainer", "_c9", "GlobalDropZone", "props", "isDragActive", "_c0", "App", "_s", "audioFiles", "setAudioFiles", "currentTrack", "setCurrentTrack", "isPlaying", "setIsPlaying", "masterVolume", "setMasterVolume", "analysisData", "setAnalysisData", "currentTime", "setCurrentTime", "duration", "setDuration", "setIsDragActive", "isLoading", "setIsLoading", "audioAnalyzer", "audioEngine", "lastClickTime", "lastPlayPauseTime", "validateAndRecoverState", "engine", "current", "engineIsPlaying", "getIsPlaying", "engineCurrentTime", "getCurrentTime", "engineDuration", "getDuration", "console", "log", "Math", "abs", "error", "interval", "setInterval", "clearInterval", "onTimeUpdate", "time", "dur", "isNaN", "onEnded", "onError", "onLoadStart", "onLoadEnd", "setVolume", "dispose", "handleKeyDown", "e", "code", "target", "tagName", "preventDefault", "pause", "play", "document", "addEventListener", "removeEventListener", "handleDragEnter", "stopPropagation", "dataTransfer", "items", "length", "handleDragLeave", "currentTarget", "contains", "relatedTarget", "handleDragOver", "handleDrop", "files", "i", "file", "isAudioFile", "name", "push", "handleFilesAdded", "filename", "audioExtensions", "ext", "toLowerCase", "substring", "lastIndexOf", "includes", "newFiles", "fileName", "filePath", "File", "split", "pop", "analysis", "analyzeFile", "preloadAudioFile", "warn", "audioFile", "id", "Date", "now", "random", "path", "color", "generateRandomColor", "waveformData", "prev", "colors", "floor", "handleTrackSelect", "track", "stop", "loadAudioFile", "handlePlayPause", "currentEngineState", "current<PERSON><PERSON><PERSON>", "handleWaveformClick", "position", "toFixed", "Promise", "resolve", "setTimeout", "trackAnalysis", "trackDuration", "seekTime", "max", "min", "seek", "handleTrackDelete", "trackToDelete", "filter", "newData", "children", "_jsxFileName", "lineNumber", "columnNumber", "style", "fontSize", "opacity", "onFilesAdded", "hasFiles", "map", "index", "isActive", "onTrackSelect", "onWaveformClick", "onTrackDelete", "onPlayPause", "onVolumeChange", "_c1", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/VSC Folder/AlbumPlayer/src/App.js"], "sourcesContent": ["import React, { useState, useCallback, useRef, useEffect } from 'react';\nimport styled from 'styled-components';\nimport WaveformPlayer from './components/WaveformPlayer';\nimport ControlPanel from './components/ControlPanel';\nimport FileDropZone from './components/FileDropZone';\nimport AudioAnalyzer from './audio/AudioAnalyzer';\nimport AudioEngine from './audio/AudioEngine';\n\nconst AppContainer = styled.div `\n  display: flex;\n  flex-direction: column;\n  height: 100vh;\n  background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);\n  color: #ffffff;\n  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;\n`;\n\nconst Header = styled.div `\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 20px 30px;\n  background: rgba(0, 0, 0, 0.3);\n  border-bottom: 1px solid rgba(255, 255, 255, 0.1);\n`;\n\nconst Title = styled.h1 `\n  margin: 0;\n  font-size: 24px;\n  font-weight: 300;\n  color: #4CAF50;\n`;\n\nconst MainContent = styled.div `\n  display: flex;\n  flex: 1;\n  overflow: hidden;\n`;\n\nconst WaveformContainer = styled.div `\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  padding: 20px;\n  overflow-y: auto;\n  position: relative;\n`;\n\nconst SidePanel = styled.div `\n  width: 350px;\n  background: rgba(0, 0, 0, 0.4);\n  border-left: 1px solid rgba(255, 255, 255, 0.1);\n  display: flex;\n  flex-direction: column;\n`;\n\nconst EmptyState = styled.div `\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  height: 100%;\n  color: rgba(255, 255, 255, 0.6);\n  text-align: center;\n`;\n\nconst EmptyStateIcon = styled.div `\n  font-size: 64px;\n  margin-bottom: 20px;\n  opacity: 0.3;\n`;\n\nconst TracksContainer = styled.div `\n  position: relative;\n`;\n\nconst GlobalDropZone = styled.div `\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  z-index: 1000;\n  pointer-events: ${props => props.isDragActive ? 'auto' : 'none'};\n  background: ${props => props.isDragActive ? 'rgba(76, 175, 80, 0.1)' : 'transparent'};\n  border: 2px dashed ${props => props.isDragActive ? '#4CAF50' : 'transparent'};\n  border-radius: 12px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  transition: all 0.3s ease;\n  \n  h2 {\n    color: #4CAF50;\n    font-size: 24px;\n    text-shadow: 0 2px 4px rgba(0,0,0,0.3);\n    margin: 0;\n  }\n`;\n\nfunction App() {\n    const [audioFiles, setAudioFiles] = useState([]);\n    const [currentTrack, setCurrentTrack] = useState(null);\n    const [isPlaying, setIsPlaying] = useState(false);\n    const [masterVolume, setMasterVolume] = useState(0.8);\n    const [analysisData, setAnalysisData] = useState({});\n    const [currentTime, setCurrentTime] = useState(0);\n    const [duration, setDuration] = useState(0);\n    const [isDragActive, setIsDragActive] = useState(false);\n    const [isLoading, setIsLoading] = useState(false);\n\n    const audioAnalyzer = useRef(new AudioAnalyzer());\n    const audioEngine = useRef(new AudioEngine());\n    const lastClickTime = useRef(0);\n    const lastPlayPauseTime = useRef(0);\n\n    // State validation and recovery\n    const validateAndRecoverState = useCallback(() => {\n        if (!currentTrack) return;\n\n        const engine = audioEngine.current;\n        try {\n            // Check if audio engine state matches React state\n            const engineIsPlaying = engine.getIsPlaying();\n            const engineCurrentTime = engine.getCurrentTime();\n            const engineDuration = engine.getDuration();\n\n            // Sync playing state\n            if (engineIsPlaying !== isPlaying) {\n                console.log(`Syncing play state: engine=${engineIsPlaying}, react=${isPlaying}`);\n                setIsPlaying(engineIsPlaying);\n            }\n\n            // Sync time if there's a significant difference\n            if (Math.abs(engineCurrentTime - currentTime) > 1) {\n                console.log(`Syncing time: engine=${engineCurrentTime}, react=${currentTime}`);\n                setCurrentTime(engineCurrentTime);\n            }\n\n            // Sync duration\n            if (engineDuration > 0 && Math.abs(engineDuration - duration) > 1) {\n                console.log(`Syncing duration: engine=${engineDuration}, react=${duration}`);\n                setDuration(engineDuration);\n            }\n        } catch (error) {\n            console.error('Error in state validation:', error);\n        }\n    }, [currentTrack, isPlaying, currentTime, duration]);\n\n    // Periodic state validation (less frequent to avoid interference)\n    useEffect(() => {\n        const interval = setInterval(validateAndRecoverState, 5000);\n        return () => clearInterval(interval);\n    }, [validateAndRecoverState]);\n\n    // Set up audio engine event handlers\n    useEffect(() => {\n        const engine = audioEngine.current;\n\n        engine.onTimeUpdate = (time, dur) => {\n            // Validate the time values before setting state\n            if (typeof time === 'number' && !isNaN(time) && time >= 0) {\n                setCurrentTime(time);\n            }\n            if (typeof dur === 'number' && !isNaN(dur) && dur > 0) {\n                setDuration(dur);\n            }\n        };\n\n        engine.onEnded = () => {\n            console.log('Audio ended');\n            setIsPlaying(false);\n            setCurrentTime(0);\n        };\n\n        engine.onError = (error) => {\n            console.error('Audio engine error:', error);\n            setIsPlaying(false);\n            setIsLoading(false);\n        };\n\n        engine.onLoadStart = () => {\n            console.log('Audio load started');\n            setIsLoading(true);\n        };\n\n        engine.onLoadEnd = () => {\n            console.log('Audio load ended');\n            setIsLoading(false);\n        };\n\n        // Set initial volume\n        engine.setVolume(masterVolume);\n\n        return () => {\n            engine.dispose();\n        };\n    }, []);\n\n    // Update volume when masterVolume changes\n    useEffect(() => {\n        audioEngine.current.setVolume(masterVolume);\n    }, [masterVolume]);\n\n    // Add spacebar play/pause functionality\n    useEffect(() => {\n        const handleKeyDown = (e) => {\n            if (e.code === 'Space' && e.target.tagName !== 'INPUT' && e.target.tagName !== 'TEXTAREA') {\n                e.preventDefault();\n                if (currentTrack) {\n                    if (isPlaying) {\n                        audioEngine.current.pause();\n                        setIsPlaying(false);\n                    } else {\n                        audioEngine.current.play();\n                        setIsPlaying(true);\n                    }\n                }\n            }\n        };\n\n        document.addEventListener('keydown', handleKeyDown);\n        return () => {\n            document.removeEventListener('keydown', handleKeyDown);\n        };\n    }, [currentTrack, isPlaying]);\n\n    // Global drag and drop handlers\n    useEffect(() => {\n        const handleDragEnter = (e) => {\n            e.preventDefault();\n            e.stopPropagation();\n            if (e.dataTransfer.items && e.dataTransfer.items.length > 0) {\n                setIsDragActive(true);\n            }\n        };\n\n        const handleDragLeave = (e) => {\n            e.preventDefault();\n            e.stopPropagation();\n            if (!e.currentTarget.contains(e.relatedTarget)) {\n                setIsDragActive(false);\n            }\n        };\n\n        const handleDragOver = (e) => {\n            e.preventDefault();\n            e.stopPropagation();\n        };\n\n        const handleDrop = async(e) => {\n            e.preventDefault();\n            e.stopPropagation();\n            setIsDragActive(false);\n\n            const files = [];\n            if (e.dataTransfer.files && e.dataTransfer.files.length > 0) {\n                for (let i = 0; i < e.dataTransfer.files.length; i++) {\n                    const file = e.dataTransfer.files[i];\n                    if (isAudioFile(file.name)) {\n                        files.push(file);\n                    }\n                }\n            }\n\n            if (files.length > 0) {\n                handleFilesAdded(files);\n            }\n        };\n\n        document.addEventListener('dragenter', handleDragEnter);\n        document.addEventListener('dragleave', handleDragLeave);\n        document.addEventListener('dragover', handleDragOver);\n        document.addEventListener('drop', handleDrop);\n\n        return () => {\n            document.removeEventListener('dragenter', handleDragEnter);\n            document.removeEventListener('dragleave', handleDragLeave);\n            document.removeEventListener('dragover', handleDragOver);\n            document.removeEventListener('drop', handleDrop);\n        };\n    }, []);\n\n    const isAudioFile = (filename) => {\n        const audioExtensions = ['.wav', '.flac', '.mp3', '.aac', '.ogg', '.m4a', '.wma'];\n        const ext = filename.toLowerCase().substring(filename.lastIndexOf('.'));\n        return audioExtensions.includes(ext);\n    };\n\n    // Handle file loading\n    const handleFilesAdded = useCallback(async(files) => {\n        const newFiles = [];\n\n        for (const file of files) {\n            try {\n                // Determine file name and path based on file type\n                let fileName, filePath;\n\n                if (file instanceof File) {\n                    // Browser File object from drag & drop\n                    fileName = file.name;\n                    filePath = file; // Keep the File object for browser processing\n                } else if (typeof file === 'string') {\n                    // File path string from Electron\n                    fileName = file.split('/').pop().split('\\\\').pop(); // Handle both / and \\ separators\n                    filePath = file;\n                } else {\n                    // Fallback\n                    fileName = 'Unknown File';\n                    filePath = file;\n                }\n\n                // Analyze the audio file\n                const analysis = await audioAnalyzer.current.analyzeFile(filePath);\n\n                // Preload audio for seamless playback\n                try {\n                    await audioEngine.current.preloadAudioFile(filePath);\n                    console.log(`Preloaded: ${fileName}`);\n                } catch (error) {\n                    console.warn(`Failed to preload ${fileName}:`, error);\n                }\n\n                const audioFile = {\n                    id: Date.now() + Math.random(),\n                    name: fileName,\n                    path: filePath,\n                    analysis,\n                    color: generateRandomColor(),\n                    waveformData: analysis.waveformData || null\n                };\n\n                newFiles.push(audioFile);\n\n                // Store analysis data\n                setAnalysisData(prev => ({\n                    ...prev,\n                    [audioFile.id]: analysis\n                }));\n\n            } catch (error) {\n                console.error('Error analyzing file:', error);\n\n                // Create a basic file entry even if analysis fails\n                let fileName;\n                if (file instanceof File) {\n                    fileName = file.name;\n                } else if (typeof file === 'string') {\n                    fileName = file.split('/').pop().split('\\\\').pop();\n                } else {\n                    fileName = 'Unknown File';\n                }\n\n                const audioFile = {\n                    id: Date.now() + Math.random(),\n                    name: fileName,\n                    path: file,\n                    analysis: null,\n                    color: generateRandomColor(),\n                    waveformData: null\n                };\n                newFiles.push(audioFile);\n            }\n        }\n\n        setAudioFiles(prev => [...prev, ...newFiles]);\n\n        // Auto-select first track if none selected\n        if (!currentTrack && newFiles.length > 0) {\n            setCurrentTrack(newFiles[0]);\n        }\n    }, [currentTrack]);\n\n    // Generate random colors for waveforms\n    const generateRandomColor = () => {\n        const colors = [\n            '#4CAF50', '#2196F3', '#FF9800', '#E91E63',\n            '#9C27B0', '#00BCD4', '#FFEB3B', '#FF5722'\n        ];\n        return colors[Math.floor(Math.random() * colors.length)];\n    };\n\n    // Handle track selection\n    const handleTrackSelect = useCallback(async(track) => {\n        if (track.id === (currentTrack && currentTrack.id)) {\n            console.log('Track already selected');\n            return;\n        }\n\n        if (isLoading) {\n            console.log('Already loading a track, ignoring click');\n            return;\n        }\n\n        try {\n            console.log(`Loading track: ${track.name}`);\n            setIsLoading(true);\n\n            // Stop current playback\n            audioEngine.current.stop();\n            setIsPlaying(false);\n\n            // Load new track\n            await audioEngine.current.loadAudioFile(track.path);\n            setCurrentTrack(track);\n            setCurrentTime(0);\n\n            console.log(`Track loaded successfully: ${track.name}`);\n        } catch (error) {\n            console.error('Error loading track:', error);\n            // Reset state on error\n            setCurrentTrack(null);\n            setIsPlaying(false);\n            setCurrentTime(0);\n        } finally {\n            setIsLoading(false);\n        }\n    }, [currentTrack, isLoading]);\n\n    // Handle playback control\n    const handlePlayPause = useCallback(() => {\n        if (!currentTrack) {\n            console.log('No current track selected');\n            return;\n        }\n\n        if (isLoading) {\n            console.log('Track is loading, cannot play/pause');\n            return;\n        }\n\n        // Debounce rapid play/pause clicks\n        const now = Date.now();\n        if (now - lastPlayPauseTime.current < 200) {\n            console.log('Play/pause debounced');\n            return;\n        }\n        lastPlayPauseTime.current = now;\n\n        try {\n            const engine = audioEngine.current;\n            const currentEngineState = engine.getIsPlaying();\n\n            console.log(`Play/Pause clicked - React state: ${isPlaying}, Engine state: ${currentEngineState}`);\n\n            // Use the engine state as the source of truth\n            if (currentEngineState || isPlaying) {\n                console.log('Pausing playback');\n                engine.pause();\n                setIsPlaying(false);\n                console.log('Pause completed - Engine state:', engine.getIsPlaying());\n            } else {\n                console.log('Starting playback');\n                // Ensure the track is loaded before playing\n                if (engine.currentBuffer) {\n                    engine.play();\n                    setIsPlaying(true);\n                    console.log('Play completed - Engine state:', engine.getIsPlaying());\n                } else {\n                    console.log('Track not loaded, reloading...');\n                    // Reload the track if it's not loaded\n                    handleTrackSelect(currentTrack);\n                }\n            }\n        } catch (error) {\n            console.error('Error in play/pause:', error);\n            // Reset state on error\n            setIsPlaying(false);\n        }\n    }, [isPlaying, currentTrack, isLoading, handleTrackSelect]);\n\n    // Handle waveform click (seek)\n    const handleWaveformClick = useCallback(async(track, position) => {\n        if (!track || typeof position !== 'number' || position < 0 || position > 1) {\n            console.error('Invalid waveform click parameters');\n            return;\n        }\n\n        // Debounce rapid clicks\n        const now = Date.now();\n        if (now - lastClickTime.current < 100) {\n            console.log('Click debounced');\n            return;\n        }\n        lastClickTime.current = now;\n\n        console.log(`Waveform clicked: ${track.name} at position ${position.toFixed(3)}`);\n\n        try {\n            // If clicking on a different track, switch to it\n            if (track.id !== (currentTrack && currentTrack.id)) {\n                await handleTrackSelect(track);\n                // Wait for the track to load before seeking\n                await new Promise(resolve => setTimeout(resolve, 200));\n            }\n\n            // Ensure the audio engine is ready\n            const engine = audioEngine.current;\n            if (!engine.currentBuffer) {\n                console.warn('Audio not loaded, cannot seek');\n                return;\n            }\n\n            // Seek to position\n            const trackAnalysis = analysisData[track.id];\n            const trackDuration = trackAnalysis && trackAnalysis.duration || engine.getDuration() || 0;\n\n            if (trackDuration > 0) {\n                const seekTime = Math.max(0, Math.min(trackDuration, position * trackDuration));\n                console.log(`Seeking to: ${seekTime.toFixed(2)}s of ${trackDuration.toFixed(2)}s`);\n\n                // Perform the seek\n                engine.seek(seekTime);\n                setCurrentTime(seekTime);\n\n                // Start playing for seamless playback\n                if (!isPlaying) {\n                    engine.play();\n                    setIsPlaying(true);\n                }\n\n                // Validate state after a short delay\n                setTimeout(validateAndRecoverState, 100);\n            } else {\n                console.warn('No duration available for seeking');\n            }\n        } catch (error) {\n            console.error('Error in waveform click:', error);\n        }\n    }, [currentTrack, analysisData, handleTrackSelect, isPlaying]);\n\n    // Handle track deletion\n    const handleTrackDelete = useCallback((trackToDelete) => {\n        console.log(`Deleting track: ${trackToDelete.name}`);\n\n        // Stop playback if deleting current track\n        if (currentTrack && currentTrack.id === trackToDelete.id) {\n            audioEngine.current.stop();\n            setIsPlaying(false);\n            setCurrentTrack(null);\n            setCurrentTime(0);\n            setDuration(0);\n        }\n\n        // Remove from audio files list\n        setAudioFiles(prev => prev.filter(track => track.id !== trackToDelete.id));\n\n        // Remove from analysis data\n        setAnalysisData(prev => {\n            const newData = {...prev };\n            delete newData[trackToDelete.id];\n            return newData;\n        });\n    }, [currentTrack]);\n\n    return ( <\n        AppContainer >\n        <\n        Header >\n        <\n        Title > Album Player < /Title> <\n        div style = {\n            { fontSize: '14px', opacity: 0.7 }\n        } >\n        Spotify Normalization Analyzer <\n        /div> < /\n        Header >\n\n        <\n        MainContent >\n        <\n        WaveformContainer > {\n            audioFiles.length === 0 ? ( <\n                FileDropZone onFilesAdded = { handleFilesAdded }\n                hasFiles = { false } >\n                <\n                EmptyState >\n                <\n                EmptyStateIcon > 🎵 < /EmptyStateIcon> <\n                h2 > Drop Audio Files Here < /h2> <\n                p > Drag and drop your audio files to start analyzing < /p> <\n                p style = {\n                    { fontSize: '12px', opacity: 0.5 }\n                } >\n                Supported: WAV, FLAC, MP3, AAC, OGG <\n                /p> < /\n                EmptyState > <\n                /FileDropZone>\n            ) : ( <\n                    TracksContainer > {\n                        audioFiles.map((track, index) => ( <\n                            WaveformPlayer key = { track.id }\n                            track = { track }\n                            isActive = { currentTrack && currentTrack.id === track.id }\n                            isPlaying = { isPlaying && currentTrack && currentTrack.id === track.id }\n                            onTrackSelect = { handleTrackSelect }\n                            onWaveformClick = { handleWaveformClick }\n                            onTrackDelete = { handleTrackDelete }\n                            onPlayPause = { handlePlayPause }\n                            analysisData = { analysisData[track.id] }\n                            currentTime = { currentTrack && currentTrack.id === track.id ? currentTime : 0 }\n                            duration = { currentTrack && currentTrack.id === track.id ? duration : (analysisData[track.id] && analysisData[track.id].duration || 0) }\n                            />\n                        ))\n                    } <\n                    GlobalDropZone isDragActive = { isDragActive } > {\n                        isDragActive && < h2 > Drop to Add More Files < /h2>} < /\n                        GlobalDropZone > <\n                        /TracksContainer>\n                    )\n                } <\n                /WaveformContainer>\n\n                <\n                SidePanel >\n                <\n                ControlPanel\n            currentTrack = { currentTrack }\n            isPlaying = { isPlaying }\n            masterVolume = { masterVolume }\n            onPlayPause = { handlePlayPause }\n            onVolumeChange = { setMasterVolume }\n            onFilesAdded = { handleFilesAdded }\n            analysisData = { currentTrack ? analysisData[currentTrack.id] : null }\n            currentTime = { currentTime }\n            duration = { duration }\n            /> < /\n            SidePanel > <\n            /MainContent> < /\n            AppContainer >\n        );\n    }\n\n    export default App;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,WAAW,EAAEC,MAAM,EAAEC,SAAS,QAAQ,OAAO;AACvE,OAAOC,MAAM,MAAM,mBAAmB;AACtC,OAAOC,cAAc,MAAM,6BAA6B;AACxD,OAAOC,YAAY,MAAM,2BAA2B;AACpD,OAAOC,YAAY,MAAM,2BAA2B;AACpD,OAAOC,aAAa,MAAM,uBAAuB;AACjD,OAAOC,WAAW,MAAM,qBAAqB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE9C,MAAMC,YAAY,GAAGR,MAAM,CAACS,GAAI;AAChC;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,EAAA,GAPIF,YAAY;AASlB,MAAMG,MAAM,GAAGX,MAAM,CAACS,GAAI;AAC1B;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACG,GAAA,GAPID,MAAM;AASZ,MAAME,KAAK,GAAGb,MAAM,CAACc,EAAG;AACxB;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GALIF,KAAK;AAOX,MAAMG,WAAW,GAAGhB,MAAM,CAACS,GAAI;AAC/B;AACA;AACA;AACA,CAAC;AAACQ,GAAA,GAJID,WAAW;AAMjB,MAAME,iBAAiB,GAAGlB,MAAM,CAACS,GAAI;AACrC;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACU,GAAA,GAPID,iBAAiB;AASvB,MAAME,SAAS,GAAGpB,MAAM,CAACS,GAAI;AAC7B;AACA;AACA;AACA;AACA;AACA,CAAC;AAACY,GAAA,GANID,SAAS;AAQf,MAAME,UAAU,GAAGtB,MAAM,CAACS,GAAI;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACc,GAAA,GARID,UAAU;AAUhB,MAAME,cAAc,GAAGxB,MAAM,CAACS,GAAI;AAClC;AACA;AACA;AACA,CAAC;AAACgB,GAAA,GAJID,cAAc;AAMpB,MAAME,eAAe,GAAG1B,MAAM,CAACS,GAAI;AACnC;AACA,CAAC;AAACkB,GAAA,GAFID,eAAe;AAIrB,MAAME,cAAc,GAAG5B,MAAM,CAACS,GAAI;AAClC;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoBoB,KAAK,IAAIA,KAAK,CAACC,YAAY,GAAG,MAAM,GAAG,MAAM;AACjE,gBAAgBD,KAAK,IAAIA,KAAK,CAACC,YAAY,GAAG,wBAAwB,GAAG,aAAa;AACtF,uBAAuBD,KAAK,IAAIA,KAAK,CAACC,YAAY,GAAG,SAAS,GAAG,aAAa;AAC9E;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GAtBIH,cAAc;AAwBpB,SAASI,GAAGA,CAAA,EAAG;EAAAC,EAAA;EACX,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGvC,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACwC,YAAY,EAAEC,eAAe,CAAC,GAAGzC,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAAC0C,SAAS,EAAEC,YAAY,CAAC,GAAG3C,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAAC4C,YAAY,EAAEC,eAAe,CAAC,GAAG7C,QAAQ,CAAC,GAAG,CAAC;EACrD,MAAM,CAAC8C,YAAY,EAAEC,eAAe,CAAC,GAAG/C,QAAQ,CAAC,CAAC,CAAC,CAAC;EACpD,MAAM,CAACgD,WAAW,EAAEC,cAAc,CAAC,GAAGjD,QAAQ,CAAC,CAAC,CAAC;EACjD,MAAM,CAACkD,QAAQ,EAAEC,WAAW,CAAC,GAAGnD,QAAQ,CAAC,CAAC,CAAC;EAC3C,MAAM,CAACkC,YAAY,EAAEkB,eAAe,CAAC,GAAGpD,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACqD,SAAS,EAAEC,YAAY,CAAC,GAAGtD,QAAQ,CAAC,KAAK,CAAC;EAEjD,MAAMuD,aAAa,GAAGrD,MAAM,CAAC,IAAIM,aAAa,CAAC,CAAC,CAAC;EACjD,MAAMgD,WAAW,GAAGtD,MAAM,CAAC,IAAIO,WAAW,CAAC,CAAC,CAAC;EAC7C,MAAMgD,aAAa,GAAGvD,MAAM,CAAC,CAAC,CAAC;EAC/B,MAAMwD,iBAAiB,GAAGxD,MAAM,CAAC,CAAC,CAAC;;EAEnC;EACA,MAAMyD,uBAAuB,GAAG1D,WAAW,CAAC,MAAM;IAC9C,IAAI,CAACuC,YAAY,EAAE;IAEnB,MAAMoB,MAAM,GAAGJ,WAAW,CAACK,OAAO;IAClC,IAAI;MACA;MACA,MAAMC,eAAe,GAAGF,MAAM,CAACG,YAAY,CAAC,CAAC;MAC7C,MAAMC,iBAAiB,GAAGJ,MAAM,CAACK,cAAc,CAAC,CAAC;MACjD,MAAMC,cAAc,GAAGN,MAAM,CAACO,WAAW,CAAC,CAAC;;MAE3C;MACA,IAAIL,eAAe,KAAKpB,SAAS,EAAE;QAC/B0B,OAAO,CAACC,GAAG,CAAC,8BAA8BP,eAAe,WAAWpB,SAAS,EAAE,CAAC;QAChFC,YAAY,CAACmB,eAAe,CAAC;MACjC;;MAEA;MACA,IAAIQ,IAAI,CAACC,GAAG,CAACP,iBAAiB,GAAGhB,WAAW,CAAC,GAAG,CAAC,EAAE;QAC/CoB,OAAO,CAACC,GAAG,CAAC,wBAAwBL,iBAAiB,WAAWhB,WAAW,EAAE,CAAC;QAC9EC,cAAc,CAACe,iBAAiB,CAAC;MACrC;;MAEA;MACA,IAAIE,cAAc,GAAG,CAAC,IAAII,IAAI,CAACC,GAAG,CAACL,cAAc,GAAGhB,QAAQ,CAAC,GAAG,CAAC,EAAE;QAC/DkB,OAAO,CAACC,GAAG,CAAC,4BAA4BH,cAAc,WAAWhB,QAAQ,EAAE,CAAC;QAC5EC,WAAW,CAACe,cAAc,CAAC;MAC/B;IACJ,CAAC,CAAC,OAAOM,KAAK,EAAE;MACZJ,OAAO,CAACI,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;IACtD;EACJ,CAAC,EAAE,CAAChC,YAAY,EAAEE,SAAS,EAAEM,WAAW,EAAEE,QAAQ,CAAC,CAAC;;EAEpD;EACA/C,SAAS,CAAC,MAAM;IACZ,MAAMsE,QAAQ,GAAGC,WAAW,CAACf,uBAAuB,EAAE,IAAI,CAAC;IAC3D,OAAO,MAAMgB,aAAa,CAACF,QAAQ,CAAC;EACxC,CAAC,EAAE,CAACd,uBAAuB,CAAC,CAAC;;EAE7B;EACAxD,SAAS,CAAC,MAAM;IACZ,MAAMyD,MAAM,GAAGJ,WAAW,CAACK,OAAO;IAElCD,MAAM,CAACgB,YAAY,GAAG,CAACC,IAAI,EAAEC,GAAG,KAAK;MACjC;MACA,IAAI,OAAOD,IAAI,KAAK,QAAQ,IAAI,CAACE,KAAK,CAACF,IAAI,CAAC,IAAIA,IAAI,IAAI,CAAC,EAAE;QACvD5B,cAAc,CAAC4B,IAAI,CAAC;MACxB;MACA,IAAI,OAAOC,GAAG,KAAK,QAAQ,IAAI,CAACC,KAAK,CAACD,GAAG,CAAC,IAAIA,GAAG,GAAG,CAAC,EAAE;QACnD3B,WAAW,CAAC2B,GAAG,CAAC;MACpB;IACJ,CAAC;IAEDlB,MAAM,CAACoB,OAAO,GAAG,MAAM;MACnBZ,OAAO,CAACC,GAAG,CAAC,aAAa,CAAC;MAC1B1B,YAAY,CAAC,KAAK,CAAC;MACnBM,cAAc,CAAC,CAAC,CAAC;IACrB,CAAC;IAEDW,MAAM,CAACqB,OAAO,GAAIT,KAAK,IAAK;MACxBJ,OAAO,CAACI,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAAC;MAC3C7B,YAAY,CAAC,KAAK,CAAC;MACnBW,YAAY,CAAC,KAAK,CAAC;IACvB,CAAC;IAEDM,MAAM,CAACsB,WAAW,GAAG,MAAM;MACvBd,OAAO,CAACC,GAAG,CAAC,oBAAoB,CAAC;MACjCf,YAAY,CAAC,IAAI,CAAC;IACtB,CAAC;IAEDM,MAAM,CAACuB,SAAS,GAAG,MAAM;MACrBf,OAAO,CAACC,GAAG,CAAC,kBAAkB,CAAC;MAC/Bf,YAAY,CAAC,KAAK,CAAC;IACvB,CAAC;;IAED;IACAM,MAAM,CAACwB,SAAS,CAACxC,YAAY,CAAC;IAE9B,OAAO,MAAM;MACTgB,MAAM,CAACyB,OAAO,CAAC,CAAC;IACpB,CAAC;EACL,CAAC,EAAE,EAAE,CAAC;;EAEN;EACAlF,SAAS,CAAC,MAAM;IACZqD,WAAW,CAACK,OAAO,CAACuB,SAAS,CAACxC,YAAY,CAAC;EAC/C,CAAC,EAAE,CAACA,YAAY,CAAC,CAAC;;EAElB;EACAzC,SAAS,CAAC,MAAM;IACZ,MAAMmF,aAAa,GAAIC,CAAC,IAAK;MACzB,IAAIA,CAAC,CAACC,IAAI,KAAK,OAAO,IAAID,CAAC,CAACE,MAAM,CAACC,OAAO,KAAK,OAAO,IAAIH,CAAC,CAACE,MAAM,CAACC,OAAO,KAAK,UAAU,EAAE;QACvFH,CAAC,CAACI,cAAc,CAAC,CAAC;QAClB,IAAInD,YAAY,EAAE;UACd,IAAIE,SAAS,EAAE;YACXc,WAAW,CAACK,OAAO,CAAC+B,KAAK,CAAC,CAAC;YAC3BjD,YAAY,CAAC,KAAK,CAAC;UACvB,CAAC,MAAM;YACHa,WAAW,CAACK,OAAO,CAACgC,IAAI,CAAC,CAAC;YAC1BlD,YAAY,CAAC,IAAI,CAAC;UACtB;QACJ;MACJ;IACJ,CAAC;IAEDmD,QAAQ,CAACC,gBAAgB,CAAC,SAAS,EAAET,aAAa,CAAC;IACnD,OAAO,MAAM;MACTQ,QAAQ,CAACE,mBAAmB,CAAC,SAAS,EAAEV,aAAa,CAAC;IAC1D,CAAC;EACL,CAAC,EAAE,CAAC9C,YAAY,EAAEE,SAAS,CAAC,CAAC;;EAE7B;EACAvC,SAAS,CAAC,MAAM;IACZ,MAAM8F,eAAe,GAAIV,CAAC,IAAK;MAC3BA,CAAC,CAACI,cAAc,CAAC,CAAC;MAClBJ,CAAC,CAACW,eAAe,CAAC,CAAC;MACnB,IAAIX,CAAC,CAACY,YAAY,CAACC,KAAK,IAAIb,CAAC,CAACY,YAAY,CAACC,KAAK,CAACC,MAAM,GAAG,CAAC,EAAE;QACzDjD,eAAe,CAAC,IAAI,CAAC;MACzB;IACJ,CAAC;IAED,MAAMkD,eAAe,GAAIf,CAAC,IAAK;MAC3BA,CAAC,CAACI,cAAc,CAAC,CAAC;MAClBJ,CAAC,CAACW,eAAe,CAAC,CAAC;MACnB,IAAI,CAACX,CAAC,CAACgB,aAAa,CAACC,QAAQ,CAACjB,CAAC,CAACkB,aAAa,CAAC,EAAE;QAC5CrD,eAAe,CAAC,KAAK,CAAC;MAC1B;IACJ,CAAC;IAED,MAAMsD,cAAc,GAAInB,CAAC,IAAK;MAC1BA,CAAC,CAACI,cAAc,CAAC,CAAC;MAClBJ,CAAC,CAACW,eAAe,CAAC,CAAC;IACvB,CAAC;IAED,MAAMS,UAAU,GAAG,MAAMpB,CAAC,IAAK;MAC3BA,CAAC,CAACI,cAAc,CAAC,CAAC;MAClBJ,CAAC,CAACW,eAAe,CAAC,CAAC;MACnB9C,eAAe,CAAC,KAAK,CAAC;MAEtB,MAAMwD,KAAK,GAAG,EAAE;MAChB,IAAIrB,CAAC,CAACY,YAAY,CAACS,KAAK,IAAIrB,CAAC,CAACY,YAAY,CAACS,KAAK,CAACP,MAAM,GAAG,CAAC,EAAE;QACzD,KAAK,IAAIQ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGtB,CAAC,CAACY,YAAY,CAACS,KAAK,CAACP,MAAM,EAAEQ,CAAC,EAAE,EAAE;UAClD,MAAMC,IAAI,GAAGvB,CAAC,CAACY,YAAY,CAACS,KAAK,CAACC,CAAC,CAAC;UACpC,IAAIE,WAAW,CAACD,IAAI,CAACE,IAAI,CAAC,EAAE;YACxBJ,KAAK,CAACK,IAAI,CAACH,IAAI,CAAC;UACpB;QACJ;MACJ;MAEA,IAAIF,KAAK,CAACP,MAAM,GAAG,CAAC,EAAE;QAClBa,gBAAgB,CAACN,KAAK,CAAC;MAC3B;IACJ,CAAC;IAEDd,QAAQ,CAACC,gBAAgB,CAAC,WAAW,EAAEE,eAAe,CAAC;IACvDH,QAAQ,CAACC,gBAAgB,CAAC,WAAW,EAAEO,eAAe,CAAC;IACvDR,QAAQ,CAACC,gBAAgB,CAAC,UAAU,EAAEW,cAAc,CAAC;IACrDZ,QAAQ,CAACC,gBAAgB,CAAC,MAAM,EAAEY,UAAU,CAAC;IAE7C,OAAO,MAAM;MACTb,QAAQ,CAACE,mBAAmB,CAAC,WAAW,EAAEC,eAAe,CAAC;MAC1DH,QAAQ,CAACE,mBAAmB,CAAC,WAAW,EAAEM,eAAe,CAAC;MAC1DR,QAAQ,CAACE,mBAAmB,CAAC,UAAU,EAAEU,cAAc,CAAC;MACxDZ,QAAQ,CAACE,mBAAmB,CAAC,MAAM,EAAEW,UAAU,CAAC;IACpD,CAAC;EACL,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMI,WAAW,GAAII,QAAQ,IAAK;IAC9B,MAAMC,eAAe,GAAG,CAAC,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC;IACjF,MAAMC,GAAG,GAAGF,QAAQ,CAACG,WAAW,CAAC,CAAC,CAACC,SAAS,CAACJ,QAAQ,CAACK,WAAW,CAAC,GAAG,CAAC,CAAC;IACvE,OAAOJ,eAAe,CAACK,QAAQ,CAACJ,GAAG,CAAC;EACxC,CAAC;;EAED;EACA,MAAMH,gBAAgB,GAAGjH,WAAW,CAAC,MAAM2G,KAAK,IAAK;IACjD,MAAMc,QAAQ,GAAG,EAAE;IAEnB,KAAK,MAAMZ,IAAI,IAAIF,KAAK,EAAE;MACtB,IAAI;QACA;QACA,IAAIe,QAAQ,EAAEC,QAAQ;QAEtB,IAAId,IAAI,YAAYe,IAAI,EAAE;UACtB;UACAF,QAAQ,GAAGb,IAAI,CAACE,IAAI;UACpBY,QAAQ,GAAGd,IAAI,CAAC,CAAC;QACrB,CAAC,MAAM,IAAI,OAAOA,IAAI,KAAK,QAAQ,EAAE;UACjC;UACAa,QAAQ,GAAGb,IAAI,CAACgB,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAAC,CAAC,CAACD,KAAK,CAAC,IAAI,CAAC,CAACC,GAAG,CAAC,CAAC,CAAC,CAAC;UACpDH,QAAQ,GAAGd,IAAI;QACnB,CAAC,MAAM;UACH;UACAa,QAAQ,GAAG,cAAc;UACzBC,QAAQ,GAAGd,IAAI;QACnB;;QAEA;QACA,MAAMkB,QAAQ,GAAG,MAAMzE,aAAa,CAACM,OAAO,CAACoE,WAAW,CAACL,QAAQ,CAAC;;QAElE;QACA,IAAI;UACA,MAAMpE,WAAW,CAACK,OAAO,CAACqE,gBAAgB,CAACN,QAAQ,CAAC;UACpDxD,OAAO,CAACC,GAAG,CAAC,cAAcsD,QAAQ,EAAE,CAAC;QACzC,CAAC,CAAC,OAAOnD,KAAK,EAAE;UACZJ,OAAO,CAAC+D,IAAI,CAAC,qBAAqBR,QAAQ,GAAG,EAAEnD,KAAK,CAAC;QACzD;QAEA,MAAM4D,SAAS,GAAG;UACdC,EAAE,EAAEC,IAAI,CAACC,GAAG,CAAC,CAAC,GAAGjE,IAAI,CAACkE,MAAM,CAAC,CAAC;UAC9BxB,IAAI,EAAEW,QAAQ;UACdc,IAAI,EAAEb,QAAQ;UACdI,QAAQ;UACRU,KAAK,EAAEC,mBAAmB,CAAC,CAAC;UAC5BC,YAAY,EAAEZ,QAAQ,CAACY,YAAY,IAAI;QAC3C,CAAC;QAEDlB,QAAQ,CAACT,IAAI,CAACmB,SAAS,CAAC;;QAExB;QACArF,eAAe,CAAC8F,IAAI,KAAK;UACrB,GAAGA,IAAI;UACP,CAACT,SAAS,CAACC,EAAE,GAAGL;QACpB,CAAC,CAAC,CAAC;MAEP,CAAC,CAAC,OAAOxD,KAAK,EAAE;QACZJ,OAAO,CAACI,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;;QAE7C;QACA,IAAImD,QAAQ;QACZ,IAAIb,IAAI,YAAYe,IAAI,EAAE;UACtBF,QAAQ,GAAGb,IAAI,CAACE,IAAI;QACxB,CAAC,MAAM,IAAI,OAAOF,IAAI,KAAK,QAAQ,EAAE;UACjCa,QAAQ,GAAGb,IAAI,CAACgB,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAAC,CAAC,CAACD,KAAK,CAAC,IAAI,CAAC,CAACC,GAAG,CAAC,CAAC;QACtD,CAAC,MAAM;UACHJ,QAAQ,GAAG,cAAc;QAC7B;QAEA,MAAMS,SAAS,GAAG;UACdC,EAAE,EAAEC,IAAI,CAACC,GAAG,CAAC,CAAC,GAAGjE,IAAI,CAACkE,MAAM,CAAC,CAAC;UAC9BxB,IAAI,EAAEW,QAAQ;UACdc,IAAI,EAAE3B,IAAI;UACVkB,QAAQ,EAAE,IAAI;UACdU,KAAK,EAAEC,mBAAmB,CAAC,CAAC;UAC5BC,YAAY,EAAE;QAClB,CAAC;QACDlB,QAAQ,CAACT,IAAI,CAACmB,SAAS,CAAC;MAC5B;IACJ;IAEA7F,aAAa,CAACsG,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAE,GAAGnB,QAAQ,CAAC,CAAC;;IAE7C;IACA,IAAI,CAAClF,YAAY,IAAIkF,QAAQ,CAACrB,MAAM,GAAG,CAAC,EAAE;MACtC5D,eAAe,CAACiF,QAAQ,CAAC,CAAC,CAAC,CAAC;IAChC;EACJ,CAAC,EAAE,CAAClF,YAAY,CAAC,CAAC;;EAElB;EACA,MAAMmG,mBAAmB,GAAGA,CAAA,KAAM;IAC9B,MAAMG,MAAM,GAAG,CACX,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAC1C,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,CAC7C;IACD,OAAOA,MAAM,CAACxE,IAAI,CAACyE,KAAK,CAACzE,IAAI,CAACkE,MAAM,CAAC,CAAC,GAAGM,MAAM,CAACzC,MAAM,CAAC,CAAC;EAC5D,CAAC;;EAED;EACA,MAAM2C,iBAAiB,GAAG/I,WAAW,CAAC,MAAMgJ,KAAK,IAAK;IAClD,IAAIA,KAAK,CAACZ,EAAE,MAAM7F,YAAY,IAAIA,YAAY,CAAC6F,EAAE,CAAC,EAAE;MAChDjE,OAAO,CAACC,GAAG,CAAC,wBAAwB,CAAC;MACrC;IACJ;IAEA,IAAIhB,SAAS,EAAE;MACXe,OAAO,CAACC,GAAG,CAAC,yCAAyC,CAAC;MACtD;IACJ;IAEA,IAAI;MACAD,OAAO,CAACC,GAAG,CAAC,kBAAkB4E,KAAK,CAACjC,IAAI,EAAE,CAAC;MAC3C1D,YAAY,CAAC,IAAI,CAAC;;MAElB;MACAE,WAAW,CAACK,OAAO,CAACqF,IAAI,CAAC,CAAC;MAC1BvG,YAAY,CAAC,KAAK,CAAC;;MAEnB;MACA,MAAMa,WAAW,CAACK,OAAO,CAACsF,aAAa,CAACF,KAAK,CAACR,IAAI,CAAC;MACnDhG,eAAe,CAACwG,KAAK,CAAC;MACtBhG,cAAc,CAAC,CAAC,CAAC;MAEjBmB,OAAO,CAACC,GAAG,CAAC,8BAA8B4E,KAAK,CAACjC,IAAI,EAAE,CAAC;IAC3D,CAAC,CAAC,OAAOxC,KAAK,EAAE;MACZJ,OAAO,CAACI,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC5C;MACA/B,eAAe,CAAC,IAAI,CAAC;MACrBE,YAAY,CAAC,KAAK,CAAC;MACnBM,cAAc,CAAC,CAAC,CAAC;IACrB,CAAC,SAAS;MACNK,YAAY,CAAC,KAAK,CAAC;IACvB;EACJ,CAAC,EAAE,CAACd,YAAY,EAAEa,SAAS,CAAC,CAAC;;EAE7B;EACA,MAAM+F,eAAe,GAAGnJ,WAAW,CAAC,MAAM;IACtC,IAAI,CAACuC,YAAY,EAAE;MACf4B,OAAO,CAACC,GAAG,CAAC,2BAA2B,CAAC;MACxC;IACJ;IAEA,IAAIhB,SAAS,EAAE;MACXe,OAAO,CAACC,GAAG,CAAC,qCAAqC,CAAC;MAClD;IACJ;;IAEA;IACA,MAAMkE,GAAG,GAAGD,IAAI,CAACC,GAAG,CAAC,CAAC;IACtB,IAAIA,GAAG,GAAG7E,iBAAiB,CAACG,OAAO,GAAG,GAAG,EAAE;MACvCO,OAAO,CAACC,GAAG,CAAC,sBAAsB,CAAC;MACnC;IACJ;IACAX,iBAAiB,CAACG,OAAO,GAAG0E,GAAG;IAE/B,IAAI;MACA,MAAM3E,MAAM,GAAGJ,WAAW,CAACK,OAAO;MAClC,MAAMwF,kBAAkB,GAAGzF,MAAM,CAACG,YAAY,CAAC,CAAC;MAEhDK,OAAO,CAACC,GAAG,CAAC,qCAAqC3B,SAAS,mBAAmB2G,kBAAkB,EAAE,CAAC;;MAElG;MACA,IAAIA,kBAAkB,IAAI3G,SAAS,EAAE;QACjC0B,OAAO,CAACC,GAAG,CAAC,kBAAkB,CAAC;QAC/BT,MAAM,CAACgC,KAAK,CAAC,CAAC;QACdjD,YAAY,CAAC,KAAK,CAAC;QACnByB,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAET,MAAM,CAACG,YAAY,CAAC,CAAC,CAAC;MACzE,CAAC,MAAM;QACHK,OAAO,CAACC,GAAG,CAAC,mBAAmB,CAAC;QAChC;QACA,IAAIT,MAAM,CAAC0F,aAAa,EAAE;UACtB1F,MAAM,CAACiC,IAAI,CAAC,CAAC;UACblD,YAAY,CAAC,IAAI,CAAC;UAClByB,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAET,MAAM,CAACG,YAAY,CAAC,CAAC,CAAC;QACxE,CAAC,MAAM;UACHK,OAAO,CAACC,GAAG,CAAC,gCAAgC,CAAC;UAC7C;UACA2E,iBAAiB,CAACxG,YAAY,CAAC;QACnC;MACJ;IACJ,CAAC,CAAC,OAAOgC,KAAK,EAAE;MACZJ,OAAO,CAACI,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC5C;MACA7B,YAAY,CAAC,KAAK,CAAC;IACvB;EACJ,CAAC,EAAE,CAACD,SAAS,EAAEF,YAAY,EAAEa,SAAS,EAAE2F,iBAAiB,CAAC,CAAC;;EAE3D;EACA,MAAMO,mBAAmB,GAAGtJ,WAAW,CAAC,OAAMgJ,KAAK,EAAEO,QAAQ,KAAK;IAC9D,IAAI,CAACP,KAAK,IAAI,OAAOO,QAAQ,KAAK,QAAQ,IAAIA,QAAQ,GAAG,CAAC,IAAIA,QAAQ,GAAG,CAAC,EAAE;MACxEpF,OAAO,CAACI,KAAK,CAAC,mCAAmC,CAAC;MAClD;IACJ;;IAEA;IACA,MAAM+D,GAAG,GAAGD,IAAI,CAACC,GAAG,CAAC,CAAC;IACtB,IAAIA,GAAG,GAAG9E,aAAa,CAACI,OAAO,GAAG,GAAG,EAAE;MACnCO,OAAO,CAACC,GAAG,CAAC,iBAAiB,CAAC;MAC9B;IACJ;IACAZ,aAAa,CAACI,OAAO,GAAG0E,GAAG;IAE3BnE,OAAO,CAACC,GAAG,CAAC,qBAAqB4E,KAAK,CAACjC,IAAI,gBAAgBwC,QAAQ,CAACC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC;IAEjF,IAAI;MACA;MACA,IAAIR,KAAK,CAACZ,EAAE,MAAM7F,YAAY,IAAIA,YAAY,CAAC6F,EAAE,CAAC,EAAE;QAChD,MAAMW,iBAAiB,CAACC,KAAK,CAAC;QAC9B;QACA,MAAM,IAAIS,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,GAAG,CAAC,CAAC;MAC1D;;MAEA;MACA,MAAM/F,MAAM,GAAGJ,WAAW,CAACK,OAAO;MAClC,IAAI,CAACD,MAAM,CAAC0F,aAAa,EAAE;QACvBlF,OAAO,CAAC+D,IAAI,CAAC,+BAA+B,CAAC;QAC7C;MACJ;;MAEA;MACA,MAAM0B,aAAa,GAAG/G,YAAY,CAACmG,KAAK,CAACZ,EAAE,CAAC;MAC5C,MAAMyB,aAAa,GAAGD,aAAa,IAAIA,aAAa,CAAC3G,QAAQ,IAAIU,MAAM,CAACO,WAAW,CAAC,CAAC,IAAI,CAAC;MAE1F,IAAI2F,aAAa,GAAG,CAAC,EAAE;QACnB,MAAMC,QAAQ,GAAGzF,IAAI,CAAC0F,GAAG,CAAC,CAAC,EAAE1F,IAAI,CAAC2F,GAAG,CAACH,aAAa,EAAEN,QAAQ,GAAGM,aAAa,CAAC,CAAC;QAC/E1F,OAAO,CAACC,GAAG,CAAC,eAAe0F,QAAQ,CAACN,OAAO,CAAC,CAAC,CAAC,QAAQK,aAAa,CAACL,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC;;QAElF;QACA7F,MAAM,CAACsG,IAAI,CAACH,QAAQ,CAAC;QACrB9G,cAAc,CAAC8G,QAAQ,CAAC;;QAExB;QACA,IAAI,CAACrH,SAAS,EAAE;UACZkB,MAAM,CAACiC,IAAI,CAAC,CAAC;UACblD,YAAY,CAAC,IAAI,CAAC;QACtB;;QAEA;QACAiH,UAAU,CAACjG,uBAAuB,EAAE,GAAG,CAAC;MAC5C,CAAC,MAAM;QACHS,OAAO,CAAC+D,IAAI,CAAC,mCAAmC,CAAC;MACrD;IACJ,CAAC,CAAC,OAAO3D,KAAK,EAAE;MACZJ,OAAO,CAACI,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;IACpD;EACJ,CAAC,EAAE,CAAChC,YAAY,EAAEM,YAAY,EAAEkG,iBAAiB,EAAEtG,SAAS,CAAC,CAAC;;EAE9D;EACA,MAAMyH,iBAAiB,GAAGlK,WAAW,CAAEmK,aAAa,IAAK;IACrDhG,OAAO,CAACC,GAAG,CAAC,mBAAmB+F,aAAa,CAACpD,IAAI,EAAE,CAAC;;IAEpD;IACA,IAAIxE,YAAY,IAAIA,YAAY,CAAC6F,EAAE,KAAK+B,aAAa,CAAC/B,EAAE,EAAE;MACtD7E,WAAW,CAACK,OAAO,CAACqF,IAAI,CAAC,CAAC;MAC1BvG,YAAY,CAAC,KAAK,CAAC;MACnBF,eAAe,CAAC,IAAI,CAAC;MACrBQ,cAAc,CAAC,CAAC,CAAC;MACjBE,WAAW,CAAC,CAAC,CAAC;IAClB;;IAEA;IACAZ,aAAa,CAACsG,IAAI,IAAIA,IAAI,CAACwB,MAAM,CAACpB,KAAK,IAAIA,KAAK,CAACZ,EAAE,KAAK+B,aAAa,CAAC/B,EAAE,CAAC,CAAC;;IAE1E;IACAtF,eAAe,CAAC8F,IAAI,IAAI;MACpB,MAAMyB,OAAO,GAAG;QAAC,GAAGzB;MAAK,CAAC;MAC1B,OAAOyB,OAAO,CAACF,aAAa,CAAC/B,EAAE,CAAC;MAChC,OAAOiC,OAAO;IAClB,CAAC,CAAC;EACN,CAAC,EAAE,CAAC9H,YAAY,CAAC,CAAC;EAElB,oBAAS7B,OAAA,CACLC,YAAY;IAAA2J,QAAA,gBACZ5J,OAAA,CACAI,MAAM;MAAAwJ,QAAA,gBACN5J,OAAA,CACAM,KAAK;QAAAsJ,QAAA,EAAE;MAAc;QAAA5C,QAAA,EAAA6C,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,KAAC,eAAA/J,OAAA;QAC3BgK,KAAK,EACL;UAAEC,QAAQ,EAAE,MAAM;UAAEC,OAAO,EAAE;QAAI,CACpC;QAAAN,QAAA,EAAE;MAC4B;QAAA5C,QAAA,EAAA6C,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3B,CAAC,KAAC;IAAA;MAAA/C,QAAA,EAAA6C,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAER/J,OAAA,CACAS,WAAW;MAAAmJ,QAAA,gBACX5J,OAAA,CACAW,iBAAiB;QAAAiJ,QAAA,GAAE,GAAC,EAChBjI,UAAU,CAAC+D,MAAM,KAAK,CAAC,gBAAK1F,OAAA,CACxBJ,YAAY;UAACuK,YAAY,EAAK5D,gBAAkB;UAChD6D,QAAQ,EAAK,KAAO;UAAAR,QAAA,gBACpB5J,OAAA,CACAe,UAAU;YAAA6I,QAAA,gBACV5J,OAAA,CACAiB,cAAc;cAAA2I,QAAA,EAAE;YAAI;cAAA5C,QAAA,EAAA6C,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAiB,CAAC,KAAC,eAAA/J,OAAA;cAAA4J,QAAA,EACnC;YAAuB;cAAA5C,QAAA,EAAA6C,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,KAAC,eAAA/J,OAAA;cAAA4J,QAAA,EAC/B;YAAmD;cAAA5C,QAAA,EAAA6C,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,KAAC,eAAA/J,OAAA;cAC1DgK,KAAK,EACH;gBAAEC,QAAQ,EAAE,MAAM;gBAAEC,OAAO,EAAE;cAAI,CACpC;cAAAN,QAAA,EAAE;YACiC;cAAA5C,QAAA,EAAA6C,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClC,CAAC,KAAC;UAAA;YAAA/C,QAAA,EAAA6C,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACO,CAAC,KAAC;QAAA;UAAA/C,QAAA,EAAA6C,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,gBACZ/J,OAAA,CACEmB,eAAe;UAAAyI,QAAA,GAAE,GAAC,EACdjI,UAAU,CAAC0I,GAAG,CAAC,CAAC/B,KAAK,EAAEgC,KAAK,kBAAOtK,OAAA,CAC/BN,cAAc;YACd4I,KAAK,EAAKA,KAAO;YACjBiC,QAAQ,EAAK1I,YAAY,IAAIA,YAAY,CAAC6F,EAAE,KAAKY,KAAK,CAACZ,EAAI;YAC3D3F,SAAS,EAAKA,SAAS,IAAIF,YAAY,IAAIA,YAAY,CAAC6F,EAAE,KAAKY,KAAK,CAACZ,EAAI;YACzE8C,aAAa,EAAKnC,iBAAmB;YACrCoC,eAAe,EAAK7B,mBAAqB;YACzC8B,aAAa,EAAKlB,iBAAmB;YACrCmB,WAAW,EAAKlC,eAAiB;YACjCtG,YAAY,EAAKA,YAAY,CAACmG,KAAK,CAACZ,EAAE,CAAG;YACzCrF,WAAW,EAAKR,YAAY,IAAIA,YAAY,CAAC6F,EAAE,KAAKY,KAAK,CAACZ,EAAE,GAAGrF,WAAW,GAAG,CAAG;YAChFE,QAAQ,EAAKV,YAAY,IAAIA,YAAY,CAAC6F,EAAE,KAAKY,KAAK,CAACZ,EAAE,GAAGnF,QAAQ,GAAIJ,YAAY,CAACmG,KAAK,CAACZ,EAAE,CAAC,IAAIvF,YAAY,CAACmG,KAAK,CAACZ,EAAE,CAAC,CAACnF,QAAQ,IAAI;UAAI,GAVlH+F,KAAK,CAACZ,EAAE;YAAAV,QAAA,EAAA6C,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAW9B,CACJ,CAAC,EACL,GAAC,eAAA/J,OAAA,CACFqB,cAAc;YAACE,YAAY,EAAKA,YAAc;YAAAqI,QAAA,GAAE,GAAC,EAC7CrI,YAAY,iBAAIvB,OAAA;cAAA4J,QAAA,EAAM;YAAwB;cAAA5C,QAAA,EAAA6C,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,EAAC,GAAC;UAAA;YAAA/C,QAAA,EAAA6C,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvC,CAAC,KAAC;QAAA;UAAA/C,QAAA,EAAA6C,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CACnB,EACJ,GAAC;MAAA;QAAA/C,QAAA,EAAA6C,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACgB,CAAC,eAEnB/J,OAAA,CACAa,SAAS;QAAA+I,QAAA,gBACT5J,OAAA,CACAL,YAAY;UAChBkC,YAAY,EAAKA,YAAc;UAC/BE,SAAS,EAAKA,SAAW;UACzBE,YAAY,EAAKA,YAAc;UAC/B0I,WAAW,EAAKlC,eAAiB;UACjCmC,cAAc,EAAK1I,eAAiB;UACpCiI,YAAY,EAAK5D,gBAAkB;UACnCpE,YAAY,EAAKN,YAAY,GAAGM,YAAY,CAACN,YAAY,CAAC6F,EAAE,CAAC,GAAG,IAAM;UACtErF,WAAW,EAAKA,WAAa;UAC7BE,QAAQ,EAAKA;QAAU;UAAAyE,QAAA,EAAA6C,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtB,CAAC,KAAC;MAAA;QAAA/C,QAAA,EAAA6C,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACO,CAAC,KAAC;IAAA;MAAA/C,QAAA,EAAA6C,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,KAAC;EAAA;IAAA/C,QAAA,EAAA6C,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACD,CAAC;AAEtB;AAACrI,EAAA,CAnhBID,GAAG;AAAAoJ,GAAA,GAAHpJ,GAAG;AAqhBR,eAAeA,GAAG;AAAC,IAAAtB,EAAA,EAAAE,GAAA,EAAAG,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAI,GAAA,EAAAqJ,GAAA;AAAAC,YAAA,CAAA3K,EAAA;AAAA2K,YAAA,CAAAzK,GAAA;AAAAyK,YAAA,CAAAtK,GAAA;AAAAsK,YAAA,CAAApK,GAAA;AAAAoK,YAAA,CAAAlK,GAAA;AAAAkK,YAAA,CAAAhK,GAAA;AAAAgK,YAAA,CAAA9J,GAAA;AAAA8J,YAAA,CAAA5J,GAAA;AAAA4J,YAAA,CAAA1J,GAAA;AAAA0J,YAAA,CAAAtJ,GAAA;AAAAsJ,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}