{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\VSC Folder\\\\AlbumPlayer\\\\src\\\\App.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useCallback, useRef, useEffect } from 'react';\nimport styled from 'styled-components';\nimport WaveformPlayer from './components/WaveformPlayer';\nimport ControlPanel from './components/ControlPanel';\nimport FileDropZone from './components/FileDropZone';\nimport AudioAnalyzer from './audio/AudioAnalyzer';\nimport AudioEngine from './audio/AudioEngine';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AppContainer = styled.div`\n  display: flex;\n  flex-direction: column;\n  height: 100vh;\n  background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);\n  color: #ffffff;\n  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;\n`;\n_c = AppContainer;\nconst Header = styled.div`\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 20px 30px;\n  background: rgba(0, 0, 0, 0.3);\n  border-bottom: 1px solid rgba(255, 255, 255, 0.1);\n`;\n_c2 = Header;\nconst Title = styled.h1`\n  margin: 0;\n  font-size: 24px;\n  font-weight: 300;\n  color: #4CAF50;\n`;\n_c3 = Title;\nconst MainContent = styled.div`\n  display: flex;\n  flex: 1;\n  overflow: hidden;\n`;\n_c4 = MainContent;\nconst WaveformContainer = styled.div`\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  padding: 20px;\n  overflow-y: auto;\n  position: relative;\n`;\n_c5 = WaveformContainer;\nconst SidePanel = styled.div`\n  width: 350px;\n  background: rgba(0, 0, 0, 0.4);\n  border-left: 1px solid rgba(255, 255, 255, 0.1);\n  display: flex;\n  flex-direction: column;\n`;\n_c6 = SidePanel;\nconst EmptyState = styled.div`\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  height: 100%;\n  color: rgba(255, 255, 255, 0.6);\n  text-align: center;\n`;\n_c7 = EmptyState;\nconst EmptyStateIcon = styled.div`\n  font-size: 64px;\n  margin-bottom: 20px;\n  opacity: 0.3;\n`;\n_c8 = EmptyStateIcon;\nconst TracksContainer = styled.div`\n  position: relative;\n`;\n_c9 = TracksContainer;\nconst GlobalDropZone = styled.div`\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  z-index: 1000;\n  pointer-events: ${props => props.isDragActive ? 'auto' : 'none'};\n  background: ${props => props.isDragActive ? 'rgba(76, 175, 80, 0.1)' : 'transparent'};\n  border: 2px dashed ${props => props.isDragActive ? '#4CAF50' : 'transparent'};\n  border-radius: 12px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  transition: all 0.3s ease;\n  \n  h2 {\n    color: #4CAF50;\n    font-size: 24px;\n    text-shadow: 0 2px 4px rgba(0,0,0,0.3);\n    margin: 0;\n  }\n`;\n_c0 = GlobalDropZone;\nfunction App() {\n  _s();\n  const [audioFiles, setAudioFiles] = useState([]);\n  const [currentTrack, setCurrentTrack] = useState(null);\n  const [isPlaying, setIsPlaying] = useState(false);\n  const [masterVolume, setMasterVolume] = useState(0.8);\n  const [analysisData, setAnalysisData] = useState({});\n  const [currentTime, setCurrentTime] = useState(0);\n  const [duration, setDuration] = useState(0);\n  const [isDragActive, setIsDragActive] = useState(false);\n  const [isLoading, setIsLoading] = useState(false);\n  const [error, setError] = useState(null);\n  const [loadingMessage, setLoadingMessage] = useState('');\n  const audioAnalyzer = useRef(new AudioAnalyzer());\n  const audioEngine = useRef(new AudioEngine());\n  const lastClickTime = useRef(0);\n  const lastPlayPauseTime = useRef(0);\n\n  // REMOVED: validateAndRecoverState function - was causing performance issues\n  // Audio engine event handlers below provide proper state synchronization\n\n  // REMOVED: Performance-killing validation loop\n  // The audio engine event handlers below provide proper state sync\n\n  // Set up audio engine event handlers\n  useEffect(() => {\n    const engine = audioEngine.current;\n    engine.onTimeUpdate = (time, dur) => {\n      // Validate the time values before setting state\n      if (typeof time === 'number' && !isNaN(time) && time >= 0) {\n        setCurrentTime(time);\n      }\n      if (typeof dur === 'number' && !isNaN(dur) && dur > 0) {\n        setDuration(dur);\n      }\n    };\n    engine.onEnded = () => {\n      console.log('Audio ended');\n      setIsPlaying(false);\n      setCurrentTime(0);\n    };\n    engine.onError = error => {\n      console.error('Audio engine error:', error);\n      setIsPlaying(false);\n      setIsLoading(false);\n      setError(`Audio error: ${error.message || 'Unknown error'}`);\n      setLoadingMessage('');\n    };\n    engine.onLoadStart = () => {\n      console.log('Audio load started');\n      setIsLoading(true);\n      setLoadingMessage('Loading audio file...');\n      setError(null);\n    };\n    engine.onLoadEnd = () => {\n      console.log('Audio load ended');\n      setIsLoading(false);\n      setLoadingMessage('');\n    };\n\n    // Set initial volume\n    engine.setVolume(masterVolume);\n    return () => {\n      engine.dispose();\n    };\n  }, []);\n\n  // Update volume when masterVolume changes\n  useEffect(() => {\n    audioEngine.current.setVolume(masterVolume);\n  }, [masterVolume]);\n\n  // Add spacebar play/pause functionality\n  useEffect(() => {\n    const handleKeyDown = e => {\n      if (e.code === 'Space' && e.target.tagName !== 'INPUT' && e.target.tagName !== 'TEXTAREA') {\n        e.preventDefault();\n        if (currentTrack) {\n          if (isPlaying) {\n            audioEngine.current.pause();\n            setIsPlaying(false);\n          } else {\n            audioEngine.current.play();\n            setIsPlaying(true);\n          }\n        }\n      }\n    };\n    document.addEventListener('keydown', handleKeyDown);\n    return () => {\n      document.removeEventListener('keydown', handleKeyDown);\n    };\n  }, [currentTrack, isPlaying]);\n\n  // Global drag and drop handlers\n  useEffect(() => {\n    const handleDragEnter = e => {\n      e.preventDefault();\n      e.stopPropagation();\n      if (e.dataTransfer.items && e.dataTransfer.items.length > 0) {\n        setIsDragActive(true);\n      }\n    };\n    const handleDragLeave = e => {\n      e.preventDefault();\n      e.stopPropagation();\n      if (!e.currentTarget.contains(e.relatedTarget)) {\n        setIsDragActive(false);\n      }\n    };\n    const handleDragOver = e => {\n      e.preventDefault();\n      e.stopPropagation();\n    };\n    const handleDrop = async e => {\n      e.preventDefault();\n      e.stopPropagation();\n      setIsDragActive(false);\n      const files = [];\n      if (e.dataTransfer.files && e.dataTransfer.files.length > 0) {\n        for (let i = 0; i < e.dataTransfer.files.length; i++) {\n          const file = e.dataTransfer.files[i];\n          if (isAudioFile(file.name)) {\n            files.push(file);\n          }\n        }\n      }\n      if (files.length > 0) {\n        handleFilesAdded(files);\n      }\n    };\n    document.addEventListener('dragenter', handleDragEnter);\n    document.addEventListener('dragleave', handleDragLeave);\n    document.addEventListener('dragover', handleDragOver);\n    document.addEventListener('drop', handleDrop);\n    return () => {\n      document.removeEventListener('dragenter', handleDragEnter);\n      document.removeEventListener('dragleave', handleDragLeave);\n      document.removeEventListener('dragover', handleDragOver);\n      document.removeEventListener('drop', handleDrop);\n    };\n  }, []);\n  const isAudioFile = filename => {\n    const audioExtensions = ['.wav', '.flac', '.mp3', '.aac', '.ogg', '.m4a', '.wma'];\n    const ext = filename.toLowerCase().substring(filename.lastIndexOf('.'));\n    return audioExtensions.includes(ext);\n  };\n\n  // Handle file loading - SIMPLIFIED FOR TESTING\n  const handleFilesAdded = useCallback(async files => {\n    console.log('SIMPLIFIED handleFilesAdded called with:', files);\n    setError(null);\n    setLoadingMessage('Adding files...');\n    try {\n      const newFiles = [];\n      for (const file of files) {\n        console.log('Creating simple file entry for:', file.name);\n        const audioFile = {\n          id: Date.now() + Math.random(),\n          name: file.name,\n          path: file,\n          analysis: {\n            lufs: -16.0,\n            truePeak: -3.0,\n            duration: 180,\n            waveformData: new Array(1000).fill(0).map(() => Math.random() * 0.5)\n          },\n          color: generateRandomColor(),\n          waveformData: new Array(1000).fill(0).map(() => Math.random() * 0.5)\n        };\n        newFiles.push(audioFile);\n        console.log('Added file to newFiles:', audioFile.name);\n      }\n      console.log('About to update state with files:', newFiles.length);\n      setAudioFiles(prev => {\n        const updated = [...prev, ...newFiles];\n        console.log('State updated, total files:', updated.length);\n        return updated;\n      });\n      setLoadingMessage('');\n\n      // Auto-select first track if none selected\n      if (!currentTrack && newFiles.length > 0) {\n        console.log('Auto-selecting first track:', newFiles[0].name);\n        setCurrentTrack(newFiles[0]);\n      }\n      console.log('SIMPLIFIED handleFilesAdded completed successfully');\n    } catch (error) {\n      console.error('Error in simplified handleFilesAdded:', error);\n      setError(`Failed to add files: ${error.message}`);\n      setLoadingMessage('');\n    }\n  }, [currentTrack]);\n\n  // Generate random colors for waveforms\n  const generateRandomColor = () => {\n    const colors = ['#4CAF50', '#2196F3', '#FF9800', '#E91E63', '#9C27B0', '#00BCD4', '#FFEB3B', '#FF5722'];\n    return colors[Math.floor(Math.random() * colors.length)];\n  };\n\n  // Handle track selection\n  const handleTrackSelect = useCallback(async track => {\n    if (track.id === (currentTrack && currentTrack.id)) {\n      console.log('Track already selected');\n      return;\n    }\n    if (isLoading) {\n      console.log('Already loading a track, ignoring click');\n      return;\n    }\n    try {\n      console.log(`Loading track: ${track.name}`);\n      setIsLoading(true);\n\n      // Stop current playback\n      audioEngine.current.stop();\n      setIsPlaying(false);\n\n      // Load new track\n      await audioEngine.current.loadAudioFile(track.path);\n      setCurrentTrack(track);\n      setCurrentTime(0);\n      console.log(`Track loaded successfully: ${track.name}`);\n    } catch (error) {\n      console.error('Error loading track:', error);\n      // Reset state on error\n      setCurrentTrack(null);\n      setIsPlaying(false);\n      setCurrentTime(0);\n    } finally {\n      setIsLoading(false);\n    }\n  }, [currentTrack, isLoading]);\n\n  // Handle playback control\n  const handlePlayPause = useCallback(() => {\n    if (!currentTrack) {\n      console.log('No current track selected');\n      return;\n    }\n    if (isLoading) {\n      console.log('Track is loading, cannot play/pause');\n      return;\n    }\n\n    // Debounce rapid play/pause clicks\n    const now = Date.now();\n    if (now - lastPlayPauseTime.current < 200) {\n      console.log('Play/pause debounced');\n      return;\n    }\n    lastPlayPauseTime.current = now;\n    try {\n      const engine = audioEngine.current;\n      const currentEngineState = engine.getIsPlaying();\n      console.log(`Play/Pause clicked - Engine state: ${currentEngineState}`);\n\n      // Use ONLY the engine state as the source of truth\n      if (currentEngineState) {\n        console.log('Pausing playback');\n        engine.pause();\n        setIsPlaying(false);\n        console.log('Pause completed');\n      } else {\n        console.log('Starting playback');\n        // Ensure the track is loaded before playing\n        if (engine.currentBuffer) {\n          engine.play();\n          setIsPlaying(true);\n          console.log('Play started');\n        } else {\n          console.log('Track not loaded, reloading...');\n          // Reload the track if it's not loaded\n          handleTrackSelect(currentTrack);\n        }\n      }\n    } catch (error) {\n      console.error('Error in play/pause:', error);\n      // Reset state on error\n      setIsPlaying(false);\n    }\n  }, [isPlaying, currentTrack, isLoading, handleTrackSelect]);\n\n  // Handle waveform click (seek)\n  const handleWaveformClick = useCallback(async (track, position) => {\n    if (!track || typeof position !== 'number' || position < 0 || position > 1) {\n      console.error('Invalid waveform click parameters');\n      return;\n    }\n\n    // Debounce rapid clicks more aggressively\n    const now = Date.now();\n    if (now - lastClickTime.current < 300) {\n      console.log('Click debounced');\n      return;\n    }\n    lastClickTime.current = now;\n    console.log(`Waveform clicked: ${track.name} at position ${position.toFixed(3)}`);\n    try {\n      // If clicking on a different track, switch to it\n      if (track.id !== (currentTrack && currentTrack.id)) {\n        await handleTrackSelect(track);\n        // Wait for the track to load before seeking\n        await new Promise(resolve => setTimeout(resolve, 200));\n      }\n\n      // Ensure the audio engine is ready\n      const engine = audioEngine.current;\n      if (!engine.currentBuffer) {\n        console.warn('Audio not loaded, cannot seek');\n        return;\n      }\n\n      // Seek to position\n      const trackAnalysis = analysisData[track.id];\n      const trackDuration = trackAnalysis && trackAnalysis.duration || engine.getDuration() || 0;\n      if (trackDuration > 0) {\n        const seekTime = Math.max(0, Math.min(trackDuration, position * trackDuration));\n        console.log(`Seeking to: ${seekTime.toFixed(2)}s of ${trackDuration.toFixed(2)}s`);\n\n        // Store the current playing state before seeking\n        const wasPlaying = engine.getIsPlaying();\n\n        // Perform the seek\n        engine.seek(seekTime);\n        setCurrentTime(seekTime);\n\n        // Only start playing if we were already playing or if explicitly requested\n        if (wasPlaying) {\n          // Give a small delay to ensure seek is complete\n          setTimeout(() => {\n            if (engine.getIsPlaying()) {\n              setIsPlaying(true);\n            }\n          }, 50);\n        } else {\n          // If we weren't playing, just update the position\n          setIsPlaying(false);\n        }\n\n        // Validate state after a longer delay to avoid race conditions\n        setTimeout(validateAndRecoverState, 500);\n      } else {\n        console.warn('No duration available for seeking');\n      }\n    } catch (error) {\n      console.error('Error in waveform click:', error);\n    }\n  }, [currentTrack, analysisData, handleTrackSelect, isPlaying]);\n\n  // Handle track deletion\n  const handleTrackDelete = useCallback(trackToDelete => {\n    console.log(`Deleting track: ${trackToDelete.name}`);\n\n    // Stop playback if deleting current track\n    if (currentTrack && currentTrack.id === trackToDelete.id) {\n      audioEngine.current.stop();\n      setIsPlaying(false);\n      setCurrentTrack(null);\n      setCurrentTime(0);\n      setDuration(0);\n    }\n\n    // Remove from audio files list\n    setAudioFiles(prev => prev.filter(track => track.id !== trackToDelete.id));\n\n    // Remove from analysis data\n    setAnalysisData(prev => {\n      const newData = {\n        ...prev\n      };\n      delete newData[trackToDelete.id];\n      return newData;\n    });\n  }, [currentTrack]);\n  return /*#__PURE__*/_jsxDEV(AppContainer, {\n    children: [/*#__PURE__*/_jsxDEV(Header, {\n      children: [/*#__PURE__*/_jsxDEV(Title, {\n        children: \" Album Player \"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 514,\n        columnNumber: 9\n      }, this), \" \", /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          fontSize: '14px',\n          opacity: 0.7\n        },\n        children: \"Spotify Normalization Analyzer \"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 515,\n        columnNumber: 40\n      }, this), \" \", error && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          color: '#ff5722',\n          fontSize: '12px',\n          marginTop: '8px'\n        },\n        children: [\" \\u26A0\\uFE0F\", error, \" \"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 521,\n        columnNumber: 18\n      }, this), \" \", isLoading && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          color: '#4CAF50',\n          fontSize: '12px',\n          marginTop: '8px'\n        },\n        children: [\" \\u23F3\", loadingMessage, \" \"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 524,\n        columnNumber: 22\n      }, this), \" \"]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 512,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(MainContent, {\n      children: [/*#__PURE__*/_jsxDEV(WaveformContainer, {\n        children: [\" \", audioFiles.length === 0 ? /*#__PURE__*/_jsxDEV(FileDropZone, {\n          onFilesAdded: handleFilesAdded,\n          hasFiles: false,\n          children: [/*#__PURE__*/_jsxDEV(EmptyState, {\n            children: [/*#__PURE__*/_jsxDEV(EmptyStateIcon, {\n              children: \" \\uD83C\\uDFB5 \"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 538,\n              columnNumber: 17\n            }, this), \" \", /*#__PURE__*/_jsxDEV(\"h2\", {\n              children: \" Drop Audio Files Here \"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 539,\n              columnNumber: 56\n            }, this), \" \", /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \" Drag and drop your audio files to start analyzing \"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 540,\n              columnNumber: 51\n            }, this), \" \", /*#__PURE__*/_jsxDEV(\"p\", {\n              style: {\n                fontSize: '12px',\n                opacity: 0.5\n              },\n              children: \"Supported: WAV, FLAC, MP3, AAC, OGG \"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 541,\n              columnNumber: 77\n            }, this), \" \"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 536,\n            columnNumber: 17\n          }, this), \" \"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 533,\n          columnNumber: 41\n        }, this) : /*#__PURE__*/_jsxDEV(TracksContainer, {\n          children: [\" \", audioFiles.map((track, index) => /*#__PURE__*/_jsxDEV(WaveformPlayer, {\n            track: track,\n            isActive: currentTrack && currentTrack.id === track.id,\n            isPlaying: isPlaying && currentTrack && currentTrack.id === track.id,\n            onTrackSelect: handleTrackSelect,\n            onWaveformClick: handleWaveformClick,\n            onTrackDelete: handleTrackDelete,\n            onPlayPause: handlePlayPause,\n            analysisData: analysisData[track.id],\n            currentTime: currentTrack && currentTrack.id === track.id ? currentTime : 0,\n            duration: currentTrack && currentTrack.id === track.id ? duration : analysisData[track.id] && analysisData[track.id].duration || 0\n          }, track.id, false, {\n            fileName: _jsxFileName,\n            lineNumber: 551,\n            columnNumber: 60\n          }, this)), \" \", /*#__PURE__*/_jsxDEV(GlobalDropZone, {\n            isDragActive: isDragActive,\n            children: [\" \", isDragActive && /*#__PURE__*/_jsxDEV(\"h2\", {\n              children: \" Drop to Add More Files \"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 567,\n              columnNumber: 41\n            }, this), \" \"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 565,\n            columnNumber: 23\n          }, this), \" \"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 549,\n          columnNumber: 19\n        }, this), \" \"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 531,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(SidePanel, {\n        children: [/*#__PURE__*/_jsxDEV(ControlPanel, {\n          currentTrack: currentTrack,\n          isPlaying: isPlaying,\n          masterVolume: masterVolume,\n          onPlayPause: handlePlayPause,\n          onVolumeChange: setMasterVolume,\n          onFilesAdded: handleFilesAdded,\n          analysisData: currentTrack ? analysisData[currentTrack.id] : null,\n          currentTime: currentTime,\n          duration: duration\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 576,\n          columnNumber: 17\n        }, this), \" \"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 574,\n        columnNumber: 17\n      }, this), \" \"]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 529,\n      columnNumber: 9\n    }, this), \" \"]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 510,\n    columnNumber: 14\n  }, this);\n}\n_s(App, \"E8nlJykG9SLyv5S1s4R/0eH+XgQ=\");\n_c1 = App;\nexport default App;\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9, _c0, _c1;\n$RefreshReg$(_c, \"AppContainer\");\n$RefreshReg$(_c2, \"Header\");\n$RefreshReg$(_c3, \"Title\");\n$RefreshReg$(_c4, \"MainContent\");\n$RefreshReg$(_c5, \"WaveformContainer\");\n$RefreshReg$(_c6, \"SidePanel\");\n$RefreshReg$(_c7, \"EmptyState\");\n$RefreshReg$(_c8, \"EmptyStateIcon\");\n$RefreshReg$(_c9, \"TracksContainer\");\n$RefreshReg$(_c0, \"GlobalDropZone\");\n$RefreshReg$(_c1, \"App\");", "map": {"version": 3, "names": ["React", "useState", "useCallback", "useRef", "useEffect", "styled", "WaveformPlayer", "ControlPanel", "FileDropZone", "AudioAnalyzer", "AudioEngine", "jsxDEV", "_jsxDEV", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "div", "_c", "Header", "_c2", "Title", "h1", "_c3", "MainContent", "_c4", "WaveformContainer", "_c5", "SidePanel", "_c6", "EmptyState", "_c7", "EmptyStateIcon", "_c8", "TracksContainer", "_c9", "GlobalDropZone", "props", "isDragActive", "_c0", "App", "_s", "audioFiles", "setAudioFiles", "currentTrack", "setCurrentTrack", "isPlaying", "setIsPlaying", "masterVolume", "setMasterVolume", "analysisData", "setAnalysisData", "currentTime", "setCurrentTime", "duration", "setDuration", "setIsDragActive", "isLoading", "setIsLoading", "error", "setError", "loadingMessage", "setLoadingMessage", "audioAnalyzer", "audioEngine", "lastClickTime", "lastPlayPauseTime", "engine", "current", "onTimeUpdate", "time", "dur", "isNaN", "onEnded", "console", "log", "onError", "message", "onLoadStart", "onLoadEnd", "setVolume", "dispose", "handleKeyDown", "e", "code", "target", "tagName", "preventDefault", "pause", "play", "document", "addEventListener", "removeEventListener", "handleDragEnter", "stopPropagation", "dataTransfer", "items", "length", "handleDragLeave", "currentTarget", "contains", "relatedTarget", "handleDragOver", "handleDrop", "files", "i", "file", "isAudioFile", "name", "push", "handleFilesAdded", "filename", "audioExtensions", "ext", "toLowerCase", "substring", "lastIndexOf", "includes", "newFiles", "audioFile", "id", "Date", "now", "Math", "random", "path", "analysis", "lufs", "truePeak", "waveformData", "Array", "fill", "map", "color", "generateRandomColor", "prev", "updated", "colors", "floor", "handleTrackSelect", "track", "stop", "loadAudioFile", "handlePlayPause", "currentEngineState", "getIsPlaying", "current<PERSON><PERSON><PERSON>", "handleWaveformClick", "position", "toFixed", "Promise", "resolve", "setTimeout", "warn", "trackAnalysis", "trackDuration", "getDuration", "seekTime", "max", "min", "wasPlaying", "seek", "validateAndRecoverState", "handleTrackDelete", "trackToDelete", "filter", "newData", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "style", "fontSize", "opacity", "marginTop", "onFilesAdded", "hasFiles", "index", "isActive", "onTrackSelect", "onWaveformClick", "onTrackDelete", "onPlayPause", "onVolumeChange", "_c1", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/VSC Folder/AlbumPlayer/src/App.js"], "sourcesContent": ["import React, { useState, useCallback, useRef, useEffect } from 'react';\nimport styled from 'styled-components';\nimport WaveformPlayer from './components/WaveformPlayer';\nimport ControlPanel from './components/ControlPanel';\nimport FileDropZone from './components/FileDropZone';\nimport AudioAnalyzer from './audio/AudioAnalyzer';\nimport AudioEngine from './audio/AudioEngine';\n\nconst AppContainer = styled.div `\n  display: flex;\n  flex-direction: column;\n  height: 100vh;\n  background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);\n  color: #ffffff;\n  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;\n`;\n\nconst Header = styled.div `\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 20px 30px;\n  background: rgba(0, 0, 0, 0.3);\n  border-bottom: 1px solid rgba(255, 255, 255, 0.1);\n`;\n\nconst Title = styled.h1 `\n  margin: 0;\n  font-size: 24px;\n  font-weight: 300;\n  color: #4CAF50;\n`;\n\nconst MainContent = styled.div `\n  display: flex;\n  flex: 1;\n  overflow: hidden;\n`;\n\nconst WaveformContainer = styled.div `\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  padding: 20px;\n  overflow-y: auto;\n  position: relative;\n`;\n\nconst SidePanel = styled.div `\n  width: 350px;\n  background: rgba(0, 0, 0, 0.4);\n  border-left: 1px solid rgba(255, 255, 255, 0.1);\n  display: flex;\n  flex-direction: column;\n`;\n\nconst EmptyState = styled.div `\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  height: 100%;\n  color: rgba(255, 255, 255, 0.6);\n  text-align: center;\n`;\n\nconst EmptyStateIcon = styled.div `\n  font-size: 64px;\n  margin-bottom: 20px;\n  opacity: 0.3;\n`;\n\nconst TracksContainer = styled.div `\n  position: relative;\n`;\n\nconst GlobalDropZone = styled.div `\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  z-index: 1000;\n  pointer-events: ${props => props.isDragActive ? 'auto' : 'none'};\n  background: ${props => props.isDragActive ? 'rgba(76, 175, 80, 0.1)' : 'transparent'};\n  border: 2px dashed ${props => props.isDragActive ? '#4CAF50' : 'transparent'};\n  border-radius: 12px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  transition: all 0.3s ease;\n  \n  h2 {\n    color: #4CAF50;\n    font-size: 24px;\n    text-shadow: 0 2px 4px rgba(0,0,0,0.3);\n    margin: 0;\n  }\n`;\n\nfunction App() {\n    const [audioFiles, setAudioFiles] = useState([]);\n    const [currentTrack, setCurrentTrack] = useState(null);\n    const [isPlaying, setIsPlaying] = useState(false);\n    const [masterVolume, setMasterVolume] = useState(0.8);\n    const [analysisData, setAnalysisData] = useState({});\n    const [currentTime, setCurrentTime] = useState(0);\n    const [duration, setDuration] = useState(0);\n    const [isDragActive, setIsDragActive] = useState(false);\n    const [isLoading, setIsLoading] = useState(false);\n    const [error, setError] = useState(null);\n    const [loadingMessage, setLoadingMessage] = useState('');\n\n    const audioAnalyzer = useRef(new AudioAnalyzer());\n    const audioEngine = useRef(new AudioEngine());\n    const lastClickTime = useRef(0);\n    const lastPlayPauseTime = useRef(0);\n\n    // REMOVED: validateAndRecoverState function - was causing performance issues\n    // Audio engine event handlers below provide proper state synchronization\n\n    // REMOVED: Performance-killing validation loop\n    // The audio engine event handlers below provide proper state sync\n\n    // Set up audio engine event handlers\n    useEffect(() => {\n        const engine = audioEngine.current;\n\n        engine.onTimeUpdate = (time, dur) => {\n            // Validate the time values before setting state\n            if (typeof time === 'number' && !isNaN(time) && time >= 0) {\n                setCurrentTime(time);\n            }\n            if (typeof dur === 'number' && !isNaN(dur) && dur > 0) {\n                setDuration(dur);\n            }\n        };\n\n        engine.onEnded = () => {\n            console.log('Audio ended');\n            setIsPlaying(false);\n            setCurrentTime(0);\n        };\n\n        engine.onError = (error) => {\n            console.error('Audio engine error:', error);\n            setIsPlaying(false);\n            setIsLoading(false);\n            setError(`Audio error: ${error.message || 'Unknown error'}`);\n            setLoadingMessage('');\n        };\n\n        engine.onLoadStart = () => {\n            console.log('Audio load started');\n            setIsLoading(true);\n            setLoadingMessage('Loading audio file...');\n            setError(null);\n        };\n\n        engine.onLoadEnd = () => {\n            console.log('Audio load ended');\n            setIsLoading(false);\n            setLoadingMessage('');\n        };\n\n        // Set initial volume\n        engine.setVolume(masterVolume);\n\n        return () => {\n            engine.dispose();\n        };\n    }, []);\n\n    // Update volume when masterVolume changes\n    useEffect(() => {\n        audioEngine.current.setVolume(masterVolume);\n    }, [masterVolume]);\n\n    // Add spacebar play/pause functionality\n    useEffect(() => {\n        const handleKeyDown = (e) => {\n            if (e.code === 'Space' && e.target.tagName !== 'INPUT' && e.target.tagName !== 'TEXTAREA') {\n                e.preventDefault();\n                if (currentTrack) {\n                    if (isPlaying) {\n                        audioEngine.current.pause();\n                        setIsPlaying(false);\n                    } else {\n                        audioEngine.current.play();\n                        setIsPlaying(true);\n                    }\n                }\n            }\n        };\n\n        document.addEventListener('keydown', handleKeyDown);\n        return () => {\n            document.removeEventListener('keydown', handleKeyDown);\n        };\n    }, [currentTrack, isPlaying]);\n\n    // Global drag and drop handlers\n    useEffect(() => {\n        const handleDragEnter = (e) => {\n            e.preventDefault();\n            e.stopPropagation();\n            if (e.dataTransfer.items && e.dataTransfer.items.length > 0) {\n                setIsDragActive(true);\n            }\n        };\n\n        const handleDragLeave = (e) => {\n            e.preventDefault();\n            e.stopPropagation();\n            if (!e.currentTarget.contains(e.relatedTarget)) {\n                setIsDragActive(false);\n            }\n        };\n\n        const handleDragOver = (e) => {\n            e.preventDefault();\n            e.stopPropagation();\n        };\n\n        const handleDrop = async(e) => {\n            e.preventDefault();\n            e.stopPropagation();\n            setIsDragActive(false);\n\n            const files = [];\n            if (e.dataTransfer.files && e.dataTransfer.files.length > 0) {\n                for (let i = 0; i < e.dataTransfer.files.length; i++) {\n                    const file = e.dataTransfer.files[i];\n                    if (isAudioFile(file.name)) {\n                        files.push(file);\n                    }\n                }\n            }\n\n            if (files.length > 0) {\n                handleFilesAdded(files);\n            }\n        };\n\n        document.addEventListener('dragenter', handleDragEnter);\n        document.addEventListener('dragleave', handleDragLeave);\n        document.addEventListener('dragover', handleDragOver);\n        document.addEventListener('drop', handleDrop);\n\n        return () => {\n            document.removeEventListener('dragenter', handleDragEnter);\n            document.removeEventListener('dragleave', handleDragLeave);\n            document.removeEventListener('dragover', handleDragOver);\n            document.removeEventListener('drop', handleDrop);\n        };\n    }, []);\n\n    const isAudioFile = (filename) => {\n        const audioExtensions = ['.wav', '.flac', '.mp3', '.aac', '.ogg', '.m4a', '.wma'];\n        const ext = filename.toLowerCase().substring(filename.lastIndexOf('.'));\n        return audioExtensions.includes(ext);\n    };\n\n    // Handle file loading - SIMPLIFIED FOR TESTING\n    const handleFilesAdded = useCallback(async(files) => {\n        console.log('SIMPLIFIED handleFilesAdded called with:', files);\n        setError(null);\n        setLoadingMessage('Adding files...');\n\n        try {\n            const newFiles = [];\n\n            for (const file of files) {\n                console.log('Creating simple file entry for:', file.name);\n\n                const audioFile = {\n                    id: Date.now() + Math.random(),\n                    name: file.name,\n                    path: file,\n                    analysis: {\n                        lufs: -16.0,\n                        truePeak: -3.0,\n                        duration: 180,\n                        waveformData: new Array(1000).fill(0).map(() => Math.random() * 0.5)\n                    },\n                    color: generateRandomColor(),\n                    waveformData: new Array(1000).fill(0).map(() => Math.random() * 0.5)\n                };\n\n                newFiles.push(audioFile);\n                console.log('Added file to newFiles:', audioFile.name);\n            }\n\n            console.log('About to update state with files:', newFiles.length);\n            setAudioFiles(prev => {\n                const updated = [...prev, ...newFiles];\n                console.log('State updated, total files:', updated.length);\n                return updated;\n            });\n\n            setLoadingMessage('');\n\n            // Auto-select first track if none selected\n            if (!currentTrack && newFiles.length > 0) {\n                console.log('Auto-selecting first track:', newFiles[0].name);\n                setCurrentTrack(newFiles[0]);\n            }\n\n            console.log('SIMPLIFIED handleFilesAdded completed successfully');\n\n        } catch (error) {\n            console.error('Error in simplified handleFilesAdded:', error);\n            setError(`Failed to add files: ${error.message}`);\n            setLoadingMessage('');\n        }\n    }, [currentTrack]);\n\n    // Generate random colors for waveforms\n    const generateRandomColor = () => {\n        const colors = [\n            '#4CAF50', '#2196F3', '#FF9800', '#E91E63',\n            '#9C27B0', '#00BCD4', '#FFEB3B', '#FF5722'\n        ];\n        return colors[Math.floor(Math.random() * colors.length)];\n    };\n\n    // Handle track selection\n    const handleTrackSelect = useCallback(async(track) => {\n        if (track.id === (currentTrack && currentTrack.id)) {\n            console.log('Track already selected');\n            return;\n        }\n\n        if (isLoading) {\n            console.log('Already loading a track, ignoring click');\n            return;\n        }\n\n        try {\n            console.log(`Loading track: ${track.name}`);\n            setIsLoading(true);\n\n            // Stop current playback\n            audioEngine.current.stop();\n            setIsPlaying(false);\n\n            // Load new track\n            await audioEngine.current.loadAudioFile(track.path);\n            setCurrentTrack(track);\n            setCurrentTime(0);\n\n            console.log(`Track loaded successfully: ${track.name}`);\n        } catch (error) {\n            console.error('Error loading track:', error);\n            // Reset state on error\n            setCurrentTrack(null);\n            setIsPlaying(false);\n            setCurrentTime(0);\n        } finally {\n            setIsLoading(false);\n        }\n    }, [currentTrack, isLoading]);\n\n    // Handle playback control\n    const handlePlayPause = useCallback(() => {\n        if (!currentTrack) {\n            console.log('No current track selected');\n            return;\n        }\n\n        if (isLoading) {\n            console.log('Track is loading, cannot play/pause');\n            return;\n        }\n\n        // Debounce rapid play/pause clicks\n        const now = Date.now();\n        if (now - lastPlayPauseTime.current < 200) {\n            console.log('Play/pause debounced');\n            return;\n        }\n        lastPlayPauseTime.current = now;\n\n        try {\n            const engine = audioEngine.current;\n            const currentEngineState = engine.getIsPlaying();\n\n            console.log(`Play/Pause clicked - Engine state: ${currentEngineState}`);\n\n            // Use ONLY the engine state as the source of truth\n            if (currentEngineState) {\n                console.log('Pausing playback');\n                engine.pause();\n                setIsPlaying(false);\n                console.log('Pause completed');\n            } else {\n                console.log('Starting playback');\n                // Ensure the track is loaded before playing\n                if (engine.currentBuffer) {\n                    engine.play();\n                    setIsPlaying(true);\n                    console.log('Play started');\n                } else {\n                    console.log('Track not loaded, reloading...');\n                    // Reload the track if it's not loaded\n                    handleTrackSelect(currentTrack);\n                }\n            }\n        } catch (error) {\n            console.error('Error in play/pause:', error);\n            // Reset state on error\n            setIsPlaying(false);\n        }\n    }, [isPlaying, currentTrack, isLoading, handleTrackSelect]);\n\n    // Handle waveform click (seek)\n    const handleWaveformClick = useCallback(async(track, position) => {\n        if (!track || typeof position !== 'number' || position < 0 || position > 1) {\n            console.error('Invalid waveform click parameters');\n            return;\n        }\n\n        // Debounce rapid clicks more aggressively\n        const now = Date.now();\n        if (now - lastClickTime.current < 300) {\n            console.log('Click debounced');\n            return;\n        }\n        lastClickTime.current = now;\n\n        console.log(`Waveform clicked: ${track.name} at position ${position.toFixed(3)}`);\n\n        try {\n            // If clicking on a different track, switch to it\n            if (track.id !== (currentTrack && currentTrack.id)) {\n                await handleTrackSelect(track);\n                // Wait for the track to load before seeking\n                await new Promise(resolve => setTimeout(resolve, 200));\n            }\n\n            // Ensure the audio engine is ready\n            const engine = audioEngine.current;\n            if (!engine.currentBuffer) {\n                console.warn('Audio not loaded, cannot seek');\n                return;\n            }\n\n            // Seek to position\n            const trackAnalysis = analysisData[track.id];\n            const trackDuration = trackAnalysis && trackAnalysis.duration || engine.getDuration() || 0;\n\n            if (trackDuration > 0) {\n                const seekTime = Math.max(0, Math.min(trackDuration, position * trackDuration));\n                console.log(`Seeking to: ${seekTime.toFixed(2)}s of ${trackDuration.toFixed(2)}s`);\n\n                // Store the current playing state before seeking\n                const wasPlaying = engine.getIsPlaying();\n\n                // Perform the seek\n                engine.seek(seekTime);\n                setCurrentTime(seekTime);\n\n                // Only start playing if we were already playing or if explicitly requested\n                if (wasPlaying) {\n                    // Give a small delay to ensure seek is complete\n                    setTimeout(() => {\n                        if (engine.getIsPlaying()) {\n                            setIsPlaying(true);\n                        }\n                    }, 50);\n                } else {\n                    // If we weren't playing, just update the position\n                    setIsPlaying(false);\n                }\n\n                // Validate state after a longer delay to avoid race conditions\n                setTimeout(validateAndRecoverState, 500);\n            } else {\n                console.warn('No duration available for seeking');\n            }\n        } catch (error) {\n            console.error('Error in waveform click:', error);\n        }\n    }, [currentTrack, analysisData, handleTrackSelect, isPlaying]);\n\n    // Handle track deletion\n    const handleTrackDelete = useCallback((trackToDelete) => {\n        console.log(`Deleting track: ${trackToDelete.name}`);\n\n        // Stop playback if deleting current track\n        if (currentTrack && currentTrack.id === trackToDelete.id) {\n            audioEngine.current.stop();\n            setIsPlaying(false);\n            setCurrentTrack(null);\n            setCurrentTime(0);\n            setDuration(0);\n        }\n\n        // Remove from audio files list\n        setAudioFiles(prev => prev.filter(track => track.id !== trackToDelete.id));\n\n        // Remove from analysis data\n        setAnalysisData(prev => {\n            const newData = {...prev };\n            delete newData[trackToDelete.id];\n            return newData;\n        });\n    }, [currentTrack]);\n\n    return ( <\n        AppContainer >\n        <\n        Header >\n        <\n        Title > Album Player < /Title> <\n        div style = {\n            { fontSize: '14px', opacity: 0.7 }\n        } >\n        Spotify Normalization Analyzer <\n        /div> {\n        error && < div style = {\n            { color: '#ff5722', fontSize: '12px', marginTop: '8px' }\n        } > ⚠️{ error } < /div>} {\n        isLoading && < div style = {\n            { color: '#4CAF50', fontSize: '12px', marginTop: '8px' }\n        } > ⏳{ loadingMessage } < /div>} < /\n        Header >\n\n        <\n        MainContent >\n        <\n        WaveformContainer > {\n            audioFiles.length === 0 ? ( <\n                FileDropZone onFilesAdded = { handleFilesAdded }\n                hasFiles = { false } >\n                <\n                EmptyState >\n                <\n                EmptyStateIcon > 🎵 < /EmptyStateIcon> <\n                h2 > Drop Audio Files Here < /h2> <\n                p > Drag and drop your audio files to start analyzing < /p> <\n                p style = {\n                    { fontSize: '12px', opacity: 0.5 }\n                } >\n                Supported: WAV, FLAC, MP3, AAC, OGG <\n                /p> < /\n                EmptyState > <\n                /FileDropZone>\n            ) : ( <\n                    TracksContainer > {\n                        audioFiles.map((track, index) => ( <\n                            WaveformPlayer key = { track.id }\n                            track = { track }\n                            isActive = { currentTrack && currentTrack.id === track.id }\n                            isPlaying = { isPlaying && currentTrack && currentTrack.id === track.id }\n                            onTrackSelect = { handleTrackSelect }\n                            onWaveformClick = { handleWaveformClick }\n                            onTrackDelete = { handleTrackDelete }\n                            onPlayPause = { handlePlayPause }\n                            analysisData = { analysisData[track.id] }\n                            currentTime = { currentTrack && currentTrack.id === track.id ? currentTime : 0 }\n                            duration = { currentTrack && currentTrack.id === track.id ? duration : (analysisData[track.id] && analysisData[track.id].duration || 0) }\n                            />\n                        ))\n                    } <\n                    GlobalDropZone isDragActive = { isDragActive } > {\n                        isDragActive && < h2 > Drop to Add More Files < /h2>} < /\n                        GlobalDropZone > <\n                        /TracksContainer>\n                    )\n                } <\n                /WaveformContainer>\n\n                <\n                SidePanel >\n                <\n                ControlPanel\n            currentTrack = { currentTrack }\n            isPlaying = { isPlaying }\n            masterVolume = { masterVolume }\n            onPlayPause = { handlePlayPause }\n            onVolumeChange = { setMasterVolume }\n            onFilesAdded = { handleFilesAdded }\n            analysisData = { currentTrack ? analysisData[currentTrack.id] : null }\n            currentTime = { currentTime }\n            duration = { duration }\n            /> < /\n            SidePanel > <\n            /MainContent> < /\n            AppContainer >\n        );\n    }\n\n    export default App;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,WAAW,EAAEC,MAAM,EAAEC,SAAS,QAAQ,OAAO;AACvE,OAAOC,MAAM,MAAM,mBAAmB;AACtC,OAAOC,cAAc,MAAM,6BAA6B;AACxD,OAAOC,YAAY,MAAM,2BAA2B;AACpD,OAAOC,YAAY,MAAM,2BAA2B;AACpD,OAAOC,aAAa,MAAM,uBAAuB;AACjD,OAAOC,WAAW,MAAM,qBAAqB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE9C,MAAMC,YAAY,GAAGR,MAAM,CAACS,GAAI;AAChC;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,EAAA,GAPIF,YAAY;AASlB,MAAMG,MAAM,GAAGX,MAAM,CAACS,GAAI;AAC1B;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACG,GAAA,GAPID,MAAM;AASZ,MAAME,KAAK,GAAGb,MAAM,CAACc,EAAG;AACxB;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GALIF,KAAK;AAOX,MAAMG,WAAW,GAAGhB,MAAM,CAACS,GAAI;AAC/B;AACA;AACA;AACA,CAAC;AAACQ,GAAA,GAJID,WAAW;AAMjB,MAAME,iBAAiB,GAAGlB,MAAM,CAACS,GAAI;AACrC;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACU,GAAA,GAPID,iBAAiB;AASvB,MAAME,SAAS,GAAGpB,MAAM,CAACS,GAAI;AAC7B;AACA;AACA;AACA;AACA;AACA,CAAC;AAACY,GAAA,GANID,SAAS;AAQf,MAAME,UAAU,GAAGtB,MAAM,CAACS,GAAI;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACc,GAAA,GARID,UAAU;AAUhB,MAAME,cAAc,GAAGxB,MAAM,CAACS,GAAI;AAClC;AACA;AACA;AACA,CAAC;AAACgB,GAAA,GAJID,cAAc;AAMpB,MAAME,eAAe,GAAG1B,MAAM,CAACS,GAAI;AACnC;AACA,CAAC;AAACkB,GAAA,GAFID,eAAe;AAIrB,MAAME,cAAc,GAAG5B,MAAM,CAACS,GAAI;AAClC;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoBoB,KAAK,IAAIA,KAAK,CAACC,YAAY,GAAG,MAAM,GAAG,MAAM;AACjE,gBAAgBD,KAAK,IAAIA,KAAK,CAACC,YAAY,GAAG,wBAAwB,GAAG,aAAa;AACtF,uBAAuBD,KAAK,IAAIA,KAAK,CAACC,YAAY,GAAG,SAAS,GAAG,aAAa;AAC9E;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GAtBIH,cAAc;AAwBpB,SAASI,GAAGA,CAAA,EAAG;EAAAC,EAAA;EACX,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGvC,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACwC,YAAY,EAAEC,eAAe,CAAC,GAAGzC,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAAC0C,SAAS,EAAEC,YAAY,CAAC,GAAG3C,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAAC4C,YAAY,EAAEC,eAAe,CAAC,GAAG7C,QAAQ,CAAC,GAAG,CAAC;EACrD,MAAM,CAAC8C,YAAY,EAAEC,eAAe,CAAC,GAAG/C,QAAQ,CAAC,CAAC,CAAC,CAAC;EACpD,MAAM,CAACgD,WAAW,EAAEC,cAAc,CAAC,GAAGjD,QAAQ,CAAC,CAAC,CAAC;EACjD,MAAM,CAACkD,QAAQ,EAAEC,WAAW,CAAC,GAAGnD,QAAQ,CAAC,CAAC,CAAC;EAC3C,MAAM,CAACkC,YAAY,EAAEkB,eAAe,CAAC,GAAGpD,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACqD,SAAS,EAAEC,YAAY,CAAC,GAAGtD,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACuD,KAAK,EAAEC,QAAQ,CAAC,GAAGxD,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAACyD,cAAc,EAAEC,iBAAiB,CAAC,GAAG1D,QAAQ,CAAC,EAAE,CAAC;EAExD,MAAM2D,aAAa,GAAGzD,MAAM,CAAC,IAAIM,aAAa,CAAC,CAAC,CAAC;EACjD,MAAMoD,WAAW,GAAG1D,MAAM,CAAC,IAAIO,WAAW,CAAC,CAAC,CAAC;EAC7C,MAAMoD,aAAa,GAAG3D,MAAM,CAAC,CAAC,CAAC;EAC/B,MAAM4D,iBAAiB,GAAG5D,MAAM,CAAC,CAAC,CAAC;;EAEnC;EACA;;EAEA;EACA;;EAEA;EACAC,SAAS,CAAC,MAAM;IACZ,MAAM4D,MAAM,GAAGH,WAAW,CAACI,OAAO;IAElCD,MAAM,CAACE,YAAY,GAAG,CAACC,IAAI,EAAEC,GAAG,KAAK;MACjC;MACA,IAAI,OAAOD,IAAI,KAAK,QAAQ,IAAI,CAACE,KAAK,CAACF,IAAI,CAAC,IAAIA,IAAI,IAAI,CAAC,EAAE;QACvDjB,cAAc,CAACiB,IAAI,CAAC;MACxB;MACA,IAAI,OAAOC,GAAG,KAAK,QAAQ,IAAI,CAACC,KAAK,CAACD,GAAG,CAAC,IAAIA,GAAG,GAAG,CAAC,EAAE;QACnDhB,WAAW,CAACgB,GAAG,CAAC;MACpB;IACJ,CAAC;IAEDJ,MAAM,CAACM,OAAO,GAAG,MAAM;MACnBC,OAAO,CAACC,GAAG,CAAC,aAAa,CAAC;MAC1B5B,YAAY,CAAC,KAAK,CAAC;MACnBM,cAAc,CAAC,CAAC,CAAC;IACrB,CAAC;IAEDc,MAAM,CAACS,OAAO,GAAIjB,KAAK,IAAK;MACxBe,OAAO,CAACf,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAAC;MAC3CZ,YAAY,CAAC,KAAK,CAAC;MACnBW,YAAY,CAAC,KAAK,CAAC;MACnBE,QAAQ,CAAC,gBAAgBD,KAAK,CAACkB,OAAO,IAAI,eAAe,EAAE,CAAC;MAC5Df,iBAAiB,CAAC,EAAE,CAAC;IACzB,CAAC;IAEDK,MAAM,CAACW,WAAW,GAAG,MAAM;MACvBJ,OAAO,CAACC,GAAG,CAAC,oBAAoB,CAAC;MACjCjB,YAAY,CAAC,IAAI,CAAC;MAClBI,iBAAiB,CAAC,uBAAuB,CAAC;MAC1CF,QAAQ,CAAC,IAAI,CAAC;IAClB,CAAC;IAEDO,MAAM,CAACY,SAAS,GAAG,MAAM;MACrBL,OAAO,CAACC,GAAG,CAAC,kBAAkB,CAAC;MAC/BjB,YAAY,CAAC,KAAK,CAAC;MACnBI,iBAAiB,CAAC,EAAE,CAAC;IACzB,CAAC;;IAED;IACAK,MAAM,CAACa,SAAS,CAAChC,YAAY,CAAC;IAE9B,OAAO,MAAM;MACTmB,MAAM,CAACc,OAAO,CAAC,CAAC;IACpB,CAAC;EACL,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA1E,SAAS,CAAC,MAAM;IACZyD,WAAW,CAACI,OAAO,CAACY,SAAS,CAAChC,YAAY,CAAC;EAC/C,CAAC,EAAE,CAACA,YAAY,CAAC,CAAC;;EAElB;EACAzC,SAAS,CAAC,MAAM;IACZ,MAAM2E,aAAa,GAAIC,CAAC,IAAK;MACzB,IAAIA,CAAC,CAACC,IAAI,KAAK,OAAO,IAAID,CAAC,CAACE,MAAM,CAACC,OAAO,KAAK,OAAO,IAAIH,CAAC,CAACE,MAAM,CAACC,OAAO,KAAK,UAAU,EAAE;QACvFH,CAAC,CAACI,cAAc,CAAC,CAAC;QAClB,IAAI3C,YAAY,EAAE;UACd,IAAIE,SAAS,EAAE;YACXkB,WAAW,CAACI,OAAO,CAACoB,KAAK,CAAC,CAAC;YAC3BzC,YAAY,CAAC,KAAK,CAAC;UACvB,CAAC,MAAM;YACHiB,WAAW,CAACI,OAAO,CAACqB,IAAI,CAAC,CAAC;YAC1B1C,YAAY,CAAC,IAAI,CAAC;UACtB;QACJ;MACJ;IACJ,CAAC;IAED2C,QAAQ,CAACC,gBAAgB,CAAC,SAAS,EAAET,aAAa,CAAC;IACnD,OAAO,MAAM;MACTQ,QAAQ,CAACE,mBAAmB,CAAC,SAAS,EAAEV,aAAa,CAAC;IAC1D,CAAC;EACL,CAAC,EAAE,CAACtC,YAAY,EAAEE,SAAS,CAAC,CAAC;;EAE7B;EACAvC,SAAS,CAAC,MAAM;IACZ,MAAMsF,eAAe,GAAIV,CAAC,IAAK;MAC3BA,CAAC,CAACI,cAAc,CAAC,CAAC;MAClBJ,CAAC,CAACW,eAAe,CAAC,CAAC;MACnB,IAAIX,CAAC,CAACY,YAAY,CAACC,KAAK,IAAIb,CAAC,CAACY,YAAY,CAACC,KAAK,CAACC,MAAM,GAAG,CAAC,EAAE;QACzDzC,eAAe,CAAC,IAAI,CAAC;MACzB;IACJ,CAAC;IAED,MAAM0C,eAAe,GAAIf,CAAC,IAAK;MAC3BA,CAAC,CAACI,cAAc,CAAC,CAAC;MAClBJ,CAAC,CAACW,eAAe,CAAC,CAAC;MACnB,IAAI,CAACX,CAAC,CAACgB,aAAa,CAACC,QAAQ,CAACjB,CAAC,CAACkB,aAAa,CAAC,EAAE;QAC5C7C,eAAe,CAAC,KAAK,CAAC;MAC1B;IACJ,CAAC;IAED,MAAM8C,cAAc,GAAInB,CAAC,IAAK;MAC1BA,CAAC,CAACI,cAAc,CAAC,CAAC;MAClBJ,CAAC,CAACW,eAAe,CAAC,CAAC;IACvB,CAAC;IAED,MAAMS,UAAU,GAAG,MAAMpB,CAAC,IAAK;MAC3BA,CAAC,CAACI,cAAc,CAAC,CAAC;MAClBJ,CAAC,CAACW,eAAe,CAAC,CAAC;MACnBtC,eAAe,CAAC,KAAK,CAAC;MAEtB,MAAMgD,KAAK,GAAG,EAAE;MAChB,IAAIrB,CAAC,CAACY,YAAY,CAACS,KAAK,IAAIrB,CAAC,CAACY,YAAY,CAACS,KAAK,CAACP,MAAM,GAAG,CAAC,EAAE;QACzD,KAAK,IAAIQ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGtB,CAAC,CAACY,YAAY,CAACS,KAAK,CAACP,MAAM,EAAEQ,CAAC,EAAE,EAAE;UAClD,MAAMC,IAAI,GAAGvB,CAAC,CAACY,YAAY,CAACS,KAAK,CAACC,CAAC,CAAC;UACpC,IAAIE,WAAW,CAACD,IAAI,CAACE,IAAI,CAAC,EAAE;YACxBJ,KAAK,CAACK,IAAI,CAACH,IAAI,CAAC;UACpB;QACJ;MACJ;MAEA,IAAIF,KAAK,CAACP,MAAM,GAAG,CAAC,EAAE;QAClBa,gBAAgB,CAACN,KAAK,CAAC;MAC3B;IACJ,CAAC;IAEDd,QAAQ,CAACC,gBAAgB,CAAC,WAAW,EAAEE,eAAe,CAAC;IACvDH,QAAQ,CAACC,gBAAgB,CAAC,WAAW,EAAEO,eAAe,CAAC;IACvDR,QAAQ,CAACC,gBAAgB,CAAC,UAAU,EAAEW,cAAc,CAAC;IACrDZ,QAAQ,CAACC,gBAAgB,CAAC,MAAM,EAAEY,UAAU,CAAC;IAE7C,OAAO,MAAM;MACTb,QAAQ,CAACE,mBAAmB,CAAC,WAAW,EAAEC,eAAe,CAAC;MAC1DH,QAAQ,CAACE,mBAAmB,CAAC,WAAW,EAAEM,eAAe,CAAC;MAC1DR,QAAQ,CAACE,mBAAmB,CAAC,UAAU,EAAEU,cAAc,CAAC;MACxDZ,QAAQ,CAACE,mBAAmB,CAAC,MAAM,EAAEW,UAAU,CAAC;IACpD,CAAC;EACL,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMI,WAAW,GAAII,QAAQ,IAAK;IAC9B,MAAMC,eAAe,GAAG,CAAC,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC;IACjF,MAAMC,GAAG,GAAGF,QAAQ,CAACG,WAAW,CAAC,CAAC,CAACC,SAAS,CAACJ,QAAQ,CAACK,WAAW,CAAC,GAAG,CAAC,CAAC;IACvE,OAAOJ,eAAe,CAACK,QAAQ,CAACJ,GAAG,CAAC;EACxC,CAAC;;EAED;EACA,MAAMH,gBAAgB,GAAGzG,WAAW,CAAC,MAAMmG,KAAK,IAAK;IACjD9B,OAAO,CAACC,GAAG,CAAC,0CAA0C,EAAE6B,KAAK,CAAC;IAC9D5C,QAAQ,CAAC,IAAI,CAAC;IACdE,iBAAiB,CAAC,iBAAiB,CAAC;IAEpC,IAAI;MACA,MAAMwD,QAAQ,GAAG,EAAE;MAEnB,KAAK,MAAMZ,IAAI,IAAIF,KAAK,EAAE;QACtB9B,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAE+B,IAAI,CAACE,IAAI,CAAC;QAEzD,MAAMW,SAAS,GAAG;UACdC,EAAE,EAAEC,IAAI,CAACC,GAAG,CAAC,CAAC,GAAGC,IAAI,CAACC,MAAM,CAAC,CAAC;UAC9BhB,IAAI,EAAEF,IAAI,CAACE,IAAI;UACfiB,IAAI,EAAEnB,IAAI;UACVoB,QAAQ,EAAE;YACNC,IAAI,EAAE,CAAC,IAAI;YACXC,QAAQ,EAAE,CAAC,GAAG;YACd1E,QAAQ,EAAE,GAAG;YACb2E,YAAY,EAAE,IAAIC,KAAK,CAAC,IAAI,CAAC,CAACC,IAAI,CAAC,CAAC,CAAC,CAACC,GAAG,CAAC,MAAMT,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG;UACvE,CAAC;UACDS,KAAK,EAAEC,mBAAmB,CAAC,CAAC;UAC5BL,YAAY,EAAE,IAAIC,KAAK,CAAC,IAAI,CAAC,CAACC,IAAI,CAAC,CAAC,CAAC,CAACC,GAAG,CAAC,MAAMT,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG;QACvE,CAAC;QAEDN,QAAQ,CAACT,IAAI,CAACU,SAAS,CAAC;QACxB7C,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAE4C,SAAS,CAACX,IAAI,CAAC;MAC1D;MAEAlC,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAE2C,QAAQ,CAACrB,MAAM,CAAC;MACjEtD,aAAa,CAAC4F,IAAI,IAAI;QAClB,MAAMC,OAAO,GAAG,CAAC,GAAGD,IAAI,EAAE,GAAGjB,QAAQ,CAAC;QACtC5C,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAE6D,OAAO,CAACvC,MAAM,CAAC;QAC1D,OAAOuC,OAAO;MAClB,CAAC,CAAC;MAEF1E,iBAAiB,CAAC,EAAE,CAAC;;MAErB;MACA,IAAI,CAAClB,YAAY,IAAI0E,QAAQ,CAACrB,MAAM,GAAG,CAAC,EAAE;QACtCvB,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAE2C,QAAQ,CAAC,CAAC,CAAC,CAACV,IAAI,CAAC;QAC5D/D,eAAe,CAACyE,QAAQ,CAAC,CAAC,CAAC,CAAC;MAChC;MAEA5C,OAAO,CAACC,GAAG,CAAC,oDAAoD,CAAC;IAErE,CAAC,CAAC,OAAOhB,KAAK,EAAE;MACZe,OAAO,CAACf,KAAK,CAAC,uCAAuC,EAAEA,KAAK,CAAC;MAC7DC,QAAQ,CAAC,wBAAwBD,KAAK,CAACkB,OAAO,EAAE,CAAC;MACjDf,iBAAiB,CAAC,EAAE,CAAC;IACzB;EACJ,CAAC,EAAE,CAAClB,YAAY,CAAC,CAAC;;EAElB;EACA,MAAM0F,mBAAmB,GAAGA,CAAA,KAAM;IAC9B,MAAMG,MAAM,GAAG,CACX,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAC1C,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,CAC7C;IACD,OAAOA,MAAM,CAACd,IAAI,CAACe,KAAK,CAACf,IAAI,CAACC,MAAM,CAAC,CAAC,GAAGa,MAAM,CAACxC,MAAM,CAAC,CAAC;EAC5D,CAAC;;EAED;EACA,MAAM0C,iBAAiB,GAAGtI,WAAW,CAAC,MAAMuI,KAAK,IAAK;IAClD,IAAIA,KAAK,CAACpB,EAAE,MAAM5E,YAAY,IAAIA,YAAY,CAAC4E,EAAE,CAAC,EAAE;MAChD9C,OAAO,CAACC,GAAG,CAAC,wBAAwB,CAAC;MACrC;IACJ;IAEA,IAAIlB,SAAS,EAAE;MACXiB,OAAO,CAACC,GAAG,CAAC,yCAAyC,CAAC;MACtD;IACJ;IAEA,IAAI;MACAD,OAAO,CAACC,GAAG,CAAC,kBAAkBiE,KAAK,CAAChC,IAAI,EAAE,CAAC;MAC3ClD,YAAY,CAAC,IAAI,CAAC;;MAElB;MACAM,WAAW,CAACI,OAAO,CAACyE,IAAI,CAAC,CAAC;MAC1B9F,YAAY,CAAC,KAAK,CAAC;;MAEnB;MACA,MAAMiB,WAAW,CAACI,OAAO,CAAC0E,aAAa,CAACF,KAAK,CAACf,IAAI,CAAC;MACnDhF,eAAe,CAAC+F,KAAK,CAAC;MACtBvF,cAAc,CAAC,CAAC,CAAC;MAEjBqB,OAAO,CAACC,GAAG,CAAC,8BAA8BiE,KAAK,CAAChC,IAAI,EAAE,CAAC;IAC3D,CAAC,CAAC,OAAOjD,KAAK,EAAE;MACZe,OAAO,CAACf,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC5C;MACAd,eAAe,CAAC,IAAI,CAAC;MACrBE,YAAY,CAAC,KAAK,CAAC;MACnBM,cAAc,CAAC,CAAC,CAAC;IACrB,CAAC,SAAS;MACNK,YAAY,CAAC,KAAK,CAAC;IACvB;EACJ,CAAC,EAAE,CAACd,YAAY,EAAEa,SAAS,CAAC,CAAC;;EAE7B;EACA,MAAMsF,eAAe,GAAG1I,WAAW,CAAC,MAAM;IACtC,IAAI,CAACuC,YAAY,EAAE;MACf8B,OAAO,CAACC,GAAG,CAAC,2BAA2B,CAAC;MACxC;IACJ;IAEA,IAAIlB,SAAS,EAAE;MACXiB,OAAO,CAACC,GAAG,CAAC,qCAAqC,CAAC;MAClD;IACJ;;IAEA;IACA,MAAM+C,GAAG,GAAGD,IAAI,CAACC,GAAG,CAAC,CAAC;IACtB,IAAIA,GAAG,GAAGxD,iBAAiB,CAACE,OAAO,GAAG,GAAG,EAAE;MACvCM,OAAO,CAACC,GAAG,CAAC,sBAAsB,CAAC;MACnC;IACJ;IACAT,iBAAiB,CAACE,OAAO,GAAGsD,GAAG;IAE/B,IAAI;MACA,MAAMvD,MAAM,GAAGH,WAAW,CAACI,OAAO;MAClC,MAAM4E,kBAAkB,GAAG7E,MAAM,CAAC8E,YAAY,CAAC,CAAC;MAEhDvE,OAAO,CAACC,GAAG,CAAC,sCAAsCqE,kBAAkB,EAAE,CAAC;;MAEvE;MACA,IAAIA,kBAAkB,EAAE;QACpBtE,OAAO,CAACC,GAAG,CAAC,kBAAkB,CAAC;QAC/BR,MAAM,CAACqB,KAAK,CAAC,CAAC;QACdzC,YAAY,CAAC,KAAK,CAAC;QACnB2B,OAAO,CAACC,GAAG,CAAC,iBAAiB,CAAC;MAClC,CAAC,MAAM;QACHD,OAAO,CAACC,GAAG,CAAC,mBAAmB,CAAC;QAChC;QACA,IAAIR,MAAM,CAAC+E,aAAa,EAAE;UACtB/E,MAAM,CAACsB,IAAI,CAAC,CAAC;UACb1C,YAAY,CAAC,IAAI,CAAC;UAClB2B,OAAO,CAACC,GAAG,CAAC,cAAc,CAAC;QAC/B,CAAC,MAAM;UACHD,OAAO,CAACC,GAAG,CAAC,gCAAgC,CAAC;UAC7C;UACAgE,iBAAiB,CAAC/F,YAAY,CAAC;QACnC;MACJ;IACJ,CAAC,CAAC,OAAOe,KAAK,EAAE;MACZe,OAAO,CAACf,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC5C;MACAZ,YAAY,CAAC,KAAK,CAAC;IACvB;EACJ,CAAC,EAAE,CAACD,SAAS,EAAEF,YAAY,EAAEa,SAAS,EAAEkF,iBAAiB,CAAC,CAAC;;EAE3D;EACA,MAAMQ,mBAAmB,GAAG9I,WAAW,CAAC,OAAMuI,KAAK,EAAEQ,QAAQ,KAAK;IAC9D,IAAI,CAACR,KAAK,IAAI,OAAOQ,QAAQ,KAAK,QAAQ,IAAIA,QAAQ,GAAG,CAAC,IAAIA,QAAQ,GAAG,CAAC,EAAE;MACxE1E,OAAO,CAACf,KAAK,CAAC,mCAAmC,CAAC;MAClD;IACJ;;IAEA;IACA,MAAM+D,GAAG,GAAGD,IAAI,CAACC,GAAG,CAAC,CAAC;IACtB,IAAIA,GAAG,GAAGzD,aAAa,CAACG,OAAO,GAAG,GAAG,EAAE;MACnCM,OAAO,CAACC,GAAG,CAAC,iBAAiB,CAAC;MAC9B;IACJ;IACAV,aAAa,CAACG,OAAO,GAAGsD,GAAG;IAE3BhD,OAAO,CAACC,GAAG,CAAC,qBAAqBiE,KAAK,CAAChC,IAAI,gBAAgBwC,QAAQ,CAACC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC;IAEjF,IAAI;MACA;MACA,IAAIT,KAAK,CAACpB,EAAE,MAAM5E,YAAY,IAAIA,YAAY,CAAC4E,EAAE,CAAC,EAAE;QAChD,MAAMmB,iBAAiB,CAACC,KAAK,CAAC;QAC9B;QACA,MAAM,IAAIU,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,GAAG,CAAC,CAAC;MAC1D;;MAEA;MACA,MAAMpF,MAAM,GAAGH,WAAW,CAACI,OAAO;MAClC,IAAI,CAACD,MAAM,CAAC+E,aAAa,EAAE;QACvBxE,OAAO,CAAC+E,IAAI,CAAC,+BAA+B,CAAC;QAC7C;MACJ;;MAEA;MACA,MAAMC,aAAa,GAAGxG,YAAY,CAAC0F,KAAK,CAACpB,EAAE,CAAC;MAC5C,MAAMmC,aAAa,GAAGD,aAAa,IAAIA,aAAa,CAACpG,QAAQ,IAAIa,MAAM,CAACyF,WAAW,CAAC,CAAC,IAAI,CAAC;MAE1F,IAAID,aAAa,GAAG,CAAC,EAAE;QACnB,MAAME,QAAQ,GAAGlC,IAAI,CAACmC,GAAG,CAAC,CAAC,EAAEnC,IAAI,CAACoC,GAAG,CAACJ,aAAa,EAAEP,QAAQ,GAAGO,aAAa,CAAC,CAAC;QAC/EjF,OAAO,CAACC,GAAG,CAAC,eAAekF,QAAQ,CAACR,OAAO,CAAC,CAAC,CAAC,QAAQM,aAAa,CAACN,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC;;QAElF;QACA,MAAMW,UAAU,GAAG7F,MAAM,CAAC8E,YAAY,CAAC,CAAC;;QAExC;QACA9E,MAAM,CAAC8F,IAAI,CAACJ,QAAQ,CAAC;QACrBxG,cAAc,CAACwG,QAAQ,CAAC;;QAExB;QACA,IAAIG,UAAU,EAAE;UACZ;UACAR,UAAU,CAAC,MAAM;YACb,IAAIrF,MAAM,CAAC8E,YAAY,CAAC,CAAC,EAAE;cACvBlG,YAAY,CAAC,IAAI,CAAC;YACtB;UACJ,CAAC,EAAE,EAAE,CAAC;QACV,CAAC,MAAM;UACH;UACAA,YAAY,CAAC,KAAK,CAAC;QACvB;;QAEA;QACAyG,UAAU,CAACU,uBAAuB,EAAE,GAAG,CAAC;MAC5C,CAAC,MAAM;QACHxF,OAAO,CAAC+E,IAAI,CAAC,mCAAmC,CAAC;MACrD;IACJ,CAAC,CAAC,OAAO9F,KAAK,EAAE;MACZe,OAAO,CAACf,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;IACpD;EACJ,CAAC,EAAE,CAACf,YAAY,EAAEM,YAAY,EAAEyF,iBAAiB,EAAE7F,SAAS,CAAC,CAAC;;EAE9D;EACA,MAAMqH,iBAAiB,GAAG9J,WAAW,CAAE+J,aAAa,IAAK;IACrD1F,OAAO,CAACC,GAAG,CAAC,mBAAmByF,aAAa,CAACxD,IAAI,EAAE,CAAC;;IAEpD;IACA,IAAIhE,YAAY,IAAIA,YAAY,CAAC4E,EAAE,KAAK4C,aAAa,CAAC5C,EAAE,EAAE;MACtDxD,WAAW,CAACI,OAAO,CAACyE,IAAI,CAAC,CAAC;MAC1B9F,YAAY,CAAC,KAAK,CAAC;MACnBF,eAAe,CAAC,IAAI,CAAC;MACrBQ,cAAc,CAAC,CAAC,CAAC;MACjBE,WAAW,CAAC,CAAC,CAAC;IAClB;;IAEA;IACAZ,aAAa,CAAC4F,IAAI,IAAIA,IAAI,CAAC8B,MAAM,CAACzB,KAAK,IAAIA,KAAK,CAACpB,EAAE,KAAK4C,aAAa,CAAC5C,EAAE,CAAC,CAAC;;IAE1E;IACArE,eAAe,CAACoF,IAAI,IAAI;MACpB,MAAM+B,OAAO,GAAG;QAAC,GAAG/B;MAAK,CAAC;MAC1B,OAAO+B,OAAO,CAACF,aAAa,CAAC5C,EAAE,CAAC;MAChC,OAAO8C,OAAO;IAClB,CAAC,CAAC;EACN,CAAC,EAAE,CAAC1H,YAAY,CAAC,CAAC;EAElB,oBAAS7B,OAAA,CACLC,YAAY;IAAAuJ,QAAA,gBACZxJ,OAAA,CACAI,MAAM;MAAAoJ,QAAA,gBACNxJ,OAAA,CACAM,KAAK;QAAAkJ,QAAA,EAAE;MAAc;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,KAAC,eAAA5J,OAAA;QAC3B6J,KAAK,EACL;UAAEC,QAAQ,EAAE,MAAM;UAAEC,OAAO,EAAE;QAAI,CACpC;QAAAP,QAAA,EAAE;MAC4B;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3B,CAAC,KAAC,EACNhH,KAAK,iBAAI5C,OAAA;QAAM6J,KAAK,EAChB;UAAEvC,KAAK,EAAE,SAAS;UAAEwC,QAAQ,EAAE,MAAM;UAAEE,SAAS,EAAE;QAAM,CAC1D;QAAAR,QAAA,GAAE,eAAG,EAAE5G,KAAK,EAAE,GAAC;MAAA;QAAA6G,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,EAAC,GAAC,EACzBlH,SAAS,iBAAI1C,OAAA;QAAM6J,KAAK,EACpB;UAAEvC,KAAK,EAAE,SAAS;UAAEwC,QAAQ,EAAE,MAAM;UAAEE,SAAS,EAAE;QAAM,CAC1D;QAAAR,QAAA,GAAE,SAAE,EAAE1G,cAAc,EAAE,GAAC;MAAA;QAAA2G,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,EAAC,GAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC1B,CAAC,eAER5J,OAAA,CACAS,WAAW;MAAA+I,QAAA,gBACXxJ,OAAA,CACAW,iBAAiB;QAAA6I,QAAA,GAAE,GAAC,EAChB7H,UAAU,CAACuD,MAAM,KAAK,CAAC,gBAAKlF,OAAA,CACxBJ,YAAY;UAACqK,YAAY,EAAKlE,gBAAkB;UAChDmE,QAAQ,EAAK,KAAO;UAAAV,QAAA,gBACpBxJ,OAAA,CACAe,UAAU;YAAAyI,QAAA,gBACVxJ,OAAA,CACAiB,cAAc;cAAAuI,QAAA,EAAE;YAAI;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAiB,CAAC,KAAC,eAAA5J,OAAA;cAAAwJ,QAAA,EACnC;YAAuB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,KAAC,eAAA5J,OAAA;cAAAwJ,QAAA,EAC/B;YAAmD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,KAAC,eAAA5J,OAAA;cAC1D6J,KAAK,EACH;gBAAEC,QAAQ,EAAE,MAAM;gBAAEC,OAAO,EAAE;cAAI,CACpC;cAAAP,QAAA,EAAE;YACiC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClC,CAAC,KAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACO,CAAC,KAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,gBACZ5J,OAAA,CACEmB,eAAe;UAAAqI,QAAA,GAAE,GAAC,EACd7H,UAAU,CAAC0F,GAAG,CAAC,CAACQ,KAAK,EAAEsC,KAAK,kBAAOnK,OAAA,CAC/BN,cAAc;YACdmI,KAAK,EAAKA,KAAO;YACjBuC,QAAQ,EAAKvI,YAAY,IAAIA,YAAY,CAAC4E,EAAE,KAAKoB,KAAK,CAACpB,EAAI;YAC3D1E,SAAS,EAAKA,SAAS,IAAIF,YAAY,IAAIA,YAAY,CAAC4E,EAAE,KAAKoB,KAAK,CAACpB,EAAI;YACzE4D,aAAa,EAAKzC,iBAAmB;YACrC0C,eAAe,EAAKlC,mBAAqB;YACzCmC,aAAa,EAAKnB,iBAAmB;YACrCoB,WAAW,EAAKxC,eAAiB;YACjC7F,YAAY,EAAKA,YAAY,CAAC0F,KAAK,CAACpB,EAAE,CAAG;YACzCpE,WAAW,EAAKR,YAAY,IAAIA,YAAY,CAAC4E,EAAE,KAAKoB,KAAK,CAACpB,EAAE,GAAGpE,WAAW,GAAG,CAAG;YAChFE,QAAQ,EAAKV,YAAY,IAAIA,YAAY,CAAC4E,EAAE,KAAKoB,KAAK,CAACpB,EAAE,GAAGlE,QAAQ,GAAIJ,YAAY,CAAC0F,KAAK,CAACpB,EAAE,CAAC,IAAItE,YAAY,CAAC0F,KAAK,CAACpB,EAAE,CAAC,CAAClE,QAAQ,IAAI;UAAI,GAVlHsF,KAAK,CAACpB,EAAE;YAAAgD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAW9B,CACJ,CAAC,EACL,GAAC,eAAA5J,OAAA,CACFqB,cAAc;YAACE,YAAY,EAAKA,YAAc;YAAAiI,QAAA,GAAE,GAAC,EAC7CjI,YAAY,iBAAIvB,OAAA;cAAAwJ,QAAA,EAAM;YAAwB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,EAAC,GAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvC,CAAC,KAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CACnB,EACJ,GAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACgB,CAAC,eAEnB5J,OAAA,CACAa,SAAS;QAAA2I,QAAA,gBACTxJ,OAAA,CACAL,YAAY;UAChBkC,YAAY,EAAKA,YAAc;UAC/BE,SAAS,EAAKA,SAAW;UACzBE,YAAY,EAAKA,YAAc;UAC/BuI,WAAW,EAAKxC,eAAiB;UACjCyC,cAAc,EAAKvI,eAAiB;UACpC+H,YAAY,EAAKlE,gBAAkB;UACnC5D,YAAY,EAAKN,YAAY,GAAGM,YAAY,CAACN,YAAY,CAAC4E,EAAE,CAAC,GAAG,IAAM;UACtEpE,WAAW,EAAKA,WAAa;UAC7BE,QAAQ,EAAKA;QAAU;UAAAkH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtB,CAAC,KAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACO,CAAC,KAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,KAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACD,CAAC;AAEtB;AAAClI,EAAA,CA3eID,GAAG;AAAAiJ,GAAA,GAAHjJ,GAAG;AA6eR,eAAeA,GAAG;AAAC,IAAAtB,EAAA,EAAAE,GAAA,EAAAG,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAI,GAAA,EAAAkJ,GAAA;AAAAC,YAAA,CAAAxK,EAAA;AAAAwK,YAAA,CAAAtK,GAAA;AAAAsK,YAAA,CAAAnK,GAAA;AAAAmK,YAAA,CAAAjK,GAAA;AAAAiK,YAAA,CAAA/J,GAAA;AAAA+J,YAAA,CAAA7J,GAAA;AAAA6J,YAAA,CAAA3J,GAAA;AAAA2J,YAAA,CAAAzJ,GAAA;AAAAyJ,YAAA,CAAAvJ,GAAA;AAAAuJ,YAAA,CAAAnJ,GAAA;AAAAmJ,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}