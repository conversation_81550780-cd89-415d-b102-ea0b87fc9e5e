import React, { useState, useCallback, useRef, useEffect } from 'react';
import styled from 'styled-components';
import WaveformPlayer from './components/WaveformPlayer';
import ControlPanel from './components/ControlPanel';
import FileDropZone from './components/FileDropZone';
import AudioAnalyzer from './audio/AudioAnalyzer';
import AudioEngine from './audio/AudioEngine';

const AppContainer = styled.div `
  display: flex;
  flex-direction: column;
  height: 100vh;
  background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
  color: #ffffff;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
`;

const Header = styled.div `
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 30px;
  background: rgba(0, 0, 0, 0.3);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
`;

const Title = styled.h1 `
  margin: 0;
  font-size: 24px;
  font-weight: 300;
  color: #4CAF50;
`;

const MainContent = styled.div `
  display: flex;
  flex: 1;
  overflow: hidden;
`;

const WaveformContainer = styled.div `
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 20px;
  overflow-y: auto;
  position: relative;
`;

const SidePanel = styled.div `
  width: 350px;
  background: rgba(0, 0, 0, 0.4);
  border-left: 1px solid rgba(255, 255, 255, 0.1);
  display: flex;
  flex-direction: column;
`;

const EmptyState = styled.div `
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: rgba(255, 255, 255, 0.6);
  text-align: center;
`;

const EmptyStateIcon = styled.div `
  font-size: 64px;
  margin-bottom: 20px;
  opacity: 0.3;
`;

const TracksContainer = styled.div `
  position: relative;
`;

const GlobalDropZone = styled.div `
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  pointer-events: ${props => props.isDragActive ? 'auto' : 'none'};
  background: ${props => props.isDragActive ? 'rgba(76, 175, 80, 0.1)' : 'transparent'};
  border: 2px dashed ${props => props.isDragActive ? '#4CAF50' : 'transparent'};
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  
  h2 {
    color: #4CAF50;
    font-size: 24px;
    text-shadow: 0 2px 4px rgba(0,0,0,0.3);
    margin: 0;
  }
`;

function App() {
    const [audioFiles, setAudioFiles] = useState([]);
    const [currentTrack, setCurrentTrack] = useState(null);
    const [isPlaying, setIsPlaying] = useState(false);
    const [masterVolume, setMasterVolume] = useState(0.8);
    const [analysisData, setAnalysisData] = useState({});
    const [currentTime, setCurrentTime] = useState(0);
    const [duration, setDuration] = useState(0);
    const [isDragActive, setIsDragActive] = useState(false);
    const [isLoading, setIsLoading] = useState(false);
    const [error, setError] = useState(null);
    const [loadingMessage, setLoadingMessage] = useState('');

    const audioAnalyzer = useRef(new AudioAnalyzer());
    const audioEngine = useRef(new AudioEngine());
    const lastClickTime = useRef(0);
    const lastPlayPauseTime = useRef(0);

    // REMOVED: validateAndRecoverState function - was causing performance issues
    // Audio engine event handlers below provide proper state synchronization

    // REMOVED: Performance-killing validation loop
    // The audio engine event handlers below provide proper state sync

    // Set up audio engine event handlers
    useEffect(() => {
        const engine = audioEngine.current;

        engine.onTimeUpdate = (time, dur) => {
            // Validate the time values before setting state
            if (typeof time === 'number' && !isNaN(time) && time >= 0) {
                setCurrentTime(time);
            }
            if (typeof dur === 'number' && !isNaN(dur) && dur > 0) {
                setDuration(dur);
            }
        };

        engine.onEnded = () => {
            console.log('Audio ended');
            setIsPlaying(false);
            setCurrentTime(0);
        };

        engine.onError = (error) => {
            console.error('Audio engine error:', error);
            setIsPlaying(false);
            setIsLoading(false);
            setError(`Audio error: ${error.message || 'Unknown error'}`);
            setLoadingMessage('');
        };

        engine.onLoadStart = () => {
            console.log('Audio load started');
            setIsLoading(true);
            setLoadingMessage('Loading audio file...');
            setError(null);
        };

        engine.onLoadEnd = () => {
            console.log('Audio load ended');
            setIsLoading(false);
            setLoadingMessage('');
        };

        // Set initial volume
        engine.setVolume(masterVolume);

        return () => {
            engine.dispose();
        };
    }, []);

    // Update volume when masterVolume changes
    useEffect(() => {
        audioEngine.current.setVolume(masterVolume);
    }, [masterVolume]);

    // Add spacebar play/pause functionality
    useEffect(() => {
        const handleKeyDown = (e) => {
            if (e.code === 'Space' && e.target.tagName !== 'INPUT' && e.target.tagName !== 'TEXTAREA') {
                e.preventDefault();
                if (currentTrack) {
                    if (isPlaying) {
                        audioEngine.current.pause();
                        setIsPlaying(false);
                    } else {
                        audioEngine.current.play();
                        setIsPlaying(true);
                    }
                }
            }
        };

        document.addEventListener('keydown', handleKeyDown);
        return () => {
            document.removeEventListener('keydown', handleKeyDown);
        };
    }, [currentTrack, isPlaying]);

    // Global drag and drop handlers
    useEffect(() => {
        const handleDragEnter = (e) => {
            e.preventDefault();
            e.stopPropagation();
            if (e.dataTransfer.items && e.dataTransfer.items.length > 0) {
                setIsDragActive(true);
            }
        };

        const handleDragLeave = (e) => {
            e.preventDefault();
            e.stopPropagation();
            if (!e.currentTarget.contains(e.relatedTarget)) {
                setIsDragActive(false);
            }
        };

        const handleDragOver = (e) => {
            e.preventDefault();
            e.stopPropagation();
        };

        const handleDrop = async(e) => {
            e.preventDefault();
            e.stopPropagation();
            setIsDragActive(false);

            const files = [];
            if (e.dataTransfer.files && e.dataTransfer.files.length > 0) {
                for (let i = 0; i < e.dataTransfer.files.length; i++) {
                    const file = e.dataTransfer.files[i];
                    if (isAudioFile(file.name)) {
                        files.push(file);
                    }
                }
            }

            if (files.length > 0) {
                handleFilesAdded(files);
            }
        };

        document.addEventListener('dragenter', handleDragEnter);
        document.addEventListener('dragleave', handleDragLeave);
        document.addEventListener('dragover', handleDragOver);
        document.addEventListener('drop', handleDrop);

        return () => {
            document.removeEventListener('dragenter', handleDragEnter);
            document.removeEventListener('dragleave', handleDragLeave);
            document.removeEventListener('dragover', handleDragOver);
            document.removeEventListener('drop', handleDrop);
        };
    }, []);

    const isAudioFile = (filename) => {
        const audioExtensions = ['.wav', '.flac', '.mp3', '.aac', '.ogg', '.m4a', '.wma'];
        const ext = filename.toLowerCase().substring(filename.lastIndexOf('.'));
        return audioExtensions.includes(ext);
    };

    // Handle file loading - RESTORED WITH REAL ANALYSIS
    const handleFilesAdded = useCallback(async(files) => {
        console.log('handleFilesAdded called with:', files);
        setError(null);
        setLoadingMessage('Processing files...');
        const newFiles = [];

        try {
            for (const file of files) {
                console.log('Processing file:', file);
                try {
                    // Determine file name and path based on file type
                    let fileName, filePath;

                    if (file instanceof File) {
                        // Browser File object from drag & drop
                        fileName = file.name;
                        filePath = file; // Keep the File object for browser processing
                    } else if (typeof file === 'string') {
                        // File path string from Electron
                        fileName = file.split('/').pop().split('\\').pop(); // Handle both / and \ separators
                        filePath = file;
                    } else {
                        // Fallback
                        fileName = 'Unknown File';
                        filePath = file;
                    }

                    // Analyze the audio file
                    console.log('Starting analysis for:', fileName);
                    setLoadingMessage(`Analyzing ${fileName}...`);
                    const analysis = await audioAnalyzer.current.analyzeFile(filePath);
                    console.log('Analysis completed for:', fileName, analysis);

                    const audioFile = {
                        id: Date.now() + Math.random(),
                        name: fileName,
                        path: filePath,
                        analysis,
                        color: generateRandomColor(),
                        waveformData: analysis.waveformData || null
                    };

                    console.log('Created audioFile object:', audioFile);
                    newFiles.push(audioFile);
                    console.log('Added to newFiles, total:', newFiles.length);

                    // Store analysis data
                    setAnalysisData(prev => ({
                        ...prev,
                        [audioFile.id]: analysis
                    }));

                } catch (error) {
                    console.error('Error analyzing file:', error);

                    // Create a basic file entry even if analysis fails
                    let fileName;
                    if (file instanceof File) {
                        fileName = file.name;
                    } else if (typeof file === 'string') {
                        fileName = file.split('/').pop().split('\\').pop();
                    } else {
                        fileName = 'Unknown File';
                    }

                    const audioFile = {
                        id: Date.now() + Math.random(),
                        name: fileName,
                        path: file,
                        analysis: null,
                        color: generateRandomColor(),
                        waveformData: null
                    };
                    newFiles.push(audioFile);
                }
            }

            console.log('About to update audioFiles state with:', newFiles);
            setAudioFiles(prev => {
                const updated = [...prev, ...newFiles];
                console.log('Updated audioFiles state:', updated);
                return updated;
            });
            setLoadingMessage('');
            console.log('Files added successfully:', newFiles.length);

            // Auto-select first track if none selected
            if (!currentTrack && newFiles.length > 0) {
                setCurrentTrack(newFiles[0]);
            }
        } catch (outerError) {
            console.error('Critical error in handleFilesAdded:', outerError);
            setError(`Failed to process files: ${outerError.message}`);
            setLoadingMessage('');
        }
    }, [currentTrack]);



    // Generate random colors for waveforms
    const generateRandomColor = () => {
        const colors = [
            '#4CAF50', '#2196F3', '#FF9800', '#E91E63',
            '#9C27B0', '#00BCD4', '#FFEB3B', '#FF5722'
        ];
        return colors[Math.floor(Math.random() * colors.length)];
    };

    // Handle track selection
    const handleTrackSelect = useCallback(async(track) => {
        if (track.id === (currentTrack && currentTrack.id)) {
            console.log('Track already selected');
            return;
        }

        if (isLoading) {
            console.log('Already loading a track, ignoring click');
            return;
        }

        try {
            console.log(`Loading track: ${track.name}`);
            setIsLoading(true);

            // Stop current playback
            audioEngine.current.stop();
            setIsPlaying(false);

            // Load new track
            await audioEngine.current.loadAudioFile(track.path);
            setCurrentTrack(track);
            setCurrentTime(0);

            console.log(`Track loaded successfully: ${track.name}`);
        } catch (error) {
            console.error('Error loading track:', error);
            // Reset state on error
            setCurrentTrack(null);
            setIsPlaying(false);
            setCurrentTime(0);
        } finally {
            setIsLoading(false);
        }
    }, [currentTrack, isLoading]);

    // Handle playback control
    const handlePlayPause = useCallback(() => {
        if (!currentTrack) {
            console.log('No current track selected');
            return;
        }

        if (isLoading) {
            console.log('Track is loading, cannot play/pause');
            return;
        }

        // Debounce rapid play/pause clicks
        const now = Date.now();
        if (now - lastPlayPauseTime.current < 200) {
            console.log('Play/pause debounced');
            return;
        }
        lastPlayPauseTime.current = now;

        try {
            const engine = audioEngine.current;
            const currentEngineState = engine.getIsPlaying();

            console.log(`Play/Pause clicked - Engine state: ${currentEngineState}`);

            // Use ONLY the engine state as the source of truth
            if (currentEngineState) {
                console.log('Pausing playback');
                engine.pause();
                setIsPlaying(false);
                console.log('Pause completed');
            } else {
                console.log('Starting playback');
                // Ensure the track is loaded before playing
                if (engine.currentBuffer) {
                    engine.play();
                    setIsPlaying(true);
                    console.log('Play started');
                } else {
                    console.log('Track not loaded, reloading...');
                    // Reload the track if it's not loaded
                    handleTrackSelect(currentTrack);
                }
            }
        } catch (error) {
            console.error('Error in play/pause:', error);
            // Reset state on error
            setIsPlaying(false);
        }
    }, [isPlaying, currentTrack, isLoading, handleTrackSelect]);

    // Handle waveform click (seek)
    const handleWaveformClick = useCallback(async(track, position) => {
        if (!track || typeof position !== 'number' || position < 0 || position > 1) {
            console.error('Invalid waveform click parameters');
            return;
        }

        // Debounce rapid clicks more aggressively
        const now = Date.now();
        if (now - lastClickTime.current < 300) {
            console.log('Click debounced');
            return;
        }
        lastClickTime.current = now;

        console.log(`Waveform clicked: ${track.name} at position ${position.toFixed(3)}`);

        try {
            // If clicking on a different track, switch to it
            if (track.id !== (currentTrack && currentTrack.id)) {
                await handleTrackSelect(track);
                // Wait for the track to load before seeking
                await new Promise(resolve => setTimeout(resolve, 200));
            }

            // Ensure the audio engine is ready
            const engine = audioEngine.current;
            if (!engine.currentBuffer) {
                console.warn('Audio not loaded, cannot seek');
                return;
            }

            // Seek to position
            const trackAnalysis = analysisData[track.id];
            const trackDuration = trackAnalysis && trackAnalysis.duration || engine.getDuration() || 0;

            if (trackDuration > 0) {
                const seekTime = Math.max(0, Math.min(trackDuration, position * trackDuration));
                console.log(`Seeking to: ${seekTime.toFixed(2)}s of ${trackDuration.toFixed(2)}s`);

                // Store the current playing state before seeking
                const wasPlaying = engine.getIsPlaying();

                // Perform the seek
                engine.seek(seekTime);
                setCurrentTime(seekTime);

                // Only start playing if we were already playing or if explicitly requested
                if (wasPlaying) {
                    // Give a small delay to ensure seek is complete
                    setTimeout(() => {
                        if (engine.getIsPlaying()) {
                            setIsPlaying(true);
                        }
                    }, 50);
                } else {
                    // If we weren't playing, just update the position
                    setIsPlaying(false);
                }

                // Validate state after a longer delay to avoid race conditions
                setTimeout(validateAndRecoverState, 500);
            } else {
                console.warn('No duration available for seeking');
            }
        } catch (error) {
            console.error('Error in waveform click:', error);
        }
    }, [currentTrack, analysisData, handleTrackSelect, isPlaying]);

    // Handle track deletion
    const handleTrackDelete = useCallback((trackToDelete) => {
        console.log(`Deleting track: ${trackToDelete.name}`);

        // Stop playback if deleting current track
        if (currentTrack && currentTrack.id === trackToDelete.id) {
            audioEngine.current.stop();
            setIsPlaying(false);
            setCurrentTrack(null);
            setCurrentTime(0);
            setDuration(0);
        }

        // Remove from audio files list
        setAudioFiles(prev => prev.filter(track => track.id !== trackToDelete.id));

        // Remove from analysis data
        setAnalysisData(prev => {
            const newData = {...prev };
            delete newData[trackToDelete.id];
            return newData;
        });
    }, [currentTrack]);

    return ( <
        AppContainer >
        <
        Header >
        <
        Title > Album Player < /Title> <
        div style = {
            { fontSize: '14px', opacity: 0.7 }
        } >
        Spotify Normalization Analyzer <
        /div> {
        error && < div style = {
            { color: '#ff5722', fontSize: '12px', marginTop: '8px' }
        } > ⚠️{ error } < /div>} {
        isLoading && < div style = {
            { color: '#4CAF50', fontSize: '12px', marginTop: '8px' }
        } > ⏳{ loadingMessage } < /div>} < /
        Header >

        <
        MainContent >
        <
        WaveformContainer > {
            audioFiles.length === 0 ? ( <
                FileDropZone onFilesAdded = { handleFilesAdded }
                hasFiles = { false } >
                <
                EmptyState >
                <
                EmptyStateIcon > 🎵 < /EmptyStateIcon> <
                h2 > Drop Audio Files Here < /h2> <
                p > Drag and drop your audio files to start analyzing < /p> <
                p style = {
                    { fontSize: '12px', opacity: 0.5 }
                } >
                Supported: WAV, FLAC, MP3, AAC, OGG <
                /p> < /
                EmptyState > <
                /FileDropZone>
            ) : ( <
                    TracksContainer > {
                        audioFiles.map((track, index) => ( <
                            WaveformPlayer key = { track.id }
                            track = { track }
                            isActive = { currentTrack && currentTrack.id === track.id }
                            isPlaying = { isPlaying && currentTrack && currentTrack.id === track.id }
                            onTrackSelect = { handleTrackSelect }
                            onWaveformClick = { handleWaveformClick }
                            onTrackDelete = { handleTrackDelete }
                            onPlayPause = { handlePlayPause }
                            analysisData = { analysisData[track.id] }
                            currentTime = { currentTrack && currentTrack.id === track.id ? currentTime : 0 }
                            duration = { currentTrack && currentTrack.id === track.id ? duration : (analysisData[track.id] && analysisData[track.id].duration || 0) }
                            />
                        ))
                    } <
                    GlobalDropZone isDragActive = { isDragActive } > {
                        isDragActive && < h2 > Drop to Add More Files < /h2>} < /
                        GlobalDropZone > <
                        /TracksContainer>
                    )
                } <
                /WaveformContainer>

                <
                SidePanel >
                <
                ControlPanel
            currentTrack = { currentTrack }
            isPlaying = { isPlaying }
            masterVolume = { masterVolume }
            onPlayPause = { handlePlayPause }
            onVolumeChange = { setMasterVolume }
            onFilesAdded = { handleFilesAdded }
            analysisData = { currentTrack ? analysisData[currentTrack.id] : null }
            currentTime = { currentTime }
            duration = { duration }
            /> < /
            SidePanel > <
            /MainContent> < /
            AppContainer >
        );
    }

    export default App;